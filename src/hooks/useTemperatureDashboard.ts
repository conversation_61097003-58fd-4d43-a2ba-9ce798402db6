/**
 * Custom hook for managing temperature dashboard data
 * 
 * Provides centralized state management for:
 * - Real-time sensor data from TempStick API
 * - Temperature trends
 * - Dashboard filters
 * - Auto-refresh functionality
 * - Error handling
 */

import { useState, useEffect, useCallback } from 'react';
import { tempStickService } from '@/lib/tempstick-service';

export interface DashboardFilters {
  timeRange: '1h' | '6h' | '24h' | '7d' | '30d';
  selectedSensors: string[];
  selectedStorageAreas: string[];
  showOfflineSensors: boolean;
  alertsOnly: boolean;
}

interface SensorStatus {
  id: string;
  name: string;
  location: string;
  status: 'online' | 'offline' | 'low_battery';
  temperature: number;
  humidity?: number;
  lastReading: string;
  batteryLevel?: number;
}

interface DashboardSummary {
  totalSensors: number;
  onlineSensors: number;
  offlineSensors: number;
  alertCount: number;
  averageTemperature: number;
}

interface TemperatureTrendData {
  timestamp: string;
  temperature: number;
  sensorId: string;
  sensorName: string;
}

export const useTemperatureDashboard = (options: { 
  autoRefreshInterval?: number;
  enableRealTimeUpdates?: boolean;
  maxTrendDataPoints?: number;
} = {}) => {
  const [sensorStatuses, setSensorStatuses] = useState<SensorStatus[]>([]);
  const [dashboardSummary, setDashboardSummary] = useState<DashboardSummary | null>(null);
  const [temperatureTrends, setTemperatureTrends] = useState<TemperatureTrendData[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [lastUpdate, setLastUpdate] = useState<Date | null>(null);
  const [autoRefresh, setAutoRefresh] = useState(true);
  const [systemHealth, setSystemHealth] = useState<any>(null);
  const [availableStorageAreas, setAvailableStorageAreas] = useState<any[]>([]);
  const [availableSensors, setAvailableSensors] = useState<any[]>([]);
  const [filters, setFilters] = useState<DashboardFilters>({
    timeRange: '24h',
    selectedSensors: [],
    selectedStorageAreas: [],
    showOfflineSensors: true,
    alertsOnly: false,
  });

  const fetchSensorData = useCallback(async () => {
    try {
      setIsLoading(true);
      setError(null);
      
      // Get sensors from TempStick API
      const sensors = await tempStickService.getAllSensors();
      
      // Convert to SensorStatus format
      const sensorStatuses: SensorStatus[] = sensors.map((tempStickSensor: any) => ({
        sensor: {
          id: tempStickSensor.sensor_id,
          tempstick_sensor_id: tempStickSensor.sensor_id,
          name: tempStickSensor.sensor_name,
          location: tempStickSensor.ssid ?? 'Unknown Location',
          sensor_type: 'temperature_humidity' as const,
          temp_min_threshold: null,
          temp_max_threshold: null,
          humidity_min_threshold: null,
          humidity_max_threshold: null,
          storage_area_id: null,
          active: tempStickSensor.offline !== '1',
          created_at: new Date().toISOString(),
        },
        latestReading: tempStickSensor.last_temp !== undefined ? {
          id: `reading-${tempStickSensor.sensor_id}-${Date.now()}`,
          sensor_id: tempStickSensor.sensor_id,
          temperature: tempStickSensor.last_temp,
          humidity: tempStickSensor.last_humidity,
          reading_timestamp: tempStickSensor.last_checkin ?? new Date().toISOString(),
          alert_triggered: false,
          created_at: tempStickSensor.last_checkin ?? new Date().toISOString(),
        } : null,
        status: tempStickSensor.offline === '1' ? 'offline' as const : 'online' as const,
        activeAlerts: [],
        lastSyncTime: tempStickSensor.last_checkin ?? new Date().toISOString(),
        batteryLevel: tempStickSensor.battery_pct,
        signalStrength: parseInt(tempStickSensor.rssi) || 0,
      }));

      setSensorStatuses(sensorStatuses);
      setAvailableSensors(sensorStatuses);
      setLastUpdate(new Date());
      
      // Calculate dashboard summary
      const onlineSensors = sensorStatuses.filter(s => s.status === 'online').length;
      const offlineSensors = sensorStatuses.filter(s => s.status === 'offline').length;
      const averageTemperature = sensorStatuses.length > 0
        ? sensorStatuses.reduce((sum, s) => sum + (s.latestReading?.temperature || 0), 0) / sensorStatuses.length
        : 0;

      setDashboardSummary({
        totalSensors: sensorStatuses.length,
        onlineSensors,
        offlineSensors,
        activeAlerts: 0, // TODO: Implement alert counting
        averageTemperature,
      });

    } catch (err) {
      console.error('Failed to fetch sensor data:', err);
      setError(err instanceof Error ? err.message : 'Failed to fetch sensor data');
    } finally {
      setIsLoading(false);
    }
  }, []);

  const fetchTemperatureTrends = useCallback(async (sensorId?: string) => {
    try {
      if (!sensorId && sensorStatuses.length === 0) return;
      
      const targetSensors = sensorId ? [sensorId] : sensorStatuses.map(s => s.sensor.id);
      const allTrends: TemperatureTrendData[] = [];

      for (const id of targetSensors) {
        try {
          const readings = await tempStickService.getLatestReadings(id, 100);
          const trends = readings.map((reading: any) => ({
            timestamp: reading.timestamp,
            temperature: reading.temperature,
            sensorId: id,
            sensorName: sensorStatuses.find(s => s.sensor.id === id)?.sensor.name ?? `Sensor ${id}`,
          }));
          allTrends.push(...trends);
        } catch (err) {
          console.warn(`Failed to fetch trends for sensor ${id}:`, err);
        }
      }
      
      setTemperatureTrends(allTrends);
    } catch (err) {
      console.error('Failed to fetch temperature trends:', err);
    }
  }, [sensorStatuses]);

  const refreshData = useCallback(async () => {
    await fetchSensorData();
    await fetchTemperatureTrends();
  }, [fetchSensorData, fetchTemperatureTrends]);

  const updateFilters = useCallback((newFilters: Partial<DashboardFilters>) => {
    setFilters(prev => ({ ...prev, ...newFilters }));
  }, []);

  const setFiltersWrapper = useCallback((newFilters: Partial<DashboardFilters>) => {
    setFilters(prev => ({ ...prev, ...newFilters }));
  }, []);

  // Initial data fetch
  useEffect(() => {
    fetchSensorData();
  }, [fetchSensorData]);

  // Auto-refresh interval
  useEffect(() => {
    if (options.autoRefreshInterval && options.autoRefreshInterval > 0) {
      const interval = setInterval(refreshData, options.autoRefreshInterval);
      return () => clearInterval(interval);
    }
  }, [refreshData, options.autoRefreshInterval]);

  return {
    // Data
    sensorStatuses,
    dashboardSummary,
    temperatureTrends,
    availableStorageAreas,
    availableSensors,
    systemHealth,
    lastUpdate,
    
    // State
    loading: isLoading,
    error,
    filters,
    autoRefresh,
    
    // Actions
    refreshData,
    fetchTemperatureTrends,
    updateFilters,
    setAutoRefresh,
    setFilters: setFiltersWrapper,
    
    // Utilities
    clearError: () => setError(null),
  };
};

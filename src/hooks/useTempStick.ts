/**
 * React hooks for TempStick sensor integration
 * Provides real-time temperature monitoring, sensor management,
 * and alert handling capabilities
 */

import { useState, useEffect, useCallback, useMemo } from 'react';
import { supabase } from '../lib/supabase';
import { tempStickService } from '../lib/tempstick-service';
import type {
  Sensor,
  TemperatureReading,
  TemperatureAlert,
  SensorStatus,
  DashboardSummary,
  TemperatureTrendData,
  TimeRange,
  FilterParams,
} from '../types/tempstick';

/**
 * Hook for managing sensor data with real-time updates
 */
export function useSensors() {
  const [sensors, setSensors] = useState<Sensor[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchSensors = useCallback(async () => {
    try {
      setError(null);
      // First get sensors
      const { data: sensorsData, error: sensorsError } = await supabase
        .from('sensors')
        .select('*')
        .order('name');

      if (sensorsError) throw sensorsError;

      // Then get storage areas separately 
      const { data: storageAreasData, error: areasError } = await supabase
        .from('storage_areas')
        .select('id, name, area_type, required_temp_min, required_temp_max, haccp_control_point');

      if (areasError) {
        console.warn('Failed to load storage areas:', areasError);
        // Continue without storage areas data
      }

      // Map storage areas to sensors
      const sensorsWithAreas = (sensorsData || []).map(sensor => {
        const storage_area = storageAreasData?.find(area => area.id === sensor.storage_area_id);
        return {
          ...sensor,
          storage_areas: storage_area || null
        };
      });

      const data = sensorsWithAreas;

      if (fetchError) throw fetchError;
      setSensors(data || []);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch sensors');
    } finally {
      setLoading(false);
    }
  }, []);

  const syncSensors = useCallback(async () => {
    try {
      setError(null);
      await tempStickService.syncSensors();
      await fetchSensors();
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to sync sensors');
    }
  }, [fetchSensors]);

  useEffect(() => {
    fetchSensors();

    // Set up real-time subscription
    const subscription = supabase
      .channel('sensors_changes')
      .on(
        'postgres_changes',
        { event: '*', schema: 'public', table: 'sensors' },
        () => {
          fetchSensors();
        }
      )
      .subscribe();

    return () => {
      subscription.unsubscribe();
    };
  }, [fetchSensors]);

  return {
    sensors,
    loading,
    error,
    syncSensors,
    refetch: fetchSensors,
  };
}

/**
 * Hook for temperature readings with real-time updates
 */
export function useTemperatureReadings(
  sensorIds?: string[],
  timeRange: TimeRange = '24h'
) {
  const [readings, setReadings] = useState<TemperatureReading[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const getDateRange = useCallback((range: TimeRange) => {
    const now = new Date();
    const start = new Date();

    switch (range) {
      case '1h':
        start.setHours(now.getHours() - 1);
        break;
      case '6h':
        start.setHours(now.getHours() - 6);
        break;
      case '24h':
        start.setDate(now.getDate() - 1);
        break;
      case '7d':
        start.setDate(now.getDate() - 7);
        break;
      case '30d':
        start.setDate(now.getDate() - 30);
        break;
      case '90d':
        start.setDate(now.getDate() - 90);
        break;
      default:
        start.setDate(now.getDate() - 1); // Default to 24h
    }

    return { start, end: now };
  }, []);

  const fetchReadings = useCallback(async () => {
    try {
      setError(null);
      const { start, end } = getDateRange(timeRange);
      const data = await tempStickService.getReadingsForDateRange(start, end, sensorIds);
      setReadings(data);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch readings');
    } finally {
      setLoading(false);
    }
  }, [sensorIds, timeRange, getDateRange]);

  useEffect(() => {
    fetchReadings();

    // Set up real-time subscription for new readings
    const subscription = supabase
      .channel('temperature_readings_changes')
      .on(
        'postgres_changes',
        { event: 'INSERT', schema: 'public', table: 'temperature_readings' },
        () => {
          fetchReadings();
        }
      )
      .subscribe();

    return () => {
      subscription.unsubscribe();
    };
  }, [fetchReadings]);

  return {
    readings,
    loading,
    error,
    refetch: fetchReadings,
  };
}

/**
 * Hook for temperature alerts with real-time updates
 */
export function useTemperatureAlerts(activeOnly: boolean = true) {
  const [alerts, setAlerts] = useState<TemperatureAlert[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchAlerts = useCallback(async () => {
    try {
      setError(null);
      let query = supabase
        .from('temperature_alerts')
        .select(`
          *,
          sensors (
            id,
            name,
            location,
            storage_areas (
              id,
              name,
              area_type
            )
          )
        `)
        .order('alert_timestamp', { ascending: false });

      if (activeOnly) {
        query = query.is('resolved_timestamp', null);
      }

      const { data, error: fetchError } = await query;
      if (fetchError) throw fetchError;
      
      setAlerts(data || []);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch alerts');
    } finally {
      setLoading(false);
    }
  }, [activeOnly]);

  const resolveAlert = useCallback(async (alertId: string) => {
    try {
      await tempStickService.resolveAlert(alertId);
      await fetchAlerts();
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to resolve alert');
    }
  }, [fetchAlerts]);

  useEffect(() => {
    fetchAlerts();

    // Set up real-time subscription for alerts
    const subscription = supabase
      .channel('temperature_alerts_changes')
      .on(
        'postgres_changes',
        { event: '*', schema: 'public', table: 'temperature_alerts' },
        () => {
          fetchAlerts();
        }
      )
      .subscribe();

    return () => {
      subscription.unsubscribe();
    };
  }, [fetchAlerts]);

  return {
    alerts,
    loading,
    error,
    resolveAlert,
    refetch: fetchAlerts,
  };
}

/**
 * Hook for sensor status dashboard
 */
export function useSensorStatuses() {
  const { sensors } = useSensors();
  const { readings } = useTemperatureReadings(undefined, '1h');
  const { alerts } = useTemperatureAlerts(true);
  
  const sensorStatuses = useMemo<SensorStatus[]>(() => {
    return sensors.map(sensor => {
      // Find latest reading for this sensor
      const sensorReadings = readings.filter(r => r.sensor_id === sensor.id);
      const latestReading = sensorReadings.length > 0 ? sensorReadings[0] : null;
      
      // Find active alerts for this sensor
      const sensorAlerts = alerts.filter(a => a.sensor_id === sensor.id);
      
      // Determine status
      let status: SensorStatus['status'] = 'offline';
      
      if (latestReading) {
        const readingAge = Date.now() - new Date(latestReading.reading_timestamp).getTime();
        const isRecent = readingAge < 15 * 60 * 1000; // 15 minutes
        
        if (isRecent) {
          const hasCriticalAlerts = sensorAlerts.some(a => a.severity_level === 'critical');
          const hasHighAlerts = sensorAlerts.some(a => a.severity_level === 'high');
          
          if (hasCriticalAlerts) {
            status = 'critical';
          } else if (hasHighAlerts || sensorAlerts.length > 0) {
            status = 'warning';
          } else {
            status = 'online';
          }
        }
      }

      return {
        sensor,
        latestReading,
        status,
        activeAlerts: sensorAlerts,
        lastSyncTime: latestReading?.created_at || sensor.created_at,
      };
    });
  }, [sensors, readings, alerts]);

  const summary = useMemo<DashboardSummary>(() => {
    const totalSensors = sensors.length;
    const onlineSensors = sensorStatuses.filter(s => s.status === 'online').length;
    const activeAlerts = alerts.length;
    const criticalAlerts = alerts.filter(a => a.severity_level === 'critical').length;
    
    const recentReadings = readings.slice(0, 50); // Last 50 readings
    const averageTemperature = recentReadings.length > 0 
      ? recentReadings.reduce((sum, r) => sum + r.temperature, 0) / recentReadings.length
      : null;
    
    const temperatures = recentReadings.map(r => r.temperature);
    const temperatureRange = temperatures.length > 0
      ? {
          min: Math.min(...temperatures),
          max: Math.max(...temperatures)
        }
      : null;

    return {
      totalSensors,
      onlineSensors,
      activeAlerts,
      criticalAlerts,
      averageTemperature,
      temperatureRange,
    };
  }, [sensors, sensorStatuses, alerts, readings]);

  return {
    sensorStatuses,
    summary,
  };
}

/**
 * Hook for temperature trend data for charts
 */
export function useTemperatureTrends(
  sensorIds?: string[],
  timeRange: TimeRange = '24h'
) {
  const { readings } = useTemperatureReadings(sensorIds, timeRange);
  const { sensors } = useSensors();
  const { alerts } = useTemperatureAlerts(false);

  const trendData = useMemo<TemperatureTrendData[]>(() => {
    const sensorMap = new Map(sensors.map(s => [s.id, s]));
    
    return readings.map(reading => {
      const sensor = sensorMap.get(reading.sensor_id);
      
      // Determine alert level for this reading
      const readingTime = new Date(reading.reading_timestamp);
      const readingAlerts = alerts.filter(alert => {
        const alertTime = new Date(alert.alert_timestamp);
        return alert.sensor_id === reading.sensor_id &&
               Math.abs(alertTime.getTime() - readingTime.getTime()) < 60000; // Within 1 minute
      });

      let alertLevel: TemperatureTrendData['alertLevel'] = 'normal';
      if (readingAlerts.some(a => a.severity_level === 'critical')) {
        alertLevel = 'critical';
      } else if (readingAlerts.length > 0) {
        alertLevel = 'warning';
      }

      return {
        timestamp: reading.reading_timestamp,
        temperature: reading.temperature,
        humidity: reading.humidity || undefined,
        sensorName: sensor?.name || `Sensor ${reading.sensor_id}`,
        sensorId: reading.sensor_id,
        alertLevel,
      };
    });
  }, [readings, sensors, alerts]);

  return trendData;
}

/**
 * Hook for periodic data sync
 */
export function useTemperatureSync() {
  const [syncing, setSyncing] = useState(false);
  const [lastSyncTime, setLastSyncTime] = useState<Date | null>(null);
  const [error, setError] = useState<string | null>(null);

  const sync = useCallback(async () => {
    try {
      setSyncing(true);
      setError(null);
      
      await tempStickService.scheduledSync();
      setLastSyncTime(new Date());
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Sync failed');
    } finally {
      setSyncing(false);
    }
  }, []);

  // Auto-sync every 5 minutes
  useEffect(() => {
    const interval = setInterval(sync, 5 * 60 * 1000);
    
    // Initial sync
    sync();

    return () => clearInterval(interval);
  }, [sync]);

  return {
    sync,
    syncing,
    lastSyncTime,
    error,
  };
}

/**
 * Hook for filtering and searching readings/alerts
 */
export function useTemperatureFilters(initialFilters: FilterParams = {}) {
  const [filters, setFilters] = useState<FilterParams>(initialFilters);

  const updateFilter = useCallback((key: keyof FilterParams, value: any) => {
    setFilters(prev => ({
      ...prev,
      [key]: value,
    }));
  }, []);

  const clearFilters = useCallback(() => {
    setFilters({});
  }, []);

  const applyFilters = useCallback(<T extends { sensor_id?: string; alert_timestamp?: string; temperature?: number }>(
    items: T[]
  ): T[] => {
    return items.filter(item => {
      // Sensor filter
      if (filters.sensorIds?.length && item.sensor_id && !filters.sensorIds.includes(item.sensor_id)) {
        return false;
      }

      // Date range filter
      if (filters.dateRange && item.alert_timestamp) {
        const itemDate = new Date(item.alert_timestamp);
        const start = new Date(filters.dateRange.start);
        const end = new Date(filters.dateRange.end);
        if (itemDate < start || itemDate > end) {
          return false;
        }
      }

      // Temperature range filter
      if (filters.temperatureRange && item.temperature) {
        if (item.temperature < filters.temperatureRange.min || item.temperature > filters.temperatureRange.max) {
          return false;
        }
      }

      return true;
    });
  }, [filters]);

  return {
    filters,
    updateFilter,
    clearFilters,
    applyFilters,
  };
}
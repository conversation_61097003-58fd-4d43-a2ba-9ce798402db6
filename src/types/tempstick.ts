/**
 * TypeScript type definitions for TempStick API integration
 * and temperature monitoring system in the Seafood Manager
 */

/**
 * TempStick API Response Types
 */
export interface TempStickApiResponse<T = unknown> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

/**
 * TempStick Sensor from API (matches real API response structure)
 */
export interface TempStickSensor {
  id: string;
  sensor_id: string;
  sensor_name: string;
  sensor_mac_addr: string;
  version: string;
  owner_id: string;
  type: string;
  alert_interval: string;
  send_interval: string;
  last_temp: number;
  last_humidity: number;
  last_voltage: string;
  wifi_connect_time: string;
  rssi: string;
  last_checkin: string;
  next_checkin: string;
  ssid: string;
  offline: string;
  use_sensor_settings: number;
  temp_offset: string | number;
  humidity_offset: string | number;
  alert_temp_below: string;
  alert_temp_above: string;
  alert_humidity_below: string;
  alert_humidity_above: string;
  connection_sensitivity: string;
  group: number;
  wlanA: string;
  wlanB: string;
  use_alert_interval: string;
  battery_pct: number;
}

/**
 * TempStick Reading from API
 */
export interface TempStickReading {
  temperature: number;
  humidity: number;
  timestamp: string;
  sensor_id: string;
  battery_level?: number;
  signal_strength?: number;
}

/**
 * Database Schema Types
 */

/**
 * Storage Areas table
 */
export interface StorageArea {
  id: string;
  name: string;
  area_type: 'freezer' | 'refrigerator' | 'dry_storage' | 'processing' | 'receiving' | 'other';
  required_temp_min: number | null;
  required_temp_max: number | null;
  haccp_control_point: boolean;
  description?: string | null;
  created_at: string;
}

/**
 * Sensors table
 */
export interface Sensor {
  id: string;
  tempstick_sensor_id: string;
  name: string;
  location: string;
  sensor_type: 'temperature_humidity' | 'temperature_only' | 'humidity_only';
  installation_date?: string | null;
  calibration_date?: string | null;
  temp_min_threshold: number | null;
  temp_max_threshold: number | null;
  humidity_min_threshold?: number | null;
  humidity_max_threshold?: number | null;
  storage_area_id: string | null;
  active: boolean;
  created_at: string;
  
  // Relations
  storage_areas?: StorageArea | null;
}

/**
 * Temperature Readings table
 */
export interface TemperatureReading {
  id: string;
  sensor_id: string;
  temperature: number;
  humidity: number | null;
  reading_timestamp: string;
  alert_triggered: boolean;
  created_at: string;
  
  // Relations
  sensors?: Sensor;
}

/**
 * Temperature Alerts table
 */
export interface TemperatureAlert {
  id: string;
  sensor_id: string;
  alert_type: 'high_temp' | 'low_temp' | 'high_humidity' | 'low_humidity' | 'sensor_offline' | 'haccp_violation';
  temperature: number | null;
  humidity: number | null;
  alert_timestamp: string;
  resolved_timestamp: string | null;
  severity_level: 'low' | 'medium' | 'high' | 'critical';
  notification_sent: boolean;
  notes?: string | null;
  created_at: string;
  
  // Relations
  sensors?: Sensor;
}

/**
 * Product Storage Requirements table (for HACCP compliance)
 */
export interface ProductStorageRequirement {
  id: string;
  product_category: string;
  product_name?: string | null;
  min_temperature: number | null;
  max_temperature: number | null;
  max_humidity?: number | null;
  storage_duration_hours?: number | null;
  haccp_critical: boolean;
  notes?: string | null;
  created_at: string;
}

/**
 * Temperature Events table (integration with inventory)
 */
export interface TemperatureEvent {
  id: string;
  inventory_event_id?: string | null;
  sensor_id: string;
  event_type: 'receiving' | 'storage_change' | 'violation_detected' | 'corrective_action';
  temperature: number;
  humidity?: number | null;
  event_timestamp: string;
  notes?: string | null;
  created_at: string;
  
  // Relations
  sensors?: Sensor;
}

/**
 * Dashboard and UI Types
 */

/**
 * Simple alert for dashboard display
 */
export interface SimpleAlert {
  id: string;
  type: string;
  severity: 'low' | 'warning' | 'critical';
  message: string;
  timestamp: string;
}

/**
 * Sensor status for dashboard display
 */
export interface SensorStatus {
  sensor: Sensor;
  latestReading: TemperatureReading | null;
  status: 'online' | 'offline' | 'warning' | 'critical';
  activeAlerts: SimpleAlert[];
  lastSyncTime: string;
  batteryLevel?: number;
  signalStrength?: number;
}

/**
 * Dashboard summary statistics
 */
export interface DashboardSummary {
  totalSensors: number;
  onlineSensors: number;
  activeAlerts: number;
  criticalAlerts: number;
  averageTemperature: number | null;
  temperatureRange: {
    min: number;
    max: number;
  } | null;
}

/**
 * Temperature trend data for charts
 */
export interface TemperatureTrendData {
  timestamp: string;
  temperature: number;
  humidity?: number;
  sensorName: string;
  sensorId: string;
  alertLevel?: 'normal' | 'warning' | 'critical';
}

/**
 * Alert notification data
 */
export interface AlertNotification {
  alert: TemperatureAlert;
  sensor: Sensor;
  storageArea?: StorageArea;
  message: string;
  actions: {
    label: string;
    action: () => void;
  }[];
}

/**
 * Report Generation Types
 */

/**
 * Temperature report parameters
 */
export interface TemperatureReportParams {
  startDate: Date;
  endDate: Date;
  sensorIds?: string[];
  storageAreaIds?: string[];
  includeAlerts: boolean;
  includeCharts: boolean;
  includeHACCPData: boolean;
  format: 'pdf' | 'excel' | 'csv';
}

/**
 * Report data structure
 */
export interface TemperatureReportData {
  summary: {
    reportPeriod: {
      start: string;
      end: string;
    };
    sensorsIncluded: number;
    totalReadings: number;
    totalAlerts: number;
    complianceRate: number;
  };
  sensorData: {
    sensor: Sensor;
    readings: TemperatureReading[];
    alerts: TemperatureAlert[];
    statistics: {
      avgTemp: number;
      minTemp: number;
      maxTemp: number;
      readingsCount: number;
      alertsCount: number;
    };
  }[];
  alerts: TemperatureAlert[];
  haccpCompliance: {
    storageArea: StorageArea;
    violationsCount: number;
    complianceRate: number;
    criticalViolations: TemperatureAlert[];
  }[];
}

/**
 * Email report configuration
 */
export interface EmailReportConfig {
  frequency: 'daily' | 'weekly' | 'monthly';
  recipients: string[];
  sensorIds: string[];
  alertsOnly: boolean;
  includePDF: boolean;
  includeExcel: boolean;
  timeOfDay: string; // HH:mm format
}

/**
 * Export configuration for external systems
 */
export interface ExportConfig {
  type: 'google_sheets' | 'excel' | 'csv';
  destination?: string; // Spreadsheet ID for Google Sheets, file path for others
  schedule?: {
    frequency: 'realtime' | 'hourly' | 'daily';
    lastExport?: string;
  };
  columns: string[]; // Which columns to include
  filters?: {
    sensorIds?: string[];
    dateRange?: {
      start: Date;
      end: Date;
    };
    alertsOnly?: boolean;
  };
}

/**
 * API Request/Response Types
 */

/**
 * Sync request parameters
 */
export interface SyncParams {
  sensorIds?: string[];
  since?: string; // ISO timestamp
  force?: boolean; // Force full sync
}

/**
 * Sync response data
 */
export interface SyncResponse {
  success: boolean;
  syncedSensors: number;
  newReadings: number;
  newAlerts: number;
  errors: string[];
  lastSyncTime: string;
}

/**
 * Alert configuration
 */
export interface AlertConfig {
  sensorId: string;
  thresholds: {
    tempMin?: number;
    tempMax?: number;
    humidityMin?: number;
    humidityMax?: number;
  };
  notifications: {
    email: boolean;
    sms: boolean;
    push: boolean;
    webhooks: string[];
  };
  escalation: {
    delays: number[]; // Minutes between escalation levels
    recipients: string[][]; // Recipients for each escalation level
  };
}

/**
 * System health check
 */
export interface SystemHealth {
  tempstickApi: {
    status: 'healthy' | 'degraded' | 'down';
    latency: number; // milliseconds
    lastCheck: string;
  };
  database: {
    status: 'healthy' | 'degraded' | 'down';
    activeConnections: number;
    lastMigration: string;
  };
  sensors: {
    total: number;
    online: number;
    offline: number;
    lowBattery: number;
  };
  alerts: {
    active: number;
    unresolved: number;
    critical: number;
  };
}

/**
 * Utility Types
 */

/**
 * Time range selector options
 */
export type TimeRange = 
  | '1h'
  | '6h' 
  | '24h'
  | '7d'
  | '30d'
  | '90d'
  | 'custom';

/**
 * Chart data point
 */
export interface ChartDataPoint {
  timestamp: number;
  temperature: number;
  humidity?: number;
  sensorId: string;
  alertLevel?: 'normal' | 'warning' | 'critical';
}

/**
 * Pagination parameters
 */
export interface PaginationParams {
  page: number;
  limit: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

/**
 * Search and filter parameters
 */
export interface FilterParams {
  sensorIds?: string[];
  storageAreaIds?: string[];
  alertTypes?: TemperatureAlert['alert_type'][];
  severityLevels?: TemperatureAlert['severity_level'][];
  dateRange?: {
    start: string;
    end: string;
  };
  temperatureRange?: {
    min: number;
    max: number;
  };
  resolved?: boolean;
}
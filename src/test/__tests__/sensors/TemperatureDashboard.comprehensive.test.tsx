/**
 * Comprehensive Test Suite for TempStick Temperature Dashboard
 * 
 * This test suite validates end-to-end functionality of the temperature monitoring system
 * including data accuracy, real-time updates, alert systems, and edge cases.
 * 
 * Test Coverage Areas:
 * - Component rendering and UI consistency
 * - Data flow from service to dashboard
 * - Real-time updates and subscriptions
 * - Alert system validation
 * - Mock vs real data switching
 * - Error handling and edge cases
 * - Performance under load
 * - Critical sensor scenarios
 */

import React from 'react';
import { render, screen, fireEvent, waitFor, act } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { vi, describe, it, expect, beforeEach, afterEach } from 'vitest';
import { TemperatureDashboard } from '@/components/sensors/TemperatureDashboard';
import { useTemperatureDashboard } from '@/hooks/useTemperatureDashboard';
import { tempStickService } from '@/lib/tempstick-service';
// Mock imports removed - tests now use real API service stubs
import type { 
  SensorStatus, 
  DashboardSummary, 
  TemperatureTrendData,
  SystemHealth 
} from '@/types/tempstick';

// Mock the hook
vi.mock('@/hooks/useTemperatureDashboard');
vi.mock('@/lib/tempstick-service');

// Mock Recharts to avoid canvas issues in tests
vi.mock('recharts', () => ({
  LineChart: ({ children, ...props }: any) => <div data-testid="line-chart" {...props}>{children}</div>,
  Line: (props: any) => <div data-testid="chart-line" {...props} />,
  XAxis: (props: any) => <div data-testid="x-axis" {...props} />,
  YAxis: (props: any) => <div data-testid="y-axis" {...props} />,
  CartesianGrid: (props: any) => <div data-testid="cartesian-grid" {...props} />,
  Tooltip: (props: any) => <div data-testid="chart-tooltip" {...props} />,
  ResponsiveContainer: ({ children, ...props }: any) => (
    <div data-testid="responsive-container" {...props}>{children}</div>
  ),
  ReferenceLine: (props: any) => <div data-testid="reference-line" {...props} />
}));

// Mock TempStick Data Source Selector
vi.mock('@/components/sensors/TempStickDataSourceSelector', () => ({
  TempStickDataSourceSelector: ({ onDataSourceChange }: { onDataSourceChange: (mode: string) => void }) => (
    <div data-testid="tempstick-data-source-selector">
      <button 
        data-testid="mock-data-button"
        onClick={() => onDataSourceChange('mock')}
      >
        Use Mock Data
      </button>
      <button 
        data-testid="real-data-button"
        onClick={() => onDataSourceChange('real')}
      >
        Use Real Data
      </button>
    </div>
  )
}));

describe('TemperatureDashboard - Comprehensive Test Suite', () => {
  const mockUseTemperatureDashboard = vi.mocked(useTemperatureDashboard);
  const mockTempStickService = vi.mocked(tempStickService);
  
  // Test data based on actual dashboard screenshot
  const mockSensorStatuses: SensorStatus[] = [
    {
      sensor: {
        id: 'freezer-1',
        tempstick_sensor_id: 'temp_001',
        name: 'Freezer Sensor 1',
        location: 'Walk-in Freezer',
        sensor_type: 'temperature_humidity',
        installation_date: null,
        calibration_date: null,
        temp_min_threshold: -10,
        temp_max_threshold: 0,
        storage_area_id: 'freezer-area-1',
        active: true,
        created_at: '2024-01-01T00:00:00Z'
      },
      latestReading: {
        id: 'reading-1',
        sensor_id: 'freezer-1',
        temperature: -6.8,
        humidity: 88,
        reading_timestamp: new Date().toISOString(),
        alert_triggered: false,
        created_at: new Date().toISOString()
      },
      status: 'online',
      activeAlerts: [],
      lastSyncTime: new Date().toISOString(),
      batteryLevel: 74,
      signalStrength: 85
    },
    {
      sensor: {
        id: 'display-case',
        tempstick_sensor_id: 'temp_002',
        name: 'Display Case Sensor',
        location: 'Seafood Display Case',
        sensor_type: 'temperature_humidity',
        installation_date: null,
        calibration_date: null,
        temp_min_threshold: 32,
        temp_max_threshold: 38,
        storage_area_id: 'display-area-1',
        active: true,
        created_at: '2024-01-01T00:00:00Z'
      },
      latestReading: {
        id: 'reading-2',
        sensor_id: 'display-case',
        temperature: 34.2,
        humidity: 95,
        reading_timestamp: new Date().toISOString(),
        alert_triggered: true,
        created_at: new Date().toISOString()
      },
      status: 'critical',
      activeAlerts: [
        {
          id: 'alert-1',
          sensor_id: 'display-case',
          alert_type: 'high_temp',
          temperature: 34.2,
          humidity: 95,
          alert_timestamp: new Date().toISOString(),
          severity_level: 'high',
          resolved_timestamp: null,
          notification_sent: false,
          created_at: new Date().toISOString()
        }
      ],
      lastSyncTime: new Date().toISOString(),
      batteryLevel: 93,
      signalStrength: 98
    },
    {
      sensor: {
        id: 'freezer-2',
        tempstick_sensor_id: 'temp_003',
        name: 'Freezer Sensor 2',
        location: 'Backup Freezer',
        sensor_type: 'temperature_humidity',
        installation_date: null,
        calibration_date: null,
        temp_min_threshold: -10,
        temp_max_threshold: 0,
        storage_area_id: 'freezer-area-2',
        active: true,
        created_at: '2024-01-01T00:00:00Z'
      },
      latestReading: null, // Offline sensor
      status: 'offline',
      activeAlerts: [],
      lastSyncTime: new Date(Date.now() - 3600000).toISOString(), // 1 hour ago
      batteryLevel: undefined,
      signalStrength: undefined
    },
    {
      sensor: {
        id: 'dry-storage',
        tempstick_sensor_id: 'temp_004',
        name: 'Dry Storage Sensor',
        location: 'Dry Storage Room',
        sensor_type: 'temperature_humidity',
        installation_date: null,
        calibration_date: null,
        temp_min_threshold: 50,
        temp_max_threshold: 70,
        storage_area_id: 'dry-storage-1',
        active: true,
        created_at: '2024-01-01T00:00:00Z'
      },
      latestReading: {
        id: 'reading-4',
        sensor_id: 'dry-storage',
        temperature: 67.3,
        humidity: 51,
        reading_timestamp: new Date().toISOString(),
        alert_triggered: false,
        created_at: new Date().toISOString()
      },
      status: 'online',
      activeAlerts: [],
      lastSyncTime: new Date().toISOString(),
      batteryLevel: 75,
      signalStrength: 94
    }
  ];

  const mockDashboardSummary: DashboardSummary = {
    totalSensors: 4,
    onlineSensors: 3,
    activeAlerts: 1,
    criticalAlerts: 1,
    averageTemperature: 22.2,
    temperatureRange: { min: -6.8, max: 67.3 }
  };

  const mockTemperatureTrends: TemperatureTrendData[] = [
    {
      timestamp: new Date(Date.now() - 3600000).toISOString(),
      temperature: -7.2,
      humidity: 85,
      sensorName: 'Freezer Sensor 1',
      sensorId: 'freezer-1',
      alertLevel: 'normal'
    },
    {
      timestamp: new Date(Date.now() - 1800000).toISOString(),
      temperature: 35.1,
      humidity: 92,
      sensorName: 'Display Case Sensor',
      sensorId: 'display-case',
      alertLevel: 'critical'
    },
    {
      timestamp: new Date().toISOString(),
      temperature: 34.2,
      humidity: 95,
      sensorName: 'Display Case Sensor',
      sensorId: 'display-case',
      alertLevel: 'critical'
    }
  ];

  const defaultHookReturn = {
    sensorStatuses: mockSensorStatuses,
    dashboardSummary: mockDashboardSummary,
    temperatureTrends: mockTemperatureTrends,
    systemHealth: generateMockSystemHealth(),
    loading: false,
    error: null,
    lastUpdate: new Date(),
    autoRefresh: true,
    setAutoRefresh: vi.fn(),
    filters: {
      timeRange: '24h' as const,
      selectedSensors: [],
      selectedStorageAreas: [],
      showOfflineSensors: true,
      alertsOnly: false
    },
    setFilters: vi.fn(),
    refreshData: vi.fn(),
    clearError: vi.fn(),
    availableStorageAreas: [
      { id: 'freezer-area-1', name: 'Walk-in Freezer' },
      { id: 'display-area-1', name: 'Display Case' },
      { id: 'dry-storage-1', name: 'Dry Storage' }
    ],
    availableSensors: [
      { id: 'freezer-1', name: 'Freezer Sensor 1' },
      { id: 'display-case', name: 'Display Case Sensor' },
      { id: 'dry-storage', name: 'Dry Storage Sensor' }
    ]
  };

  beforeEach(() => {
    vi.clearAllMocks();
    mockUseTemperatureDashboard.mockReturnValue(defaultHookReturn);
    
    // Mock service methods
    mockTempStickService.getSensors.mockResolvedValue([]);
    mockTempStickService.getLatestReadings.mockResolvedValue([]);
    mockTempStickService.performHealthCheck.mockResolvedValue(generateMockSystemHealth());
  });

  afterEach(() => {
    vi.clearAllTimers();
  });

  describe('1. Component Rendering and UI Consistency', () => {
    it('renders dashboard with all key components', async () => {
      render(<TemperatureDashboard />);

      // Verify main sections exist
      expect(screen.getByText('Temperature Monitoring')).toBeInTheDocument();
      expect(screen.getByText('Real-time temperature monitoring and alerts')).toBeInTheDocument();
      
      // Verify controls
      expect(screen.getByText('Auto-refresh')).toBeInTheDocument();
      expect(screen.getByText('Refresh')).toBeInTheDocument();
      
      // Verify filter section
      expect(screen.getByText('Filters')).toBeInTheDocument();
      
      // Verify data source selector
      expect(screen.getByTestId('tempstick-data-source-selector')).toBeInTheDocument();
    });

    it('displays sensor status cards with correct data', async () => {
      render(<TemperatureDashboard />);

      // Verify sensor cards are rendered
      expect(screen.getByText('Freezer Sensor 1')).toBeInTheDocument();
      expect(screen.getByText('Display Case Sensor')).toBeInTheDocument();
      expect(screen.getByText('Dry Storage Sensor')).toBeInTheDocument();
      
      // Verify temperature readings
      expect(screen.getByText('-6.8°F')).toBeInTheDocument();
      expect(screen.getByText('34.2°F')).toBeInTheDocument();
      expect(screen.getByText('67.3°F')).toBeInTheDocument();
      
      // Verify humidity readings
      expect(screen.getByText('88% RH')).toBeInTheDocument();
      expect(screen.getByText('95% RH')).toBeInTheDocument();
      expect(screen.getByText('51% RH')).toBeInTheDocument();
    });

    it('shows correct sensor statuses and alerts', async () => {
      render(<TemperatureDashboard />);

      // Check status badges
      expect(screen.getAllByText('Online')).toHaveLength(2); // Freezer 1 and Dry Storage
      expect(screen.getByText('Critical')).toBeInTheDocument(); // Display Case
      expect(screen.getByText('Offline')).toBeInTheDocument(); // Freezer 2
      
      // Check alert indicator
      expect(screen.getByText('1 active alert')).toBeInTheDocument();
    });

    it('displays dashboard summary cards correctly', async () => {
      render(<TemperatureDashboard />);

      // Verify summary statistics
      expect(screen.getByText('Total Sensors')).toBeInTheDocument();
      expect(screen.getByText('4')).toBeInTheDocument();
      
      expect(screen.getByText('Online Sensors')).toBeInTheDocument();
      expect(screen.getByText('3')).toBeInTheDocument();
      
      expect(screen.getByText('Active Alerts')).toBeInTheDocument();
      expect(screen.getByText('1')).toBeInTheDocument();
      
      expect(screen.getByText('Avg Temperature')).toBeInTheDocument();
      expect(screen.getByText('22.2°F')).toBeInTheDocument();
    });

    it('renders temperature trend chart', async () => {
      render(<TemperatureDashboard />);

      // Verify chart components are present
      expect(screen.getByText('Temperature Trends')).toBeInTheDocument();
      expect(screen.getByTestId('line-chart')).toBeInTheDocument();
      expect(screen.getByTestId('responsive-container')).toBeInTheDocument();
    });
  });

  describe('2. Data Flow and Service Integration', () => {
    it('calls useTemperatureDashboard hook with correct parameters', () => {
      render(<TemperatureDashboard autoRefreshInterval={15000} />);

      expect(mockUseTemperatureDashboard).toHaveBeenCalledWith({
        autoRefreshInterval: 15000,
        enableRealTimeUpdates: true,
        maxTrendDataPoints: 1000
      });
    });

    it('handles data source mode switching', async () => {
      const user = userEvent.setup();
      const mockRefreshData = vi.fn();
      mockUseTemperatureDashboard.mockReturnValue({
        ...defaultHookReturn,
        refreshData: mockRefreshData
      });

      render(<TemperatureDashboard />);

      // Click mock data button
      const mockDataButton = screen.getByTestId('mock-data-button');
      await user.click(mockDataButton);

      expect(mockRefreshData).toHaveBeenCalled();
    });

    it('displays loading state correctly', () => {
      mockUseTemperatureDashboard.mockReturnValue({
        ...defaultHookReturn,
        loading: true,
        sensorStatuses: []
      });

      render(<TemperatureDashboard />);

      expect(screen.getByText('Loading temperature dashboard...')).toBeInTheDocument();
    });

    it('handles empty sensor state', () => {
      mockUseTemperatureDashboard.mockReturnValue({
        ...defaultHookReturn,
        sensorStatuses: [],
        loading: false
      });

      render(<TemperatureDashboard />);

      expect(screen.getByText('No sensors found')).toBeInTheDocument();
      expect(screen.getByText('No temperature sensors match your current filters.')).toBeInTheDocument();
    });
  });

  describe('3. Real-time Updates and Auto-refresh', () => {
    it('toggles auto-refresh functionality', async () => {
      const user = userEvent.setup();
      const mockSetAutoRefresh = vi.fn();
      
      mockUseTemperatureDashboard.mockReturnValue({
        ...defaultHookReturn,
        setAutoRefresh: mockSetAutoRefresh
      });

      render(<TemperatureDashboard />);

      const autoRefreshSwitch = screen.getByRole('switch');
      await user.click(autoRefreshSwitch);

      expect(mockSetAutoRefresh).toHaveBeenCalledWith(false);
    });

    it('handles manual refresh action', async () => {
      const user = userEvent.setup();
      const mockRefreshData = vi.fn();
      
      mockUseTemperatureDashboard.mockReturnValue({
        ...defaultHookReturn,
        refreshData: mockRefreshData
      });

      render(<TemperatureDashboard />);

      const refreshButton = screen.getByRole('button', { name: /refresh/i });
      await user.click(refreshButton);

      expect(mockRefreshData).toHaveBeenCalled();
    });

    it('displays last update timestamp', () => {
      const lastUpdate = new Date('2024-01-15T10:30:00Z');
      
      mockUseTemperatureDashboard.mockReturnValue({
        ...defaultHookReturn,
        lastUpdate
      });

      render(<TemperatureDashboard />);

      expect(screen.getByText(/Last updated:/)).toBeInTheDocument();
    });

    it('shows loading state during refresh', () => {
      mockUseTemperatureDashboard.mockReturnValue({
        ...defaultHookReturn,
        loading: true
      });

      render(<TemperatureDashboard />);

      // Refresh button should be disabled and show loading
      const refreshButton = screen.getByRole('button', { name: /refresh/i });
      expect(refreshButton).toBeDisabled();
    });
  });

  describe('4. Alert System Validation', () => {
    it('identifies critical sensor correctly', () => {
      render(<TemperatureDashboard />);

      // Display Case Sensor should show critical status
      const criticalBadge = screen.getByText('Critical');
      expect(criticalBadge).toBeInTheDocument();
      expect(criticalBadge.closest('[class*="border-red"]')).toBeTruthy();
    });

    it('displays alert count correctly', () => {
      render(<TemperatureDashboard />);

      // Should show 1 active alert from Display Case Sensor
      expect(screen.getByText('1 active alert')).toBeInTheDocument();
    });

    it('handles multiple alerts scenario', () => {
      const sensorsWithMultipleAlerts = [...mockSensorStatuses];
      sensorsWithMultipleAlerts[0].activeAlerts = [
        {
          id: 'alert-2',
          sensor_id: 'freezer-1',
          alert_type: 'low_temp',
          temperature: -15,
          humidity: 90,
          alert_timestamp: new Date().toISOString(),
          severity_level: 'high',
          resolved_timestamp: null,
          notification_sent: false,
          created_at: new Date().toISOString()
        }
      ];
      sensorsWithMultipleAlerts[0].status = 'warning';

      mockUseTemperatureDashboard.mockReturnValue({
        ...defaultHookReturn,
        sensorStatuses: sensorsWithMultipleAlerts,
        dashboardSummary: {
          ...mockDashboardSummary,
          activeAlerts: 2
        }
      });

      render(<TemperatureDashboard />);

      expect(screen.getByText('2')).toBeInTheDocument(); // Active Alerts count
    });

    it('displays no alerts when all sensors are normal', () => {
      const normalSensors = mockSensorStatuses.map(sensor => ({
        ...sensor,
        status: sensor.sensor.id === 'freezer-2' ? 'offline' as const : 'online' as const,
        activeAlerts: []
      }));

      mockUseTemperatureDashboard.mockReturnValue({
        ...defaultHookReturn,
        sensorStatuses: normalSensors,
        dashboardSummary: {
          ...mockDashboardSummary,
          activeAlerts: 0,
          criticalAlerts: 0
        }
      });

      render(<TemperatureDashboard />);

      expect(screen.getByText('0')).toBeInTheDocument(); // Active Alerts count should be 0
    });
  });

  describe('5. Filter Functionality', () => {
    it('renders filter controls', () => {
      render(<TemperatureDashboard showFilters={true} />);

      expect(screen.getByText('Time Range')).toBeInTheDocument();
      expect(screen.getByText('Storage Areas')).toBeInTheDocument();
      expect(screen.getByText('Show offline sensors')).toBeInTheDocument();
      expect(screen.getByText('Alerts only')).toBeInTheDocument();
    });

    it('handles time range filter changes', async () => {
      const user = userEvent.setup();
      const mockSetFilters = vi.fn();
      
      mockUseTemperatureDashboard.mockReturnValue({
        ...defaultHookReturn,
        setFilters: mockSetFilters
      });

      render(<TemperatureDashboard showFilters={true} />);

      // This would test the select component interaction
      // Note: Radix UI Select components need specific testing approach
      expect(screen.getByText('Time Range')).toBeInTheDocument();
    });

    it('handles clear filters action', async () => {
      const user = userEvent.setup();
      const mockSetFilters = vi.fn();
      
      mockUseTemperatureDashboard.mockReturnValue({
        ...defaultHookReturn,
        sensorStatuses: [],
        setFilters: mockSetFilters
      });

      render(<TemperatureDashboard />);

      const clearFiltersButton = screen.getByRole('button', { name: /clear filters/i });
      await user.click(clearFiltersButton);

      expect(mockSetFilters).toHaveBeenCalledWith({
        timeRange: '24h',
        selectedSensors: [],
        selectedStorageAreas: [],
        showOfflineSensors: true,
        alertsOnly: false
      });
    });

    it('hides filters when showFilters is false', () => {
      render(<TemperatureDashboard showFilters={false} />);

      expect(screen.queryByText('Filters')).not.toBeInTheDocument();
    });
  });

  describe('6. Error Handling and Edge Cases', () => {
    it('displays error alert when error occurs', () => {
      const errorMessage = 'Failed to connect to TempStick API';
      
      mockUseTemperatureDashboard.mockReturnValue({
        ...defaultHookReturn,
        error: errorMessage
      });

      render(<TemperatureDashboard />);

      expect(screen.getByText(errorMessage)).toBeInTheDocument();
      expect(screen.getByRole('alert')).toBeInTheDocument();
    });

    it('handles offline sensor gracefully', () => {
      render(<TemperatureDashboard />);

      // Freezer Sensor 2 is offline
      expect(screen.getByText('Offline')).toBeInTheDocument();
      expect(screen.getByText('No recent readings')).toBeInTheDocument();
    });

    it('handles missing sensor readings', () => {
      const sensorsWithNoReadings = mockSensorStatuses.map(sensor => ({
        ...sensor,
        latestReading: null
      }));

      mockUseTemperatureDashboard.mockReturnValue({
        ...defaultHookReturn,
        sensorStatuses: sensorsWithNoReadings
      });

      render(<TemperatureDashboard />);

      expect(screen.getAllByText('No recent readings')).toHaveLength(4);
    });

    it('handles invalid temperature data gracefully', () => {
      const sensorsWithInvalidData = [...mockSensorStatuses];
      sensorsWithInvalidData[0].latestReading = {
        ...sensorsWithInvalidData[0].latestReading!,
        temperature: NaN,
        humidity: null
      };

      mockUseTemperatureDashboard.mockReturnValue({
        ...defaultHookReturn,
        sensorStatuses: sensorsWithInvalidData
      });

      render(<TemperatureDashboard />);

      // Component should still render without crashing
      expect(screen.getByText('Temperature Monitoring')).toBeInTheDocument();
    });

    it('handles network connectivity issues', () => {
      mockUseTemperatureDashboard.mockReturnValue({
        ...defaultHookReturn,
        error: 'Network error: Unable to connect to sensors',
        sensorStatuses: []
      });

      render(<TemperatureDashboard />);

      expect(screen.getByText('Network error: Unable to connect to sensors')).toBeInTheDocument();
      expect(screen.getByText('No sensors found')).toBeInTheDocument();
    });
  });

  describe('7. Performance and Optimization', () => {
    it('handles large datasets efficiently', () => {
      const largeSensorArray = Array.from({ length: 50 }, (_, i) => ({
        ...mockSensorStatuses[0],
        sensor: {
          ...mockSensorStatuses[0].sensor,
          id: `sensor-${i}`,
          name: `Sensor ${i + 1}`
        }
      }));

      mockUseTemperatureDashboard.mockReturnValue({
        ...defaultHookReturn,
        sensorStatuses: largeSensorArray,
        dashboardSummary: {
          ...mockDashboardSummary,
          totalSensors: 50,
          onlineSensors: 45
        }
      });

      const startTime = performance.now();
      render(<TemperatureDashboard />);
      const renderTime = performance.now() - startTime;

      // Should render within reasonable time (2 seconds for 50 sensors)
      expect(renderTime).toBeLessThan(2000);
      expect(screen.getByText('50')).toBeInTheDocument(); // Total sensors
    });

    it('renders in compact mode efficiently', () => {
      render(<TemperatureDashboard compactMode={true} />);

      // In compact mode, certain details should be hidden
      expect(screen.getByText('Temperature Monitoring')).toBeInTheDocument();
      // But component should still function
      expect(screen.getByText('Freezer Sensor 1')).toBeInTheDocument();
    });

    it('handles rapid state updates without performance degradation', async () => {
      const { rerender } = render(<TemperatureDashboard />);

      // Simulate rapid updates
      for (let i = 0; i < 10; i++) {
        const updatedSensors = mockSensorStatuses.map(sensor => ({
          ...sensor,
          latestReading: sensor.latestReading ? {
            ...sensor.latestReading,
            temperature: sensor.latestReading.temperature + Math.random() * 0.1,
            reading_timestamp: new Date().toISOString()
          } : null
        }));

        mockUseTemperatureDashboard.mockReturnValue({
          ...defaultHookReturn,
          sensorStatuses: updatedSensors,
          lastUpdate: new Date()
        });

        rerender(<TemperatureDashboard />);
      }

      // Component should still be responsive
      expect(screen.getByText('Temperature Monitoring')).toBeInTheDocument();
    });
  });

  describe('8. Critical Sensor Scenarios (From User Screenshot)', () => {
    it('correctly displays Display Case critical alert scenario', () => {
      render(<TemperatureDashboard />);

      // Display Case Sensor showing 34.2°F with Critical status and 1 alert
      const displayCaseSensor = screen.getByText('Display Case Sensor').closest('[class*="card"]');
      expect(displayCaseSensor).toBeInTheDocument();
      
      // Check critical status
      expect(screen.getByText('Critical')).toBeInTheDocument();
      
      // Check temperature reading
      expect(screen.getByText('34.2°F')).toBeInTheDocument();
      
      // Check humidity
      expect(screen.getByText('95% RH')).toBeInTheDocument();
      
      // Check alert count
      expect(screen.getByText('1 active alert')).toBeInTheDocument();
      
      // Check battery and signal (93% battery, 98% signal)
      expect(screen.getByText('93%')).toBeInTheDocument(); // Battery
      expect(screen.getByText('98%')).toBeInTheDocument(); // Signal
    });

    it('correctly displays Freezer Sensor 2 offline scenario', () => {
      render(<TemperatureDashboard />);

      // Freezer Sensor 2 should be offline with no recent readings
      const freezerSensor2 = screen.getByText('Freezer Sensor 2').closest('[class*="card"]');
      expect(freezerSensor2).toBeInTheDocument();
      
      // Check offline status
      expect(screen.getByText('Offline')).toBeInTheDocument();
      
      // Should show no recent readings
      expect(screen.getByText('No recent readings')).toBeInTheDocument();
    });

    it('validates temperature threshold logic for critical alerts', () => {
      // Display Case Sensor at 34.2°F should be critical if threshold is 32-38°F range
      const sensor = mockSensorStatuses.find(s => s.sensor.id === 'display-case');
      expect(sensor).toBeDefined();
      expect(sensor!.latestReading!.temperature).toBe(34.2);
      expect(sensor!.status).toBe('critical');
      expect(sensor!.activeAlerts).toHaveLength(1);
      
      render(<TemperatureDashboard />);
      
      // Should display as critical
      expect(screen.getByText('Critical')).toBeInTheDocument();
    });

    it('handles battery level indicators correctly', () => {
      render(<TemperatureDashboard />);

      // Check various battery levels are displayed
      expect(screen.getByText('74%')).toBeInTheDocument(); // Freezer 1
      expect(screen.getByText('93%')).toBeInTheDocument(); // Display Case
      expect(screen.getByText('75%')).toBeInTheDocument(); // Dry Storage
      
      // Offline sensor (Freezer 2) should not show battery level
      const offlineSensor = screen.getByText('Freezer Sensor 2').closest('[class*="card"]');
      expect(offlineSensor).not.toHaveTextContent('Battery');
    });

    it('handles signal strength indicators correctly', () => {
      render(<TemperatureDashboard />);

      // Check signal strength percentages
      expect(screen.getByText('85%')).toBeInTheDocument(); // Freezer 1 signal
      expect(screen.getByText('98%')).toBeInTheDocument(); // Display Case signal
      expect(screen.getByText('94%')).toBeInTheDocument(); // Dry Storage signal
    });
  });

  describe('9. Data Mode Switching Validation', () => {
    it('handles mock to real data switching', async () => {
      const user = userEvent.setup();
      const mockRefreshData = vi.fn();
      
      mockUseTemperatureDashboard.mockReturnValue({
        ...defaultHookReturn,
        refreshData: mockRefreshData
      });

      render(<TemperatureDashboard />);

      // Switch to real data
      const realDataButton = screen.getByTestId('real-data-button');
      await user.click(realDataButton);

      expect(mockRefreshData).toHaveBeenCalled();
    });

    it('displays appropriate indicators for different data modes', async () => {
      render(<TemperatureDashboard />);

      // TempStick Data Source Selector should be present
      expect(screen.getByTestId('tempstick-data-source-selector')).toBeInTheDocument();
      expect(screen.getByTestId('mock-data-button')).toBeInTheDocument();
      expect(screen.getByTestId('real-data-button')).toBeInTheDocument();
    });
  });

  describe('10. Chart and Visualization Validation', () => {
    it('renders temperature trend chart with correct data', () => {
      render(<TemperatureDashboard />);

      expect(screen.getByText('Temperature Trends')).toBeInTheDocument();
      expect(screen.getByTestId('line-chart')).toBeInTheDocument();
      expect(screen.getByTestId('responsive-container')).toBeInTheDocument();
    });

    it('handles empty chart data gracefully', () => {
      mockUseTemperatureDashboard.mockReturnValue({
        ...defaultHookReturn,
        temperatureTrends: []
      });

      render(<TemperatureDashboard />);

      // Chart section should not be rendered if no data
      expect(screen.queryByText('Temperature Trends')).not.toBeInTheDocument();
    });

    it('displays chart with multiple sensor data', () => {
      const multiSensorTrends = [
        ...mockTemperatureTrends,
        {
          timestamp: new Date().toISOString(),
          temperature: 68.5,
          humidity: 48,
          sensorName: 'Dry Storage Sensor',
          sensorId: 'dry-storage',
          alertLevel: 'normal' as const
        }
      ];

      mockUseTemperatureDashboard.mockReturnValue({
        ...defaultHookReturn,
        temperatureTrends: multiSensorTrends
      });

      render(<TemperatureDashboard />);

      expect(screen.getByText('Temperature Trends')).toBeInTheDocument();
      expect(screen.getByTestId('line-chart')).toBeInTheDocument();
    });
  });
});
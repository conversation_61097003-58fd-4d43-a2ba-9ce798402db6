/**
 * HACCP-Compliant Voice Audit Trail System
 * Comprehensive audit logging for voice-based inventory operations
 * Meets FDA, USDC, and GFSI requirements for food safety documentation
 */

import { supabase } from '../lib/supabase';
import { AudioStorageService } from './AudioStorageService';

interface VoiceAuditEntry {
  id?: string;
  session_id: string;
  user_id: string;
  timestamp: string;
  event_type: 'voice_input' | 'transcription' | 'data_extraction' | 'validation' | 'form_submission' | 'error' | 'correction';
  
  // Voice-specific data
  audio_duration_ms?: number;
  confidence_score?: number;
  raw_transcript?: string;
  processed_transcript?: string;
  extracted_data?: any;
  validation_results?: any;
  
  // HACCP-specific fields
  haccp_critical_control_point?: string;
  temperature_reading?: number;
  temperature_unit?: 'fahrenheit' | 'celsius';
  product_identification?: string;
  lot_batch_number?: string;
  corrective_actions?: string[];
  
  // Compliance fields
  regulatory_category?: 'receiving' | 'storage' | 'processing' | 'distribution' | 'disposal';
  risk_level?: 'low' | 'medium' | 'high' | 'critical';
  requires_review?: boolean;
  reviewed_by?: string;
  reviewed_at?: string;
  
  // Technical metadata
  device_info?: any;
  browser_info?: any;
  network_quality?: 'excellent' | 'good' | 'poor';
  processing_method?: 'realtime_api' | 'browser_speech' | 'offline_processing';
  
  // Data integrity
  checksum?: string;
  digital_signature?: string;
  immutable_hash?: string;
}

interface AuditSearchCriteria {
  startDate?: Date;
  endDate?: Date;
  userId?: string;
  sessionId?: string;
  eventType?: string;
  riskLevel?: string;
  productId?: string;
  requiresReview?: boolean;
  limit?: number;
  offset?: number;
}

interface ComplianceReport {
  reportId: string;
  generatedAt: string;
  reportType: 'daily' | 'weekly' | 'monthly' | 'incident' | 'audit';
  period: { startDate: string; endDate: string };
  summary: {
    totalVoiceEvents: number;
    criticalControlPoints: number;
    temperatureDeviations: number;
    correctiveActions: number;
    reviewPendingItems: number;
  };
  findings: Array<{
    category: string;
    description: string;
    severity: 'info' | 'warning' | 'critical';
    recommendations: string[];
  }>;
  auditEntries: VoiceAuditEntry[];
  digitalSignature: string;
}

/**
 * HACCP-compliant audit trail service for voice operations
 */
export class HACCPVoiceAuditTrail {
  private audioStorage: AudioStorageService;
  private currentSession: string | null = null;
  private auditBuffer: VoiceAuditEntry[] = [];
  private batchSize = 10;
  private flushInterval: NodeJS.Timeout | null = null;

  constructor() {
    this.audioStorage = new AudioStorageService();
    this.initializeSession();
    this.startBatchFlushing();
    console.log('🔒 HACCP Voice Audit Trail System initialized');
  }

  /**
   * Initialize a new audit session
   */
  private initializeSession(): void {
    this.currentSession = `voice_session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    // Log session start
    this.logAuditEntry({
      session_id: this.currentSession,
      user_id: 'system',
      timestamp: new Date().toISOString(),
      event_type: 'voice_input',
      extracted_data: {
        event: 'session_started',
        session_id: this.currentSession
      },
      device_info: this.getDeviceInfo(),
      browser_info: this.getBrowserInfo(),
      processing_method: 'realtime_api'
    });
  }

  /**
   * Log a voice input event with full audit trail
   */
  async logVoiceInput(data: {
    userId: string;
    audioData?: ArrayBuffer;
    audioDurationMs?: number;
    transcript?: string;
    confidenceScore?: number;
    extractedData?: any;
    networkQuality?: 'excellent' | 'good' | 'poor';
    processingMethod?: 'realtime_api' | 'browser_speech' | 'offline_processing';
  }): Promise<string> {
    
    const auditEntry: VoiceAuditEntry = {
      session_id: this.currentSession!,
      user_id: data.userId,
      timestamp: new Date().toISOString(),
      event_type: 'voice_input',
      audio_duration_ms: data.audioDurationMs,
      confidence_score: data.confidenceScore,
      raw_transcript: data.transcript,
      extracted_data: data.extractedData,
      network_quality: data.networkQuality,
      processing_method: data.processingMethod,
      device_info: this.getDeviceInfo(),
      browser_info: this.getBrowserInfo()
    };

    // Determine HACCP relevance and risk level
    const haccpData = this.analyzeHACCPRelevance(data);
    Object.assign(auditEntry, haccpData);

    // Store audio data if provided (for compliance with audio retention requirements)
    if (data.audioData) {
      const audioReference = await this.audioStorage.storeAudioWithMetadata(data.audioData, {
        sessionId: this.currentSession!,
        userId: data.userId,
        timestamp: auditEntry.timestamp,
        complianceCategory: auditEntry.regulatory_category,
        retentionPeriod: this.getRetentionPeriod(auditEntry.risk_level!)
      });
      
      auditEntry.extracted_data = {
        ...auditEntry.extracted_data,
        audio_reference: audioReference
      };
    }

    // Generate data integrity fields
    auditEntry.checksum = this.generateChecksum(auditEntry);
    auditEntry.immutable_hash = await this.generateImmutableHash(auditEntry);

    return await this.persistAuditEntry(auditEntry);
  }

  /**
   * Log transcription processing event
   */
  async logTranscriptionProcessing(data: {
    userId: string;
    rawTranscript: string;
    processedTranscript: string;
    confidenceScore: number;
    processingTimeMs: number;
    corrections?: Array<{ from: string; to: string; reason: string }>;
  }): Promise<string> {
    
    const auditEntry: VoiceAuditEntry = {
      session_id: this.currentSession!,
      user_id: data.userId,
      timestamp: new Date().toISOString(),
      event_type: 'transcription',
      raw_transcript: data.rawTranscript,
      processed_transcript: data.processedTranscript,
      confidence_score: data.confidenceScore,
      extracted_data: {
        processing_time_ms: data.processingTimeMs,
        corrections: data.corrections
      }
    };

    auditEntry.checksum = this.generateChecksum(auditEntry);
    return await this.persistAuditEntry(auditEntry);
  }

  /**
   * Log data extraction event
   */
  async logDataExtraction(data: {
    userId: string;
    transcript: string;
    extractedData: any;
    confidenceBreakdown: any;
    validationResults?: any;
  }): Promise<string> {
    
    const auditEntry: VoiceAuditEntry = {
      session_id: this.currentSession!,
      user_id: data.userId,
      timestamp: new Date().toISOString(),
      event_type: 'data_extraction',
      processed_transcript: data.transcript,
      extracted_data: data.extractedData,
      validation_results: data.validationResults,
      confidence_score: data.confidenceBreakdown.overall
    };

    // Enhanced HACCP analysis for data extraction
    const haccpData = this.analyzeHACCPRelevance(data);
    Object.assign(auditEntry, haccpData);

    auditEntry.checksum = this.generateChecksum(auditEntry);
    return await this.persistAuditEntry(auditEntry);
  }

  /**
   * Log form validation events
   */
  async logValidation(data: {
    userId: string;
    formData: any;
    validationResults: any[];
    passedValidation: boolean;
    criticalIssues: string[];
  }): Promise<string> {
    
    const auditEntry: VoiceAuditEntry = {
      session_id: this.currentSession!,
      user_id: data.userId,
      timestamp: new Date().toISOString(),
      event_type: 'validation',
      extracted_data: data.formData,
      validation_results: data.validationResults,
      requires_review: !data.passedValidation || data.criticalIssues.length > 0
    };

    // Check for critical control points
    const criticalControlPoint = this.identifyCriticalControlPoint(data.formData);
    if (criticalControlPoint) {
      auditEntry.haccp_critical_control_point = criticalControlPoint;
      auditEntry.risk_level = 'critical';
    }

    auditEntry.checksum = this.generateChecksum(auditEntry);
    return await this.persistAuditEntry(auditEntry);
  }

  /**
   * Log form submission events
   */
  async logFormSubmission(data: {
    userId: string;
    formData: any;
    submissionResult: { success: boolean; eventId?: string; error?: string };
    finalValidationPassed: boolean;
    voiceMetadata: any;
  }): Promise<string> {
    
    const auditEntry: VoiceAuditEntry = {
      session_id: this.currentSession!,
      user_id: data.userId,
      timestamp: new Date().toISOString(),
      event_type: 'form_submission',
      extracted_data: {
        ...data.formData,
        submission_result: data.submissionResult,
        voice_metadata: data.voiceMetadata
      },
      requires_review: !data.finalValidationPassed || !data.submissionResult.success
    };

    // Complete HACCP analysis for final submission
    const haccpData = this.analyzeHACCPRelevance(data);
    Object.assign(auditEntry, haccpData);

    auditEntry.checksum = this.generateChecksum(auditEntry);
    auditEntry.digital_signature = await this.generateDigitalSignature(auditEntry);
    
    return await this.persistAuditEntry(auditEntry);
  }

  /**
   * Log error events with corrective action tracking
   */
  async logError(data: {
    userId: string;
    error: Error;
    context: any;
    automaticRecovery?: boolean;
    correctiveActions?: string[];
    impactAssessment?: string;
  }): Promise<string> {
    
    const auditEntry: VoiceAuditEntry = {
      session_id: this.currentSession!,
      user_id: data.userId,
      timestamp: new Date().toISOString(),
      event_type: 'error',
      extracted_data: {
        error_message: data.error.message,
        error_stack: data.error.stack,
        context: data.context,
        automatic_recovery: data.automaticRecovery,
        impact_assessment: data.impactAssessment
      },
      corrective_actions: data.correctiveActions,
      risk_level: this.assessErrorRiskLevel(data.error, data.context),
      requires_review: true
    };

    auditEntry.checksum = this.generateChecksum(auditEntry);
    return await this.persistAuditEntry(auditEntry);
  }

  /**
   * Log manual corrections made by users
   */
  async logCorrection(data: {
    userId: string;
    originalData: any;
    correctedData: any;
    correctionReason: string;
    reviewedBy?: string;
  }): Promise<string> {
    
    const auditEntry: VoiceAuditEntry = {
      session_id: this.currentSession!,
      user_id: data.userId,
      timestamp: new Date().toISOString(),
      event_type: 'correction',
      extracted_data: {
        original_data: data.originalData,
        corrected_data: data.correctedData,
        correction_reason: data.correctionReason
      },
      reviewed_by: data.reviewedBy,
      reviewed_at: data.reviewedBy ? new Date().toISOString() : undefined,
      corrective_actions: [data.correctionReason]
    };

    auditEntry.checksum = this.generateChecksum(auditEntry);
    return await this.persistAuditEntry(auditEntry);
  }

  /**
   * Search audit trail entries
   */
  async searchAuditTrail(criteria: AuditSearchCriteria): Promise<VoiceAuditEntry[]> {
    let query = supabase
      .from('voice_audit_trail')
      .select('*');

    if (criteria.startDate) {
      query = query.gte('timestamp', criteria.startDate.toISOString());
    }
    if (criteria.endDate) {
      query = query.lte('timestamp', criteria.endDate.toISOString());
    }
    if (criteria.userId) {
      query = query.eq('user_id', criteria.userId);
    }
    if (criteria.sessionId) {
      query = query.eq('session_id', criteria.sessionId);
    }
    if (criteria.eventType) {
      query = query.eq('event_type', criteria.eventType);
    }
    if (criteria.riskLevel) {
      query = query.eq('risk_level', criteria.riskLevel);
    }
    if (criteria.requiresReview !== undefined) {
      query = query.eq('requires_review', criteria.requiresReview);
    }

    query = query
      .order('timestamp', { ascending: false })
      .limit(criteria.limit || 100);

    if (criteria.offset) {
      query = query.range(criteria.offset, criteria.offset + (criteria.limit || 100) - 1);
    }

    const { data, error } = await query;
    
    if (error) {
      throw new Error(`Failed to search audit trail: ${error.message}`);
    }

    return data || [];
  }

  /**
   * Generate compliance report
   */
  async generateComplianceReport(
    reportType: 'daily' | 'weekly' | 'monthly' | 'incident' | 'audit',
    startDate: Date,
    endDate: Date,
    additionalCriteria?: Partial<AuditSearchCriteria>
  ): Promise<ComplianceReport> {
    
    const reportId = `${reportType}_${startDate.toISOString().split('T')[0]}_${endDate.toISOString().split('T')[0]}_${Date.now()}`;
    
    // Fetch audit entries for the period
    const auditEntries = await this.searchAuditTrail({
      startDate,
      endDate,
      ...additionalCriteria,
      limit: 10000 // High limit for comprehensive reporting
    });

    // Generate summary statistics
    const summary = {
      totalVoiceEvents: auditEntries.filter(e => e.event_type === 'voice_input').length,
      criticalControlPoints: auditEntries.filter(e => e.haccp_critical_control_point).length,
      temperatureDeviations: auditEntries.filter(e => 
        e.temperature_reading && this.isTemperatureDeviation(e.temperature_reading, e.temperature_unit)
      ).length,
      correctiveActions: auditEntries.filter(e => 
        e.corrective_actions && e.corrective_actions.length > 0
      ).length,
      reviewPendingItems: auditEntries.filter(e => e.requires_review && !e.reviewed_by).length
    };

    // Analyze findings
    const findings = this.analyzeComplianceFindings(auditEntries, reportType);

    const report: ComplianceReport = {
      reportId,
      generatedAt: new Date().toISOString(),
      reportType,
      period: {
        startDate: startDate.toISOString(),
        endDate: endDate.toISOString()
      },
      summary,
      findings,
      auditEntries: auditEntries.slice(0, 1000), // Limit audit entries in report
      digitalSignature: await this.generateReportSignature(reportId, summary, findings)
    };

    // Store the report
    await this.storeComplianceReport(report);

    return report;
  }

  /**
   * Verify audit trail integrity
   */
  async verifyAuditIntegrity(startDate: Date, endDate: Date): Promise<{
    totalEntries: number;
    verifiedEntries: number;
    failedVerifications: Array<{ id: string; reason: string }>;
    integrityScore: number;
  }> {
    
    const entries = await this.searchAuditTrail({ startDate, endDate, limit: 10000 });
    const failedVerifications: Array<{ id: string; reason: string }> = [];

    for (const entry of entries) {
      // Verify checksum
      const calculatedChecksum = this.generateChecksum(entry);
      if (entry.checksum && entry.checksum !== calculatedChecksum) {
        failedVerifications.push({
          id: entry.id!,
          reason: 'Checksum mismatch - possible data tampering'
        });
        continue;
      }

      // Verify immutable hash if available
      if (entry.immutable_hash) {
        const calculatedHash = await this.generateImmutableHash(entry);
        if (entry.immutable_hash !== calculatedHash) {
          failedVerifications.push({
            id: entry.id!,
            reason: 'Immutable hash mismatch - data integrity compromised'
          });
        }
      }
    }

    const verifiedEntries = entries.length - failedVerifications.length;
    const integrityScore = entries.length > 0 ? (verifiedEntries / entries.length) * 100 : 100;

    return {
      totalEntries: entries.length,
      verifiedEntries,
      failedVerifications,
      integrityScore
    };
  }

  /**
   * Private helper methods
   */
  
  private analyzeHACCPRelevance(data: any): Partial<VoiceAuditEntry> {
    const result: Partial<VoiceAuditEntry> = {};
    
    // Analyze temperature data
    if (data.extractedData?.temperature || data.extractedData?.haccp?.temperature) {
      const temp = data.extractedData.temperature || data.extractedData.haccp.temperature.value;
      const unit = data.extractedData.temperatureUnit || data.extractedData.haccp.temperature.unit;
      
      result.temperature_reading = temp;
      result.temperature_unit = unit;
      
      // Check for temperature deviations
      if (this.isTemperatureDeviation(temp, unit)) {
        result.risk_level = 'critical';
        result.requires_review = true;
        result.corrective_actions = ['Investigate temperature deviation', 'Check equipment functionality'];
      }
    }
    
    // Identify regulatory category
    if (data.extractedData?.eventType || data.extractedData?.event_type) {
      const eventType = data.extractedData.eventType || data.extractedData.event_type;
      result.regulatory_category = this.mapEventTypeToRegulatoryCategory(eventType);
    }
    
    // Identify product and batch information
    if (data.extractedData?.productName || data.extractedData?.product_name) {
      result.product_identification = data.extractedData.productName || data.extractedData.product_name;
    }
    
    if (data.extractedData?.batchNumber || data.extractedData?.batch_number) {
      result.lot_batch_number = data.extractedData.batchNumber || data.extractedData.batch_number;
    }
    
    // Assess risk level if not already set
    if (!result.risk_level) {
      result.risk_level = this.assessRiskLevel(data);
    }
    
    return result;
  }

  private identifyCriticalControlPoint(formData: any): string | null {
    // Temperature monitoring
    if (formData.temperature !== undefined) {
      return 'Temperature Control';
    }
    
    // Receiving inspection
    if (formData.eventType === 'receiving' && formData.condition) {
      return 'Receiving Inspection';
    }
    
    // Storage conditions
    if (formData.storageLocation || formData.storageConditions) {
      return 'Storage Control';
    }
    
    // Disposal for safety reasons
    if (formData.eventType === 'disposal' && 
        (formData.reason?.includes('expired') || formData.reason?.includes('contaminated'))) {
      return 'Product Safety Disposal';
    }
    
    return null;
  }

  private isTemperatureDeviation(temperature: number, unit?: string): boolean {
    // Convert to Fahrenheit for consistent checking
    const tempF = unit === 'celsius' ? (temperature * 9/5) + 32 : temperature;
    
    // General seafood temperature ranges (Fahrenheit)
    const minSafeTemp = 28; // -2°C
    const maxSafeTemp = 38; // 3°C
    
    return tempF < minSafeTemp || tempF > maxSafeTemp;
  }

  private mapEventTypeToRegulatoryCategory(eventType: string): VoiceAuditEntry['regulatory_category'] {
    switch (eventType.toLowerCase()) {
      case 'receiving': return 'receiving';
      case 'storage': return 'storage';
      case 'production': return 'processing';
      case 'sale': case 'shipping': return 'distribution';
      case 'disposal': return 'disposal';
      default: return 'processing';
    }
  }

  private assessRiskLevel(data: any): VoiceAuditEntry['risk_level'] {
    // High confidence with no validation issues
    if (data.confidenceScore >= 0.9 && !data.validationResults?.some((r: any) => !r.valid)) {
      return 'low';
    }
    
    // Medium confidence or minor validation issues
    if (data.confidenceScore >= 0.7) {
      return 'medium';
    }
    
    // Low confidence or major validation failures
    if (data.confidenceScore < 0.7 || data.validationResults?.some((r: any) => r.severity === 'critical')) {
      return 'high';
    }
    
    return 'medium';
  }

  private assessErrorRiskLevel(error: Error, context: any): VoiceAuditEntry['risk_level'] {
    // Critical errors that could affect food safety
    if (error.message.includes('temperature') || 
        error.message.includes('critical') ||
        context.haccpRelated) {
      return 'critical';
    }
    
    // Data integrity errors
    if (error.message.includes('validation') || 
        error.message.includes('corrupt')) {
      return 'high';
    }
    
    // System errors
    if (error.message.includes('network') || 
        error.message.includes('timeout')) {
      return 'medium';
    }
    
    return 'low';
  }

  private generateChecksum(entry: VoiceAuditEntry): string {
    // Create a deterministic string from entry data (excluding checksum and signature fields)
    const checksumData = {
      session_id: entry.session_id,
      user_id: entry.user_id,
      timestamp: entry.timestamp,
      event_type: entry.event_type,
      raw_transcript: entry.raw_transcript,
      processed_transcript: entry.processed_transcript,
      extracted_data: entry.extracted_data,
      confidence_score: entry.confidence_score
    };
    
    const dataString = JSON.stringify(checksumData);
    return btoa(dataString).substring(0, 32); // Simple checksum - would use proper hash in production
  }

  private async generateImmutableHash(entry: VoiceAuditEntry): Promise<string> {
    // Would use a proper cryptographic hash function in production
    const data = JSON.stringify(entry);
    const encoder = new TextEncoder();
    const dataBuffer = encoder.encode(data);
    const hashBuffer = await crypto.subtle.digest('SHA-256', dataBuffer);
    const hashArray = Array.from(new Uint8Array(hashBuffer));
    return hashArray.map(b => b.toString(16).padStart(2, '0')).join('');
  }

  private async generateDigitalSignature(entry: VoiceAuditEntry): Promise<string> {
    // Simplified signature - would use proper digital signing in production
    const hash = await this.generateImmutableHash(entry);
    return `SIG_${hash.substring(0, 16)}`;
  }

  private async generateReportSignature(reportId: string, summary: any, findings: any[]): Promise<string> {
    const signatureData = { reportId, summary, findingsCount: findings.length };
    const hash = await this.generateImmutableHash(signatureData as any);
    return `RPT_${hash.substring(0, 16)}`;
  }

  private analyzeComplianceFindings(entries: VoiceAuditEntry[], reportType: string): ComplianceReport['findings'] {
    const findings: ComplianceReport['findings'] = [];
    
    // Temperature deviations
    const tempDeviations = entries.filter(e => 
      e.temperature_reading && this.isTemperatureDeviation(e.temperature_reading, e.temperature_unit)
    );
    
    if (tempDeviations.length > 0) {
      findings.push({
        category: 'Temperature Control',
        description: `Found ${tempDeviations.length} temperature deviation(s)`,
        severity: 'critical',
        recommendations: [
          'Review temperature monitoring procedures',
          'Check equipment calibration',
          'Implement corrective actions for affected products'
        ]
      });
    }
    
    // Low confidence scores
    const lowConfidenceEntries = entries.filter(e => 
      e.confidence_score && e.confidence_score < 0.7
    );
    
    if (lowConfidenceEntries.length > entries.length * 0.1) { // More than 10%
      findings.push({
        category: 'Data Quality',
        description: `High number of low-confidence voice entries (${lowConfidenceEntries.length})`,
        severity: 'warning',
        recommendations: [
          'Improve microphone setup in processing areas',
          'Provide additional voice training to staff',
          'Review background noise levels'
        ]
      });
    }
    
    // Pending reviews
    const pendingReviews = entries.filter(e => e.requires_review && !e.reviewed_by);
    if (pendingReviews.length > 0) {
      findings.push({
        category: 'Review Process',
        description: `${pendingReviews.length} entries require supervisor review`,
        severity: 'warning',
        recommendations: [
          'Assign reviewers to pending items',
          'Establish review deadlines',
          'Implement automated escalation for overdue reviews'
        ]
      });
    }
    
    return findings;
  }

  private async persistAuditEntry(entry: VoiceAuditEntry): Promise<string> {
    // Add to buffer for batch processing
    this.auditBuffer.push(entry);
    
    // Flush immediately for critical entries
    if (entry.risk_level === 'critical' || entry.event_type === 'error') {
      await this.flushAuditBuffer();
    }
    
    return entry.id || `temp_${Date.now()}`;
  }

  private async flushAuditBuffer(): Promise<void> {
    if (this.auditBuffer.length === 0) return;
    
    const entriesToFlush = [...this.auditBuffer];
    this.auditBuffer = [];
    
    try {
      const { error } = await supabase
        .from('voice_audit_trail')
        .insert(entriesToFlush);
      
      if (error) {
        console.error('Failed to persist audit entries:', error);
        // Re-add entries to buffer for retry
        this.auditBuffer.unshift(...entriesToFlush);
      } else {
        console.log(`✅ Persisted ${entriesToFlush.length} audit entries`);
      }
    } catch (error) {
      console.error('Error flushing audit buffer:', error);
      this.auditBuffer.unshift(...entriesToFlush);
    }
  }

  private startBatchFlushing(): void {
    this.flushInterval = setInterval(async () => {
      if (this.auditBuffer.length >= this.batchSize) {
        await this.flushAuditBuffer();
      }
    }, 5000); // Flush every 5 seconds if buffer has enough entries
  }

  private async storeComplianceReport(report: ComplianceReport): Promise<void> {
    try {
      const { error } = await supabase
        .from('compliance_reports')
        .insert({
          report_id: report.reportId,
          report_type: report.reportType,
          generated_at: report.generatedAt,
          period_start: report.period.startDate,
          period_end: report.period.endDate,
          summary: report.summary,
          findings: report.findings,
          digital_signature: report.digitalSignature,
          report_data: report // Store full report as JSON
        });
      
      if (error) {
        throw error;
      }
      
      console.log(`✅ Compliance report ${report.reportId} stored successfully`);
    } catch (error) {
      console.error('Failed to store compliance report:', error);
      throw error;
    }
  }

  private getDeviceInfo(): any {
    return {
      userAgent: navigator.userAgent,
      platform: navigator.platform,
      language: navigator.language,
      cookieEnabled: navigator.cookieEnabled,
      onlineStatus: navigator.onLine
    };
  }

  private getBrowserInfo(): any {
    return {
      name: this.getBrowserName(),
      version: this.getBrowserVersion(),
      viewport: {
        width: window.innerWidth,
        height: window.innerHeight
      },
      screen: {
        width: screen.width,
        height: screen.height,
        colorDepth: screen.colorDepth
      }
    };
  }

  private getBrowserName(): string {
    const userAgent = navigator.userAgent;
    if (userAgent.includes('Chrome')) return 'Chrome';
    if (userAgent.includes('Firefox')) return 'Firefox';
    if (userAgent.includes('Safari')) return 'Safari';
    if (userAgent.includes('Edge')) return 'Edge';
    return 'Unknown';
  }

  private getBrowserVersion(): string {
    const userAgent = navigator.userAgent;
    const version = userAgent.match(/(?:Chrome|Firefox|Safari|Edge)\/([0-9.]+)/);
    return version ? version[1] : 'Unknown';
  }

  private getRetentionPeriod(riskLevel: string): number {
    // Retention periods in days based on risk level and regulatory requirements
    switch (riskLevel) {
      case 'critical': return 2555; // 7 years
      case 'high': return 1095; // 3 years
      case 'medium': return 730; // 2 years
      case 'low': return 365; // 1 year
      default: return 365;
    }
  }

  /**
   * Cleanup resources
   */
  cleanup(): void {
    if (this.flushInterval) {
      clearInterval(this.flushInterval);
    }
    
    // Flush any remaining entries
    if (this.auditBuffer.length > 0) {
      this.flushAuditBuffer();
    }
    
    console.log('🔒 HACCP Voice Audit Trail System cleanup complete');
  }
}

// Export singleton instance
export const haccpVoiceAuditTrail = new HACCPVoiceAuditTrail();
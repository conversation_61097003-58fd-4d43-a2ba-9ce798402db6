import { VoiceEvent, VoiceEventFilters, VoiceEventResult, Supplier, InventorySnapshot } from '../types/schema';

/**
 * Multi-layer caching system for voice events
 * Implements in-memory cache with TTL and LRU eviction
 */

interface CacheEntry<T> {
  data: T;
  timestamp: number;
  ttl: number;
  accessCount: number;
  lastAccessed: number;
}

interface CacheStats {
  hits: number;
  misses: number;
  entries: number;
  memoryUsage: number;
  hitRate: number;
}

export class VoiceEventCache {
  private cache = new Map<string, CacheEntry<unknown>>();
  private maxSize: number;
  private defaultTTL: number;
  private stats: CacheStats;

  constructor(maxSize: number = 1000, defaultTTL: number = 5 * 60 * 1000) { // 5 minutes
    this.maxSize = maxSize;
    this.defaultTTL = defaultTTL;
    this.stats = {
      hits: 0,
      misses: 0,
      entries: 0,
      memoryUsage: 0,
      hitRate: 0
    };

    // Cleanup expired entries every minute
    setInterval(() => this.cleanup(), 60 * 1000);
  }

  /**
   * Generate cache key from parameters
   */
  private generateKey(prefix: string, params: Record<string, unknown>): string {
    const normalizedParams = JSON.stringify(params, Object.keys(params).sort());
    return `${prefix}:${btoa(normalizedParams)}`;
  }

  /**
   * Get item from cache
   */
  get<T>(key: string): T | null {
    const entry = this.cache.get(key);
    
    if (!entry) {
      this.stats.misses++;
      this.updateStats();
      return null;
    }

    // Check if expired
    if (Date.now() - entry.timestamp > entry.ttl) {
      this.cache.delete(key);
      this.stats.misses++;
      this.updateStats();
      return null;
    }

    // Update access info
    entry.accessCount++;
    entry.lastAccessed = Date.now();
    
    this.stats.hits++;
    this.updateStats();
    return entry.data as T;
  }

  /**
   * Set item in cache
   */
  set<T>(key: string, data: T, ttl?: number): void {
    // Evict LRU entries if at capacity
    if (this.cache.size >= this.maxSize) {
      this.evictLRU();
    }

    const entry: CacheEntry<T> = {
      data,
      timestamp: Date.now(),
      ttl: ttl ?? this.defaultTTL,
      accessCount: 1,
      lastAccessed: Date.now()
    };

    this.cache.set(key, entry);
    this.updateStats();
  }

  /**
   * Cache voice events with smart key generation
   */
  cacheVoiceEvents(filters: VoiceEventFilters, events: VoiceEvent[], ttl?: number): void {
    const key = this.generateKey('voice_events', filters as Record<string, unknown>);
    this.set(key, events, ttl);
  }

  /**
   * Get cached voice events
   */
  getCachedVoiceEvents(filters: VoiceEventFilters): VoiceEvent[] | null {
    const key = this.generateKey('voice_events', filters as Record<string, unknown>);
    return this.get<VoiceEvent[]>(key);
  }

  /**
   * Cache product search results
   */
  cacheProductSearch(query: string, results: unknown[], ttl?: number): void {
    const key = this.generateKey('product_search', { query: query.toLowerCase().trim() });
    this.set(key, results, ttl || 10 * 60 * 1000); // 10 minutes for product searches
  }

  /**
   * Get cached product search results
   */
  getCachedProductSearch(query: string): unknown[] | null {
    const key = this.generateKey('product_search', { query: query.toLowerCase().trim() });
    return this.get<unknown[]>(key);
  }

  /**
   * Cache vendor search results
   */
  cacheVendorSearch(query: string, results: unknown[], ttl?: number): void {
    const key = this.generateKey('vendor_search', { query: query.toLowerCase().trim() });
    this.set(key, results, ttl ?? 15 * 60 * 1000); // 15 minutes for vendor searches
  }

  /**
   * Get cached vendor search results
   */
  getCachedVendorSearch(query: string): Supplier[] | null {
    const key = this.generateKey('vendor_search', { query: query.toLowerCase().trim() });
    return this.get<Supplier[]>(key);
  }

  /**
   * Cache inventory status
   */
  cacheInventoryStatus(productName: string, status: InventorySnapshot, ttl?: number): void {
    const key = this.generateKey('inventory_status', { product: productName.toLowerCase().trim() });
    this.set(key, status, ttl ?? 2 * 60 * 1000); // 2 minutes for inventory status
  }

  /**
   * Get cached inventory status
   */
  getCachedInventoryStatus(productName: string): InventorySnapshot | null {
    const key = this.generateKey('inventory_status', { product: productName.toLowerCase().trim() });
    return this.get<InventorySnapshot>(key);
  }

  /**
   * Cache voice processing results
   */
  cacheVoiceProcessingResult(audioHash: string, result: VoiceEventResult, ttl?: number): void {
    const key = this.generateKey('voice_processing', { hash: audioHash });
    this.set(key, result, ttl ?? 30 * 60 * 1000); // 30 minutes for voice processing
  }

  /**
   * Get cached voice processing result
   */
  getCachedVoiceProcessingResult(audioHash: string): VoiceEventResult | null {
    const key = this.generateKey('voice_processing', { hash: audioHash });
    return this.get<VoiceEventResult>(key);
  }

  /**
   * Invalidate related caches when data changes
   */
  invalidateVoiceEventCaches(): void {
    const keysToDelete: string[] = [];
    
    for (const key of this.cache.keys()) {
      if (key.startsWith('voice_events:') || key.startsWith('inventory_status:')) {
        keysToDelete.push(key);
      }
    }

    keysToDelete.forEach(key => this.cache.delete(key));
    this.updateStats();
  }

  /**
   * Invalidate product-related caches
   */
  invalidateProductCaches(): void {
    const keysToDelete: string[] = [];
    
    for (const key of this.cache.keys()) {
      if (key.startsWith('product_search:') || key.startsWith('inventory_status:')) {
        keysToDelete.push(key);
      }
    }

    keysToDelete.forEach(key => this.cache.delete(key));
    this.updateStats();
  }

  /**
   * Evict least recently used entries
   */
  private evictLRU(): void {
    let oldestKey: string | null = null;
    let oldestTime = Date.now();

    for (const [key, entry] of this.cache.entries()) {
      if (entry.lastAccessed < oldestTime) {
        oldestTime = entry.lastAccessed;
        oldestKey = key;
      }
    }

    if (oldestKey) {
      this.cache.delete(oldestKey);
    }
  }

  /**
   * Clean up expired entries
   */
  private cleanup(): void {
    const now = Date.now();
    const keysToDelete: string[] = [];

    for (const [key, entry] of this.cache.entries()) {
      if (now - entry.timestamp > entry.ttl) {
        keysToDelete.push(key);
      }
    }

    keysToDelete.forEach(key => this.cache.delete(key));
    
    if (keysToDelete.length > 0) {
      console.log(`Cache cleanup: removed ${keysToDelete.length} expired entries`);
      this.updateStats();
    }
  }

  /**
   * Update cache statistics
   */
  private updateStats(): void {
    this.stats.entries = this.cache.size;
    this.stats.hitRate = this.stats.hits / (this.stats.hits + this.stats.misses) || 0;
    
    // Rough memory estimation
    this.stats.memoryUsage = this.cache.size * 1024; // Rough estimate in bytes
  }

  /**
   * Get cache statistics
   */
  getStats(): CacheStats {
    return { ...this.stats };
  }

  /**
   * Clear all cache entries
   */
  clear(): void {
    this.cache.clear();
    this.stats = {
      hits: 0,
      misses: 0,
      entries: 0,
      memoryUsage: 0,
      hitRate: 0
    };
  }

  /**
   * Get cache size
   */
  size(): number {
    return this.cache.size;
  }

  /**
   * Preload frequently accessed data
   */
  async preloadCommonData(): Promise<void> {
    // This would be called during app initialization to cache common queries
    console.log('Preloading common voice event data...');
    
    // Example: Cache recent events, common products, active vendors
    // Implementation would depend on your specific use patterns
  }
}

// Global cache instance
export const voiceEventCache = new VoiceEventCache();

/**
 * Audio processing cache for duplicate detection
 */
export class AudioProcessingCache {
  private audioHashes = new Map<string, { result: VoiceEventResult; timestamp: number }>();
  private readonly TTL = 30 * 60 * 1000; // 30 minutes

  /**
   * Generate hash for audio blob
   */
  async generateAudioHash(audioBlob: Blob): Promise<string> {
    const arrayBuffer = await audioBlob.arrayBuffer();
    const hashBuffer = await crypto.subtle.digest('SHA-256', arrayBuffer);
    const hashArray = Array.from(new Uint8Array(hashBuffer));
    return hashArray.map(b => b.toString(16).padStart(2, '0')).join('');
  }

  /**
   * Check if audio has been processed recently
   */
  async checkDuplicate(audioBlob: Blob): Promise<any | null> {
    const hash = await this.generateAudioHash(audioBlob);
    const cached = this.audioHashes.get(hash);
    
    if (cached && Date.now() - cached.timestamp < this.TTL) {
      console.log('Duplicate audio detected, returning cached result');
      return cached.result;
    }

    return null;
  }

  /**
   * Cache audio processing result
   */
  async cacheResult(audioBlob: Blob, result: VoiceEventResult): Promise<void> {
    const hash = await this.generateAudioHash(audioBlob);
    this.audioHashes.set(hash, {
      result,
      timestamp: Date.now()
    });

    // Cleanup old entries
    this.cleanup();
  }

  /**
   * Clean up expired audio hashes
   */
  private cleanup(): void {
    const now = Date.now();
    for (const [hash, entry] of this.audioHashes.entries()) {
      if (now - entry.timestamp > this.TTL) {
        this.audioHashes.delete(hash);
      }
    }
  }

  /**
   * Clear all cached audio processing results
   */
  clear(): void {
    this.audioHashes.clear();
  }
}

export const audioProcessingCache = new AudioProcessingCache();
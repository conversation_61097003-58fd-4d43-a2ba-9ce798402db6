/**
 * Advanced Voice Error Recovery System with Offline Fallback
 * Production-grade error handling and graceful degradation for voice processing
 */

import { VoiceErrorHandler } from './VoiceErrorHandler';
import { voiceEventService } from '../modules/voice-event-storage';

interface ErrorRecoveryConfig {
  maxRetryAttempts: number;
  retryDelayMs: number;
  exponentialBackoff: boolean;
  offlineModeEnabled: boolean;
  fallbackToSpeechRecognition: boolean;
  cacheFailedRequests: boolean;
  networkCheckInterval: number;
}

interface RetryState {
  attempt: number;
  maxAttempts: number;
  baseDelay: number;
  exponentialBackoff: boolean;
  lastError?: Error;
  startTime: number;
}

interface OfflineQueueItem {
  id: string;
  timestamp: number;
  audioData?: ArrayBuffer;
  transcript?: string;
  extractedData?: any;
  priority: 'high' | 'medium' | 'low';
  retries: number;
  originalError: string;
}

interface NetworkStatus {
  isOnline: boolean;
  connectionQuality: 'excellent' | 'good' | 'poor' | 'offline';
  latency: number;
  lastCheck: number;
}

/**
 * Advanced error recovery system with intelligent fallbacks and offline support
 */
export class AdvancedVoiceErrorRecovery {
  private config: ErrorRecoveryConfig;
  private errorHandler: VoiceErrorHandler;
  private offlineQueue: Map<string, OfflineQueueItem> = new Map();
  private networkStatus: NetworkStatus;
  private fallbackRecognition: SpeechRecognition | null = null;
  private localSeafoodTerms: Map<string, any> = new Map();
  private networkCheckTimer?: NodeJS.Timeout;
  private processingOfflineQueue = false;

  // Error classification and recovery strategies
  private errorStrategies = new Map<string, (error: Error, context: any) => Promise<any>>();
  private errorMetrics = {
    totalErrors: 0,
    recoveredErrors: 0,
    fallbackUsage: 0,
    offlineProcessing: 0,
    networkFailures: 0,
    audioErrors: 0,
    apiLimitErrors: 0
  };

  constructor(config: Partial<ErrorRecoveryConfig> = {}) {
    this.config = {
      maxRetryAttempts: config.maxRetryAttempts ?? 3,
      retryDelayMs: config.retryDelayMs ?? 1000,
      exponentialBackoff: config.exponentialBackoff ?? true,
      offlineModeEnabled: config.offlineModeEnabled ?? true,
      fallbackToSpeechRecognition: config.fallbackToSpeechRecognition ?? true,
      cacheFailedRequests: config.cacheFailedRequests ?? true,
      networkCheckInterval: config.networkCheckInterval ?? 30000
    };

    this.errorHandler = new VoiceErrorHandler();
    this.networkStatus = {
      isOnline: navigator.onLine,
      connectionQuality: 'good',
      latency: 0,
      lastCheck: Date.now()
    };

    this.initializeErrorStrategies();
    this.initializeFallbackRecognition();
    this.initializeLocalSeafoodDatabase();
    this.startNetworkMonitoring();
    this.setupEventListeners();

    console.log('🛡️ Advanced Voice Error Recovery System initialized');
  }

  /**
   * Main error recovery orchestrator
   */
  async handleVoiceError(
    error: Error, 
    context: any, 
    audioData?: ArrayBuffer
  ): Promise<{ success: boolean; data?: any; fallbackUsed?: boolean; queuedOffline?: boolean }> {
    
    this.errorMetrics.totalErrors++;
    
    try {
      // Classify error and determine strategy
      const errorType = this.classifyError(error);
      const strategy = this.errorStrategies.get(errorType);
      
      console.log(`🔄 Handling ${errorType} error:`, error.message);

      if (strategy) {
        // Try specific error recovery strategy
        const result = await strategy(error, { ...context, audioData });
        if (result.success) {
          this.errorMetrics.recoveredErrors++;
          return result;
        }
      }

      // If specific strategy failed, try general recovery methods
      return await this.generalErrorRecovery(error, context, audioData);

    } catch (recoveryError) {
      console.error('Error recovery failed:', recoveryError);
      return { success: false, fallbackUsed: false };
    }
  }

  /**
   * Initialize error-specific recovery strategies
   */
  private initializeErrorStrategies(): void {
    // Network connectivity errors
    this.errorStrategies.set('network_error', async (error, context) => {
      return await this.handleNetworkError(error, context);
    });

    // API rate limiting errors
    this.errorStrategies.set('rate_limit_error', async (error, context) => {
      return await this.handleRateLimitError(error, context);
    });

    // Audio processing errors
    this.errorStrategies.set('audio_error', async (error, context) => {
      return await this.handleAudioError(error, context);
    });

    // OpenAI API errors
    this.errorStrategies.set('openai_api_error', async (error, context) => {
      return await this.handleOpenAIAPIError(error, context);
    });

    // Microphone permission errors
    this.errorStrategies.set('permission_error', async (error, context) => {
      return await this.handlePermissionError(error, context);
    });

    // WebRTC connection errors
    this.errorStrategies.set('webrtc_error', async (error, context) => {
      return await this.handleWebRTCError(error, context);
    });
  }

  /**
   * Network error recovery with intelligent offline handling
   */
  private async handleNetworkError(error: Error, context: any): Promise<any> {
    this.errorMetrics.networkFailures++;
    
    // Update network status
    await this.checkNetworkStatus();
    
    if (!this.networkStatus.isOnline && this.config.offlineModeEnabled) {
      // Queue for offline processing
      return await this.queueForOfflineProcessing(error, context);
    }

    if (this.networkStatus.connectionQuality === 'poor') {
      // Use reduced-quality processing for poor connections
      return await this.processWithReducedQuality(context);
    }

    // Retry with exponential backoff
    return await this.retryWithBackoff(context, {
      attempt: 0,
      maxAttempts: this.config.maxRetryAttempts,
      baseDelay: this.config.retryDelayMs,
      exponentialBackoff: this.config.exponentialBackoff,
      startTime: Date.now()
    });
  }

  /**
   * Rate limit error recovery with intelligent queuing
   */
  private async handleRateLimitError(error: Error, context: any): Promise<any> {
    this.errorMetrics.apiLimitErrors++;
    
    // Extract retry-after header if available
    const retryAfter = this.extractRetryAfter(error);
    const delay = retryAfter || (this.config.retryDelayMs * 5); // Default to 5x delay

    console.log(`⏱️ Rate limited, retrying after ${delay}ms`);

    // Queue request for later processing
    if (this.config.cacheFailedRequests) {
      await this.queueForRetryAfterDelay(context, delay);
    }

    // Use fallback processing while waiting
    return await this.useFallbackProcessing(context);
  }

  /**
   * Audio error recovery with fallback processing
   */
  private async handleAudioError(error: Error, context: any): Promise<any> {
    this.errorMetrics.audioErrors++;
    
    if (error.message.includes('microphone') || error.message.includes('permission')) {
      return await this.handlePermissionError(error, context);
    }

    if (error.message.includes('buffer') || error.message.includes('overrun')) {
      // Reset audio pipeline and try again
      return await this.resetAudioPipelineAndRetry(context);
    }

    // Fall back to browser speech recognition
    if (this.config.fallbackToSpeechRecognition) {
      return await this.useFallbackProcessing(context);
    }

    return { success: false };
  }

  /**
   * OpenAI API error recovery
   */
  private async handleOpenAIAPIError(error: Error, context: any): Promise<any> {
    if (error.message.includes('insufficient_quota')) {
      // Switch to local processing
      return await this.useLocalProcessing(context);
    }

    if (error.message.includes('model_overloaded')) {
      // Retry with different model or reduced parameters
      return await this.retryWithFallbackModel(context);
    }

    return await this.useFallbackProcessing(context);
  }

  /**
   * Permission error recovery with user guidance
   */
  private async handlePermissionError(error: Error, context: any): Promise<any> {
    // Provide user guidance for permission issues
    const guidance = this.generatePermissionGuidance(error);
    
    // Try to re-request permissions
    try {
      const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
      stream.getTracks().forEach(track => track.stop()); // Clean up test stream
      
      // Permissions granted, retry original operation
      return await this.retryWithBackoff(context, {
        attempt: 0,
        maxAttempts: 1,
        baseDelay: 0,
        exponentialBackoff: false,
        startTime: Date.now()
      });
    } catch {
      return { 
        success: false, 
        error: 'Microphone permission denied',
        userGuidance: guidance 
      };
    }
  }

  /**
   * WebRTC connection error recovery
   */
  private async handleWebRTCError(error: Error, context: any): Promise<any> {
    // Try to re-establish WebRTC connection
    try {
      // Implementation would attempt to reconnect WebRTC
      console.log('🔄 Attempting WebRTC reconnection...');
      
      // For now, fall back to HTTP-based processing
      return await this.useFallbackProcessing(context);
    } catch {
      return await this.useLocalProcessing(context);
    }
  }

  /**
   * General error recovery when specific strategies fail
   */
  private async generalErrorRecovery(error: Error, context: any, audioData?: ArrayBuffer): Promise<any> {
    // Try fallback processing first
    if (this.config.fallbackToSpeechRecognition) {
      const fallbackResult = await this.useFallbackProcessing(context);
      if (fallbackResult.success) {
        return { ...fallbackResult, fallbackUsed: true };
      }
    }

    // Queue for offline processing if enabled
    if (this.config.offlineModeEnabled && this.config.cacheFailedRequests) {
      const queueResult = await this.queueForOfflineProcessing(error, context, audioData);
      return { ...queueResult, queuedOffline: true };
    }

    // Final fallback: return structured error
    return {
      success: false,
      error: error.message,
      timestamp: new Date().toISOString(),
      context: context.component || 'voice_processing'
    };
  }

  /**
   * Queue requests for offline processing
   */
  private async queueForOfflineProcessing(
    error: Error, 
    context: any, 
    audioData?: ArrayBuffer
  ): Promise<any> {
    const queueItem: OfflineQueueItem = {
      id: `offline_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      timestamp: Date.now(),
      audioData,
      transcript: context.transcript,
      extractedData: context.extractedData,
      priority: this.determinePriority(context),
      retries: 0,
      originalError: error.message
    };

    this.offlineQueue.set(queueItem.id, queueItem);
    this.errorMetrics.offlineProcessing++;

    console.log(`📦 Queued item for offline processing: ${queueItem.id}`);

    // Try to process with local resources
    const localResult = await this.processWithLocalResources(queueItem);
    if (localResult.success) {
      this.offlineQueue.delete(queueItem.id);
      return localResult;
    }

    return {
      success: false,
      queued: true,
      queueId: queueItem.id,
      message: 'Request queued for processing when connection is restored'
    };
  }

  /**
   * Use fallback browser speech recognition
   */
  private async useFallbackProcessing(context: any): Promise<any> {
    this.errorMetrics.fallbackUsage++;

    if (!this.fallbackRecognition) {
      return { success: false, error: 'No fallback recognition available' };
    }

    try {
      console.log('🔄 Using fallback speech recognition...');
      
      return new Promise((resolve) => {
        this.fallbackRecognition!.onresult = (event) => {
          const transcript = event.results[0][0].transcript;
          const confidence = event.results[0][0].confidence;
          
          // Process with local seafood terminology
          const extractedData = this.processWithLocalTerminology(transcript);
          
          resolve({
            success: true,
            transcript,
            confidence,
            extractedData,
            fallbackUsed: true,
            processingMethod: 'browser_speech_recognition'
          });
        };

        this.fallbackRecognition!.onerror = () => {
          resolve({ success: false, error: 'Fallback recognition failed' });
        };

        this.fallbackRecognition!.start();
      });

    } catch (error) {
      console.error('Fallback processing failed:', error);
      return { success: false, error: 'Fallback processing failed' };
    }
  }

  /**
   * Process with local resources (offline mode)
   */
  private async processWithLocalResources(queueItem: OfflineQueueItem): Promise<any> {
    try {
      const transcript = queueItem.transcript;
      
      // If we have audio data but no transcript, try local recognition
      if (!transcript && queueItem.audioData) {
        // Would implement local audio-to-text processing here
        // For now, we'll skip this advanced feature
        return { success: false, error: 'Local audio processing not available' };
      }

      if (transcript) {
        const extractedData = this.processWithLocalTerminology(transcript);
        
        if (extractedData.confidence > 0.7) {
          // Store locally and sync when online
          await this.storeForLaterSync({
            transcript,
            extractedData,
            timestamp: queueItem.timestamp,
            offlineProcessed: true
          });

          return {
            success: true,
            transcript,
            extractedData,
            processingMethod: 'offline_local',
            confidence: extractedData.confidence
          };
        }
      }

      return { success: false, error: 'Insufficient local processing capability' };

    } catch (error) {
      console.error('Local processing failed:', error);
      return { success: false, error: 'Local processing failed' };
    }
  }

  /**
   * Process transcript using local seafood terminology database
   */
  private processWithLocalTerminology(transcript: string): any {
    const lowerTranscript = transcript.toLowerCase();
    let confidence = 0.5;
    const extractedData: any = {
      species: null,
      quantity: null,
      unit: null,
      eventType: null
    };

    // Species detection using local database
    for (const [term, data] of this.localSeafoodTerms) {
      if (lowerTranscript.includes(term)) {
        extractedData.species = data;
        confidence += 0.2;
        break;
      }
    }

    // Quantity extraction
    const quantityMatch = transcript.match(/(\d+(?:\.\d+)?)\s*(pounds?|lbs?|kg|cases?|units?)/i);
    if (quantityMatch) {
      extractedData.quantity = parseFloat(quantityMatch[1]);
      extractedData.unit = quantityMatch[2].toLowerCase().replace(/s$/, '');
      confidence += 0.2;
    }

    // Event type detection
    const eventTypes = [
      { pattern: /(receiv|add|incoming)/i, type: 'receiving' },
      { pattern: /(dispos|waste|throw)/i, type: 'disposal' },
      { pattern: /(count|inventory)/i, type: 'physical_count' },
      { pattern: /(sale|sell|sold)/i, type: 'sale' }
    ];

    for (const { pattern, type } of eventTypes) {
      if (pattern.test(lowerTranscript)) {
        extractedData.eventType = type;
        confidence += 0.1;
        break;
      }
    }

    return {
      ...extractedData,
      confidence: Math.min(confidence, 1.0),
      processingMethod: 'local_terminology'
    };
  }

  /**
   * Initialize local seafood terminology database for offline use
   */
  private initializeLocalSeafoodDatabase(): void {
    // Subset of most common seafood terms for offline processing
    const commonSeafoodTerms = [
      { term: 'salmon', data: { species: 'salmon', category: 'finfish', commonUnits: ['lbs', 'kg'] }},
      { term: 'cod', data: { species: 'cod', category: 'finfish', commonUnits: ['lbs', 'kg'] }},
      { term: 'halibut', data: { species: 'halibut', category: 'finfish', commonUnits: ['lbs', 'kg'] }},
      { term: 'crab', data: { species: 'crab', category: 'crustaceans', commonUnits: ['lbs', 'cases'] }},
      { term: 'lobster', data: { species: 'lobster', category: 'crustaceans', commonUnits: ['lbs', 'pieces'] }},
      { term: 'shrimp', data: { species: 'shrimp', category: 'crustaceans', commonUnits: ['lbs', 'kg'] }},
      { term: 'scallops', data: { species: 'scallops', category: 'shellfish', commonUnits: ['lbs', 'kg'] }},
      { term: 'oysters', data: { species: 'oysters', category: 'shellfish', commonUnits: ['dozens', 'bags'] }},
      { term: 'clams', data: { species: 'clams', category: 'shellfish', commonUnits: ['lbs', 'bags'] }},
      { term: 'tuna', data: { species: 'tuna', category: 'finfish', commonUnits: ['lbs', 'loins'] }}
    ];

    commonSeafoodTerms.forEach(({ term, data }) => {
      this.localSeafoodTerms.set(term, data);
    });

    console.log(`📚 Loaded ${commonSeafoodTerms.length} terms for offline processing`);
  }

  /**
   * Initialize fallback speech recognition
   */
  private initializeFallbackRecognition(): void {
    if ('webkitSpeechRecognition' in window || 'SpeechRecognition' in window) {
      const SpeechRecognitionClass = window.webkitSpeechRecognition || window.SpeechRecognition;
      this.fallbackRecognition = new SpeechRecognitionClass();
      
      this.fallbackRecognition.continuous = false;
      this.fallbackRecognition.interimResults = false;
      this.fallbackRecognition.lang = 'en-US';
      
      console.log('🎤 Fallback speech recognition initialized');
    }
  }

  /**
   * Network monitoring and status management
   */
  private startNetworkMonitoring(): void {
    // Monitor online/offline status
    window.addEventListener('online', () => {
      this.networkStatus.isOnline = true;
      console.log('🌐 Network connection restored');
      this.processOfflineQueue();
    });

    window.addEventListener('offline', () => {
      this.networkStatus.isOnline = false;
      this.networkStatus.connectionQuality = 'offline';
      console.log('📴 Network connection lost - switching to offline mode');
    });

    // Periodic network quality checks
    this.networkCheckTimer = setInterval(() => {
      this.checkNetworkStatus();
    }, this.config.networkCheckInterval);
  }

  /**
   * Check network status and quality
   */
  private async checkNetworkStatus(): Promise<void> {
    const startTime = Date.now();
    
    try {
      // Test network latency with a small request
      await fetch('/api/health', { 
        method: 'HEAD',
        cache: 'no-cache'
      });
      
      const latency = Date.now() - startTime;
      this.networkStatus.latency = latency;
      this.networkStatus.lastCheck = Date.now();
      this.networkStatus.isOnline = true;

      // Classify connection quality based on latency
      if (latency < 100) {
        this.networkStatus.connectionQuality = 'excellent';
      } else if (latency < 300) {
        this.networkStatus.connectionQuality = 'good';
      } else {
        this.networkStatus.connectionQuality = 'poor';
      }

    } catch (error) {
      this.networkStatus.isOnline = false;
      this.networkStatus.connectionQuality = 'offline';
    }
  }

  /**
   * Process queued items when network is restored
   */
  private async processOfflineQueue(): Promise<void> {
    if (this.processingOfflineQueue || this.offlineQueue.size === 0) {
      return;
    }

    this.processingOfflineQueue = true;
    console.log(`📦 Processing ${this.offlineQueue.size} queued items...`);

    const items = Array.from(this.offlineQueue.values())
      .sort((a, b) => {
        // Sort by priority, then by timestamp
        const priorityOrder = { high: 3, medium: 2, low: 1 };
        const aPriority = priorityOrder[a.priority];
        const bPriority = priorityOrder[b.priority];
        
        if (aPriority !== bPriority) {
          return bPriority - aPriority;
        }
        
        return a.timestamp - b.timestamp;
      });

    for (const item of items) {
      try {
        // Re-attempt processing
        const result = await this.reprocessQueuedItem(item);
        
        if (result.success) {
          this.offlineQueue.delete(item.id);
          console.log(`✅ Successfully processed queued item: ${item.id}`);
        } else {
          item.retries++;
          if (item.retries >= this.config.maxRetryAttempts) {
            this.offlineQueue.delete(item.id);
            console.log(`❌ Max retries exceeded for item: ${item.id}`);
          }
        }

      } catch (error) {
        console.error(`Error processing queued item ${item.id}:`, error);
        item.retries++;
      }

      // Small delay between processing items to avoid overwhelming the system
      await new Promise(resolve => setTimeout(resolve, 100));
    }

    this.processingOfflineQueue = false;
    console.log('✅ Offline queue processing complete');
  }

  /**
   * Utility methods
   */
  private classifyError(error: Error): string {
    const message = error.message.toLowerCase();
    
    if (message.includes('network') || message.includes('fetch')) {
      return 'network_error';
    }
    if (message.includes('rate limit') || message.includes('quota')) {
      return 'rate_limit_error';
    }
    if (message.includes('audio') || message.includes('microphone')) {
      return 'audio_error';
    }
    if (message.includes('openai') || message.includes('api')) {
      return 'openai_api_error';
    }
    if (message.includes('permission') || message.includes('denied')) {
      return 'permission_error';
    }
    if (message.includes('webrtc') || message.includes('connection')) {
      return 'webrtc_error';
    }
    
    return 'unknown_error';
  }

  private determinePriority(context: any): 'high' | 'medium' | 'low' {
    // HACCP-related events are high priority
    if (context.eventType === 'haccp' || context.temperature) {
      return 'high';
    }
    
    // Receiving and disposal events are medium priority
    if (context.eventType === 'receiving' || context.eventType === 'disposal') {
      return 'medium';
    }
    
    return 'low';
  }

  private extractRetryAfter(error: Error): number | null {
    // Extract retry-after value from error message or headers
    const match = error.message.match(/retry.after.(\d+)/i);
    return match ? parseInt(match[1]) * 1000 : null; // Convert to milliseconds
  }

  private generatePermissionGuidance(error: Error): string {
    return `To use voice features:
1. Click the microphone icon in your browser's address bar
2. Select "Always allow" for microphone access
3. Refresh the page and try again
4. If issues persist, check your browser settings or contact support`;
  }

  private async retryWithBackoff(context: any, retryState: RetryState): Promise<any> {
    // Implementation would retry the original operation with exponential backoff
    return { success: false, error: 'Retry with backoff not implemented' };
  }

  private async processWithReducedQuality(context: any): Promise<any> {
    // Implementation would use reduced-quality processing for poor connections
    return { success: false, error: 'Reduced quality processing not implemented' };
  }

  private async queueForRetryAfterDelay(context: any, delay: number): Promise<void> {
    // Implementation would queue requests for retry after rate limit delay
    console.log(`⏳ Queuing request for retry after ${delay}ms`);
  }

  private async resetAudioPipelineAndRetry(context: any): Promise<any> {
    // Implementation would reset audio processing pipeline
    return { success: false, error: 'Audio pipeline reset not implemented' };
  }

  private async useLocalProcessing(context: any): Promise<any> {
    return await this.processWithLocalTerminology(context.transcript || '');
  }

  private async retryWithFallbackModel(context: any): Promise<any> {
    // Implementation would retry with a different model
    return { success: false, error: 'Fallback model retry not implemented' };
  }

  private async reprocessQueuedItem(item: OfflineQueueItem): Promise<any> {
    // Implementation would re-attempt processing of queued items
    return { success: false, error: 'Queue reprocessing not implemented' };
  }

  private async storeForLaterSync(data: any): Promise<void> {
    // Implementation would store data locally for later synchronization
    console.log('💾 Storing data for later sync:', data);
  }

  private setupEventListeners(): void {
    // Listen for custom events that might indicate errors
    document.addEventListener('voiceProcessingError', (event) => {
      const detail = (event as CustomEvent).detail;
      this.handleVoiceError(detail.error, detail.context, detail.audioData);
    });
  }

  /**
   * Public API methods
   */
  
  /**
   * Get current error recovery metrics
   */
  getMetrics(): any {
    return {
      ...this.errorMetrics,
      queueSize: this.offlineQueue.size,
      networkStatus: this.networkStatus,
      recoveryRate: this.errorMetrics.totalErrors > 0 
        ? (this.errorMetrics.recoveredErrors / this.errorMetrics.totalErrors) * 100 
        : 0
    };
  }

  /**
   * Get offline queue status
   */
  getOfflineQueueStatus(): any {
    const items = Array.from(this.offlineQueue.values());
    return {
      totalItems: items.length,
      highPriority: items.filter(i => i.priority === 'high').length,
      mediumPriority: items.filter(i => i.priority === 'medium').length,
      lowPriority: items.filter(i => i.priority === 'low').length,
      oldestItem: items.length > 0 ? Math.min(...items.map(i => i.timestamp)) : null,
      processingActive: this.processingOfflineQueue
    };
  }

  /**
   * Manually trigger offline queue processing
   */
  async triggerOfflineQueueProcessing(): Promise<void> {
    if (this.networkStatus.isOnline) {
      await this.processOfflineQueue();
    }
  }

  /**
   * Clear offline queue (emergency cleanup)
   */
  clearOfflineQueue(): void {
    this.offlineQueue.clear();
    console.log('🗑️ Offline queue cleared');
  }

  /**
   * Cleanup resources
   */
  cleanup(): void {
    if (this.networkCheckTimer) {
      clearInterval(this.networkCheckTimer);
    }
    
    this.clearOfflineQueue();
    console.log('🛡️ Advanced Voice Error Recovery System cleanup complete');
  }
}

// Export singleton instance
export const voiceErrorRecovery = new AdvancedVoiceErrorRecovery();
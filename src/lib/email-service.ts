/**
 * Email Service for Temperature Reports and Alerts
 * 
 * Handles automated email reporting, alert notifications, and scheduled
 * HACCP compliance reports with professional templates and delivery tracking.
 */

import type {
  EmailReportConfig,
  TemperatureReportData,
  TemperatureAlert,
  Sensor,
  TemperatureReading
} from '../types/tempstick';

interface EmailServiceConfig {
  provider: 'sendgrid' | 'aws-ses' | 'smtp';
  apiKey: string;
  fromEmail: string;
  fromName: string;
  replyTo?: string;
}

interface EmailTemplate {
  subject: string;
  html: string;
  text: string;
}

interface EmailJobStatus {
  id: string;
  status: 'pending' | 'sending' | 'sent' | 'failed';
  recipient: string;
  subject: string;
  scheduledAt: Date;
  sentAt?: Date;
  error?: string;
  retryCount: number;
}

/**
 * Professional Email Service for Temperature Monitoring
 */
export class EmailService {
  private static instance: EmailService;
  private config: EmailServiceConfig;
  private emailQueue: EmailJobStatus[] = [];
  private isProcessing = false;

  constructor(config: EmailServiceConfig) {
    this.config = config;
  }

  static getInstance(config?: EmailServiceConfig): EmailService {
    if (!EmailService.instance) {
      if (!config) {
        throw new Error('EmailService config required for first initialization');
      }
      EmailService.instance = new EmailService(config);
    }
    return EmailService.instance;
  }

  /**
   * Send temperature alert email
   */
  async sendAlertEmail(
    alert: TemperatureAlert,
    sensor: Sensor,
    recipients: string[]
  ): Promise<void> {
    try {
      const template = this.generateAlertEmailTemplate(alert, sensor);
      
      for (const recipient of recipients) {
        await this.queueEmail({
          to: recipient,
          subject: template.subject,
          html: template.html,
          text: template.text,
          priority: alert.severity_level === 'critical' ? 'high' : 'normal'
        });
      }

      console.log(`📧 Alert email queued for ${recipients.length} recipients`);
    } catch (error) {
      console.error('Failed to send alert email:', error);
      throw error;
    }
  }

  /**
   * Send scheduled temperature report
   */
  async sendScheduledReport(
    reportData: TemperatureReportData,
    config: EmailReportConfig
  ): Promise<void> {
    try {
      const template = this.generateReportEmailTemplate(reportData, config);
      
      for (const recipient of config.recipients) {
        if (recipient.trim()) {
          await this.queueEmail({
            to: recipient,
            subject: template.subject,
            html: template.html,
            text: template.text,
            attachments: await this.generateReportAttachments(reportData, config)
          });
        }
      }

      console.log(`📊 Report email queued for ${config.recipients.length} recipients`);
    } catch (error) {
      console.error('Failed to send scheduled report:', error);
      throw error;
    }
  }

  /**
   * Generate alert email template
   */
  private generateAlertEmailTemplate(
    alert: TemperatureAlert,
    sensor: Sensor
  ): EmailTemplate {
    const alertType = alert.alert_type.replace(/_/g, ' ').toUpperCase();
    const severity = alert.severity_level.toUpperCase();
    const isHACCP = alert.alert_type === 'haccp_violation';
    
    const subject = `${isHACCP ? '🚨 HACCP ' : '⚠️ '}Temperature Alert: ${alertType} - ${sensor.name}`;
    
    const html = `
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>${subject}</title>
    <style>
        body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; margin: 0; padding: 20px; background-color: #f5f5f5; }
        .container { max-width: 600px; margin: 0 auto; background: white; border-radius: 8px; overflow: hidden; box-shadow: 0 2px 8px rgba(0,0,0,0.1); }
        .header { background: ${isHACCP ? '#dc2626' : severity === 'CRITICAL' ? '#dc2626' : '#f59e0b'}; color: white; padding: 20px; text-align: center; }
        .content { padding: 30px; }
        .alert-details { background: #f8f9fa; padding: 20px; border-radius: 6px; margin: 20px 0; }
        .detail-row { display: flex; justify-content: space-between; margin: 8px 0; padding: 4px 0; border-bottom: 1px solid #e5e7eb; }
        .detail-label { font-weight: 600; color: #374151; }
        .detail-value { color: #6b7280; }
        .temperature { font-size: 24px; font-weight: bold; color: ${alert.temperature && alert.temperature > 50 ? '#dc2626' : '#2563eb'}; }
        .actions { background: #fef3c7; padding: 20px; border-radius: 6px; margin: 20px 0; }
        .actions h3 { color: #92400e; margin-top: 0; }
        .actions ul { color: #92400e; }
        .footer { background: #f8f9fa; padding: 20px; text-align: center; color: #6b7280; font-size: 14px; }
        .severity-badge { display: inline-block; padding: 4px 12px; border-radius: 20px; font-size: 12px; font-weight: bold; text-transform: uppercase; }
        .severity-critical { background: #fee2e2; color: #991b1b; }
        .severity-high { background: #fed7aa; color: #9a3412; }
        .severity-medium { background: #fef3c7; color: #92400e; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1 style="margin: 0; font-size: 28px;">${isHACCP ? '🚨' : '⚠️'} Temperature Alert</h1>
            <p style="margin: 10px 0 0 0; opacity: 0.9; font-size: 18px;">${alertType}</p>
        </div>
        
        <div class="content">
            <div class="alert-details">
                <div class="detail-row">
                    <span class="detail-label">Sensor:</span>
                    <span class="detail-value"><strong>${sensor.name}</strong></span>
                </div>
                <div class="detail-row">
                    <span class="detail-label">Location:</span>
                    <span class="detail-value">${sensor.location}</span>
                </div>
                <div class="detail-row">
                    <span class="detail-label">Temperature:</span>
                    <span class="detail-value temperature">${alert.temperature ? `${alert.temperature.toFixed(1)}°F` : 'N/A'}</span>
                </div>
                <div class="detail-row">
                    <span class="detail-label">Severity:</span>
                    <span class="severity-badge severity-${severity.toLowerCase()}">${severity}</span>
                </div>
                <div class="detail-row">
                    <span class="detail-label">Time:</span>
                    <span class="detail-value">${new Date(alert.alert_timestamp).toLocaleString()}</span>
                </div>
                ${sensor.storage_areas?.name ? `
                <div class="detail-row">
                    <span class="detail-label">Storage Area:</span>
                    <span class="detail-value">${sensor.storage_areas.name}</span>
                </div>
                ` : ''}
                ${sensor.storage_areas?.haccp_control_point ? `
                <div class="detail-row">
                    <span class="detail-label">HACCP Status:</span>
                    <span class="detail-value" style="color: #dc2626; font-weight: bold;">Critical Control Point</span>
                </div>
                ` : ''}
            </div>

            ${this.getEmailActionContent(alert)}
            
            <p style="color: #6b7280; font-size: 14px; margin-top: 30px;">
                This alert was generated automatically by the Seafood Manager temperature monitoring system. 
                Please take appropriate action and document any corrective measures.
            </p>
        </div>
        
        <div class="footer">
            <p>Pacific Cloud Seafoods - Seafood Manager</p>
            <p>Automated Temperature Monitoring System</p>
        </div>
    </div>
</body>
</html>`;

    const text = `
TEMPERATURE ALERT: ${alertType}

Sensor: ${sensor.name}
Location: ${sensor.location}
Temperature: ${alert.temperature ? `${alert.temperature.toFixed(1)}°F` : 'N/A'}
Severity: ${severity}
Time: ${new Date(alert.alert_timestamp).toLocaleString()}
${sensor.storage_areas?.name ? `Storage Area: ${sensor.storage_areas.name}` : ''}
${sensor.storage_areas?.haccp_control_point ? 'HACCP: Critical Control Point' : ''}

${this.getTextActionContent(alert)}

This alert was generated automatically by the Seafood Manager temperature monitoring system.
Please take appropriate action and document any corrective measures.

-- Pacific Cloud Seafoods - Seafood Manager --
`;

    return { subject, html, text };
  }

  /**
   * Generate report email template
   */
  private generateReportEmailTemplate(
    reportData: TemperatureReportData,
    config: EmailReportConfig
  ): EmailTemplate {
    const period = `${reportData.summary.reportPeriod.start} - ${reportData.summary.reportPeriod.end}`;
    const subject = `Temperature Report (${config.frequency.toUpperCase()}) - ${period}`;
    
    const html = `
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>${subject}</title>
    <style>
        body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; margin: 0; padding: 20px; background-color: #f5f5f5; }
        .container { max-width: 800px; margin: 0 auto; background: white; border-radius: 8px; overflow: hidden; box-shadow: 0 2px 8px rgba(0,0,0,0.1); }
        .header { background: linear-gradient(135deg, #3b82f6, #1e40af); color: white; padding: 30px; text-align: center; }
        .content { padding: 30px; }
        .summary-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin: 30px 0; }
        .summary-card { background: #f8f9fa; padding: 20px; border-radius: 8px; text-align: center; }
        .summary-value { font-size: 36px; font-weight: bold; margin: 10px 0; }
        .summary-label { color: #6b7280; font-size: 14px; }
        .section { margin: 40px 0; }
        .section h2 { color: #374151; border-bottom: 2px solid #e5e7eb; padding-bottom: 10px; }
        .sensor-table { width: 100%; border-collapse: collapse; margin: 20px 0; }
        .sensor-table th, .sensor-table td { padding: 12px; text-align: left; border-bottom: 1px solid #e5e7eb; }
        .sensor-table th { background: #f8f9fa; font-weight: 600; }
        .alert-row { background: #fef2f2; }
        .compliance-good { color: #059669; font-weight: bold; }
        .compliance-poor { color: #dc2626; font-weight: bold; }
        .footer { background: #f8f9fa; padding: 20px; text-align: center; color: #6b7280; font-size: 14px; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1 style="margin: 0; font-size: 32px;">📊 Temperature Report</h1>
            <p style="margin: 15px 0 0 0; opacity: 0.9; font-size: 18px;">${config.frequency.toUpperCase()} REPORT</p>
            <p style="margin: 10px 0 0 0; opacity: 0.8;">${period}</p>
        </div>
        
        <div class="content">
            <div class="summary-grid">
                <div class="summary-card">
                    <div class="summary-value" style="color: #3b82f6;">${reportData.summary.sensorsIncluded}</div>
                    <div class="summary-label">Sensors Monitored</div>
                </div>
                <div class="summary-card">
                    <div class="summary-value" style="color: #059669;">${reportData.summary.totalReadings}</div>
                    <div class="summary-label">Total Readings</div>
                </div>
                <div class="summary-card">
                    <div class="summary-value" style="color: ${reportData.summary.totalAlerts > 0 ? '#dc2626' : '#059669'};">${reportData.summary.totalAlerts}</div>
                    <div class="summary-label">Total Alerts</div>
                </div>
                <div class="summary-card">
                    <div class="summary-value" style="color: ${reportData.summary.complianceRate >= 95 ? '#059669' : reportData.summary.complianceRate >= 90 ? '#f59e0b' : '#dc2626'};">${reportData.summary.complianceRate.toFixed(1)}%</div>
                    <div class="summary-label">Compliance Rate</div>
                </div>
            </div>

            <div class="section">
                <h2>Sensor Performance</h2>
                <table class="sensor-table">
                    <thead>
                        <tr>
                            <th>Sensor</th>
                            <th>Location</th>
                            <th>Avg Temp</th>
                            <th>Range</th>
                            <th>Readings</th>
                            <th>Alerts</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${reportData.sensorData.map(sensor => `
                        <tr${sensor.alerts.length > 0 ? ' class="alert-row"' : ''}>
                            <td><strong>${sensor.sensor.name}</strong></td>
                            <td>${sensor.sensor.location}</td>
                            <td>${sensor.statistics.avgTemp.toFixed(1)}°F</td>
                            <td>${sensor.statistics.minTemp.toFixed(1)}°F - ${sensor.statistics.maxTemp.toFixed(1)}°F</td>
                            <td>${sensor.statistics.readingsCount}</td>
                            <td>${sensor.statistics.alertsCount}</td>
                        </tr>
                        `).join('')}
                    </tbody>
                </table>
            </div>

            ${reportData.haccpCompliance.length > 0 ? `
            <div class="section">
                <h2>HACCP Compliance</h2>
                <table class="sensor-table">
                    <thead>
                        <tr>
                            <th>Storage Area</th>
                            <th>Compliance Rate</th>
                            <th>Violations</th>
                            <th>Critical Violations</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${reportData.haccpCompliance.map(area => `
                        <tr>
                            <td><strong>${area.storageArea.name}</strong></td>
                            <td class="${area.complianceRate >= 95 ? 'compliance-good' : 'compliance-poor'}">${area.complianceRate.toFixed(1)}%</td>
                            <td>${area.violationsCount}</td>
                            <td>${area.criticalViolations.length}</td>
                        </tr>
                        `).join('')}
                    </tbody>
                </table>
            </div>
            ` : ''}

            ${reportData.alerts.length > 0 ? `
            <div class="section">
                <h2>Recent Alerts</h2>
                <p style="color: #6b7280;">Showing ${Math.min(10, reportData.alerts.length)} most recent alerts:</p>
                <table class="sensor-table">
                    <thead>
                        <tr>
                            <th>Time</th>
                            <th>Alert Type</th>
                            <th>Sensor</th>
                            <th>Temperature</th>
                            <th>Severity</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${reportData.alerts.slice(0, 10).map(alert => `
                        <tr>
                            <td>${new Date(alert.alert_timestamp).toLocaleString()}</td>
                            <td>${alert.alert_type.replace(/_/g, ' ')}</td>
                            <td>${alert.sensors?.name || 'Unknown'}</td>
                            <td>${alert.temperature ? `${alert.temperature.toFixed(1)}°F` : 'N/A'}</td>
                            <td><span style="color: ${alert.severity_level === 'critical' ? '#dc2626' : alert.severity_level === 'high' ? '#f59e0b' : '#6b7280'}">${alert.severity_level.toUpperCase()}</span></td>
                        </tr>
                        `).join('')}
                    </tbody>
                </table>
            </div>
            ` : ''}

            <p style="color: #6b7280; font-size: 14px; margin-top: 40px;">
                This report was generated automatically by the Seafood Manager temperature monitoring system.
                For questions or issues, please contact your system administrator.
            </p>
        </div>
        
        <div class="footer">
            <p>Pacific Cloud Seafoods - Seafood Manager</p>
            <p>Automated Temperature Monitoring & Reporting</p>
        </div>
    </div>
</body>
</html>`;

    const text = `
TEMPERATURE REPORT (${config.frequency.toUpperCase()})
Period: ${period}

SUMMARY:
- Sensors Monitored: ${reportData.summary.sensorsIncluded}
- Total Readings: ${reportData.summary.totalReadings}
- Total Alerts: ${reportData.summary.totalAlerts}
- Compliance Rate: ${reportData.summary.complianceRate.toFixed(1)}%

SENSOR PERFORMANCE:
${reportData.sensorData.map(sensor => 
  `${sensor.sensor.name} (${sensor.sensor.location}): Avg ${sensor.statistics.avgTemp.toFixed(1)}°F, ${sensor.statistics.alertsCount} alerts`
).join('\n')}

${reportData.alerts.length > 0 ? `
RECENT ALERTS (${Math.min(10, reportData.alerts.length)} shown):
${reportData.alerts.slice(0, 10).map(alert => 
  `${new Date(alert.alert_timestamp).toLocaleString()}: ${alert.alert_type.replace(/_/g, ' ')} - ${alert.sensors?.name} (${alert.severity_level.toUpperCase()})`
).join('\n')}
` : ''}

This report was generated automatically by the Seafood Manager temperature monitoring system.

-- Pacific Cloud Seafoods - Seafood Manager --
`;

    return { subject, html, text };
  }

  /**
   * Get action content for alert emails
   */
  private getEmailActionContent(alert: TemperatureAlert): string {
    switch (alert.alert_type) {
      case 'high_temp':
        return `
            <div class="actions">
                <h3>Recommended Immediate Actions:</h3>
                <ul>
                    <li>Check refrigeration system operation immediately</li>
                    <li>Verify door seals and ensure doors are properly closed</li>
                    <li>Move products to backup storage if necessary</li>
                    <li>Contact refrigeration service if problem persists</li>
                    <li>Document all corrective actions taken in HACCP log</li>
                </ul>
            </div>`;
      
      case 'low_temp':
        return `
            <div class="actions">
                <h3>Recommended Immediate Actions:</h3>
                <ul>
                    <li>Check if freezer unit is functioning within specifications</li>
                    <li>Verify temperature sensor calibration accuracy</li>
                    <li>Ensure products are not frozen when they should be refrigerated</li>
                    <li>Assess any potential quality impact on stored products</li>
                    <li>Document findings and actions in monitoring log</li>
                </ul>
            </div>`;
      
      case 'haccp_violation':
        return `
            <div class="actions" style="background: #fee2e2; border-left: 4px solid #dc2626;">
                <h3 style="color: #dc2626;">⚠️ HACCP CORRECTIVE ACTION REQUIRED</h3>
                <ul style="color: #dc2626;">
                    <li><strong>STOP all operations in the affected area immediately</strong></li>
                    <li><strong>Quarantine all affected products</strong></li>
                    <li>Contact HACCP coordinator or food safety manager</li>
                    <li>Document violation and corrective actions in HACCP log</li>
                    <li>Determine product disposition (use immediately, rework, or discard)</li>
                    <li>Investigate root cause of temperature deviation</li>
                    <li>Implement preventive measures to avoid recurrence</li>
                </ul>
            </div>`;
      
      default:
        return `
            <div class="actions">
                <h3>Recommended Actions:</h3>
                <ul>
                    <li>Investigate the cause of the temperature alert</li>
                    <li>Take appropriate corrective action based on your SOPs</li>
                    <li>Document all actions taken in your monitoring records</li>
                    <li>Monitor the situation closely until resolved</li>
                </ul>
            </div>`;
    }
  }

  /**
   * Get text action content for plain text emails
   */
  private getTextActionContent(alert: TemperatureAlert): string {
    switch (alert.alert_type) {
      case 'haccp_violation':
        return `
⚠️ HACCP CORRECTIVE ACTION REQUIRED:
• STOP all operations in the affected area immediately
• Quarantine all affected products
• Contact HACCP coordinator or food safety manager
• Document violation and corrective actions in HACCP log
• Determine product disposition (use immediately, rework, or discard)
• Investigate root cause and implement preventive measures`;
      
      default:
        return `
Recommended Actions:
• Investigate the cause of the temperature alert
• Take appropriate corrective action based on your SOPs
• Document all actions taken in your monitoring records
• Monitor the situation closely until resolved`;
    }
  }

  /**
   * Generate report attachments
   */
  private async generateReportAttachments(
    reportData: TemperatureReportData,
    config: EmailReportConfig
  ): Promise<Array<{ filename: string; content: Buffer; contentType: string }>> {
    const attachments: Array<{ filename: string; content: Buffer; contentType: string }> = [];
    
    // TODO: Implement PDF generation
    if (config.includePDF) {
      // attachments.push({
      //   filename: 'temperature-report.pdf',
      //   content: await generatePDFReport(reportData),
      //   contentType: 'application/pdf'
      // });
    }
    
    // TODO: Implement Excel generation
    if (config.includeExcel) {
      // attachments.push({
      //   filename: 'temperature-data.xlsx',
      //   content: await generateExcelReport(reportData),
      //   contentType: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
      // });
    }
    
    return attachments;
  }

  /**
   * Queue email for delivery
   */
  private async queueEmail(email: {
    to: string;
    subject: string;
    html: string;
    text: string;
    priority?: 'high' | 'normal' | 'low';
    attachments?: Array<{ filename: string; content: Buffer; contentType: string }>;
  }): Promise<void> {
    const job: EmailJobStatus = {
      id: `email_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      status: 'pending',
      recipient: email.to,
      subject: email.subject,
      scheduledAt: new Date(),
      retryCount: 0
    };

    this.emailQueue.push(job);
    
    // Process queue if not already processing
    if (!this.isProcessing) {
      this.processEmailQueue();
    }
  }

  /**
   * Process email queue with retry logic
   */
  private async processEmailQueue(): Promise<void> {
    if (this.isProcessing) return;
    this.isProcessing = true;

    try {
      while (this.emailQueue.length > 0) {
        const job = this.emailQueue.shift();
        if (!job) continue;

        try {
          job.status = 'sending';
          
          // TODO: Implement actual email sending based on provider
          await this.sendEmailViaProvider(job);
          
          job.status = 'sent';
          job.sentAt = new Date();
          
          console.log(`📧 Email sent successfully: ${job.subject} to ${job.recipient}`);
          
        } catch (error) {
          job.retryCount++;
          job.error = error instanceof Error ? error.message : 'Unknown error';
          
          if (job.retryCount < 3) {
            // Retry with exponential backoff
            setTimeout(() => {
              job.status = 'pending';
              this.emailQueue.push(job);
            }, Math.pow(2, job.retryCount) * 1000);
            
            console.log(`📧 Email retry queued: ${job.subject} (attempt ${job.retryCount + 1})`);
          } else {
            job.status = 'failed';
            console.error(`📧 Email failed permanently: ${job.subject}`, error);
          }
        }

        // Rate limiting - wait between emails
        await new Promise(resolve => setTimeout(resolve, 100));
      }
    } finally {
      this.isProcessing = false;
    }
  }

  /**
   * Send email via configured provider
   */
  private async sendEmailViaProvider(job: EmailJobStatus): Promise<void> {
    // TODO: Implement actual email providers
    switch (this.config.provider) {
      case 'sendgrid':
        // await this.sendViaSendGrid(job);
        break;
      case 'aws-ses':
        // await this.sendViaAWSSES(job);
        break;
      case 'smtp':
        // await this.sendViaSMTP(job);
        break;
      default:
        throw new Error(`Unsupported email provider: ${this.config.provider}`);
    }
    
    // For now, simulate email sending
    await new Promise(resolve => setTimeout(resolve, 100));
    console.log(`📧 [SIMULATED] Email sent: ${job.subject} to ${job.recipient}`);
  }

  /**
   * Get email service status
   */
  getStatus(): {
    queueLength: number;
    isProcessing: boolean;
    recentJobs: EmailJobStatus[];
  } {
    return {
      queueLength: this.emailQueue.length,
      isProcessing: this.isProcessing,
      recentJobs: this.emailQueue.slice(-10)
    };
  }
}

// Initialize email service with environment configuration
let emailService: EmailService | null = null;

export function getEmailService(): EmailService {
  if (!emailService) {
    const config: EmailServiceConfig = {
      provider: (process.env.EMAIL_PROVIDER as any) || 'smtp',
      apiKey: process.env.EMAIL_API_KEY || '',
      fromEmail: process.env.EMAIL_FROM || '<EMAIL>',
      fromName: process.env.EMAIL_FROM_NAME || 'Seafood Manager',
      replyTo: process.env.EMAIL_REPLY_TO
    };

    emailService = EmailService.getInstance(config);
  }

  return emailService;
}

export { EmailService };
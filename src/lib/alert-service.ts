/**
 * Temperature Alert Service
 * 
 * Handles alert notification, escalation, and automated response
 * for temperature monitoring and HACCP compliance violations.
 */

import { supabase } from './supabase';
import type {
  TemperatureAlert,
  AlertConfig,
  AlertNotification,
  Sensor,
  StorageArea,
  EmailReportConfig
} from '../types/tempstick';

interface NotificationChannel {
  type: 'email' | 'sms' | 'push' | 'webhook';
  endpoint: string;
  enabled: boolean;
}

interface EscalationRule {
  delayMinutes: number;
  recipients: string[];
  channels: NotificationChannel[];
  requiresAcknowledgment: boolean;
}

/**
 * Alert Service for Temperature Monitoring
 */
export class AlertService {
  private static instance: AlertService;
  private escalationTimers = new Map<string, NodeJS.Timeout>();
  
  static getInstance(): AlertService {
    if (!AlertService.instance) {
      AlertService.instance = new AlertService();
    }
    return AlertService.instance;
  }

  /**
   * Process new temperature alert and trigger notifications
   */
  async processAlert(alertId: string): Promise<void> {
    try {
      // Fetch alert with sensor and storage area data
      const { data: alert, error } = await supabase
        .from('temperature_alerts')
        .select(`
          *,
          sensors (
            *
          )
        `)
        .eq('id', alertId)
        .single();

      if (error || !alert) {
        console.error('Failed to fetch alert:', error);
        return;
      }

      // Get alert configuration for this sensor
      const alertConfig = await this.getAlertConfig(alert.sensor_id);
      
      // Send immediate notifications
      await this.sendImmediateNotifications(alert, alertConfig);
      
      // Setup escalation if required
      if (alert.severity_level === 'critical' || alert.severity_level === 'high') {
        await this.setupEscalation(alert, alertConfig);
      }
      
      // Mark notification as sent
      await supabase
        .from('temperature_alerts')
        .update({ notification_sent: true })
        .eq('id', alertId);
        
    } catch (error) {
      console.error('Failed to process alert:', error);
    }
  }

  /**
   * Send immediate alert notifications
   */
  private async sendImmediateNotifications(
    alert: TemperatureAlert & { sensors: Sensor & { storage_areas: StorageArea } },
    config: AlertConfig
  ): Promise<void> {
    const message = this.generateAlertMessage(alert);
    
    // Email notifications
    if (config.notifications.email) {
      await this.sendEmailAlert(alert, message);
    }
    
    // SMS notifications (if configured)
    if (config.notifications.sms) {
      await this.sendSMSAlert(alert, message);
    }
    
    // Push notifications (if configured)  
    if (config.notifications.push) {
      await this.sendPushAlert(alert, message);
    }
    
    // Webhook notifications
    for (const webhook of config.notifications.webhooks) {
      await this.sendWebhookAlert(alert, webhook, message);
    }
  }

  /**
   * Setup alert escalation timer
   */
  private async setupEscalation(
    alert: TemperatureAlert,
    config: AlertConfig
  ): Promise<void> {
    const escalationRules = config.escalation;
    
    if (!escalationRules.delays.length) return;
    
    // Clear any existing escalation for this alert
    this.clearEscalation(alert.id);
    
    // Setup first escalation level
    const firstDelay = escalationRules.delays[0];
    const timer = setTimeout(async () => {
      await this.processEscalation(alert.id, 0, escalationRules);
    }, firstDelay * 60 * 1000); // Convert minutes to milliseconds
    
    this.escalationTimers.set(alert.id, timer);
  }

  /**
   * Process escalation level
   */
  private async processEscalation(
    alertId: string,
    escalationLevel: number,
    rules: AlertConfig['escalation']
  ): Promise<void> {
    try {
      // Check if alert is still active
      const { data: alert, error } = await supabase
        .from('temperature_alerts')
        .select(`
          *,
          sensors (
            *
          )
        `)
        .eq('id', alertId)
        .is('resolved_timestamp', null)
        .single();

      if (error || !alert) {
        // Alert resolved or doesn't exist, clear escalation
        this.clearEscalation(alertId);
        return;
      }

      // Send escalation notifications
      const recipients = rules.recipients[escalationLevel] || [];
      const message = this.generateEscalationMessage(alert, escalationLevel + 1);
      
      await this.sendEscalationNotifications(alert, recipients, message);
      
      // Setup next escalation level if available
      const nextLevel = escalationLevel + 1;
      if (nextLevel < rules.delays.length) {
        const nextDelay = rules.delays[nextLevel];
        const timer = setTimeout(async () => {
          await this.processEscalation(alertId, nextLevel, rules);
        }, nextDelay * 60 * 1000);
        
        this.escalationTimers.set(alertId, timer);
      }
      
    } catch (error) {
      console.error('Failed to process escalation:', error);
    }
  }

  /**
   * Clear escalation timer for resolved alert
   */
  clearEscalation(alertId: string): void {
    const timer = this.escalationTimers.get(alertId);
    if (timer) {
      clearTimeout(timer);
      this.escalationTimers.delete(alertId);
    }
  }

  /**
   * Generate alert message
   */
  private generateAlertMessage(
    alert: TemperatureAlert & { sensors: Sensor & { storage_areas: StorageArea } }
  ): string {
    const sensor = alert.sensors;
    const storageArea = sensor.storage_areas;
    const alertType = alert.alert_type.replace(/_/g, ' ').toUpperCase();
    
    let message = `🚨 TEMPERATURE ALERT: ${alertType}\n\n`;
    message += `Sensor: ${sensor.name}\n`;
    message += `Location: ${sensor.location}\n`;
    
    if (storageArea) {
      message += `Storage Area: ${storageArea.name}\n`;
      if (storageArea.haccp_control_point) {
        message += `⚠️ HACCP CRITICAL CONTROL POINT\n`;
      }
    }
    
    if (alert.temperature) {
      message += `Temperature: ${alert.temperature.toFixed(1)}°F\n`;
    }
    
    message += `Severity: ${alert.severity_level.toUpperCase()}\n`;
    message += `Time: ${new Date(alert.alert_timestamp).toLocaleString()}\n\n`;
    
    // Add recommended actions based on alert type
    message += this.getRecommendedActions(alert);
    
    return message;
  }

  /**
   * Generate escalation message
   */
  private generateEscalationMessage(
    alert: TemperatureAlert,
    escalationLevel: number
  ): string {
    const baseMessage = this.generateAlertMessage(alert as any);
    return `🔥 ESCALATED ALERT - LEVEL ${escalationLevel}\n\n${baseMessage}\n\n⚠️ This alert has not been resolved and requires immediate attention!`;
  }

  /**
   * Get recommended actions based on alert type
   */
  private getRecommendedActions(alert: TemperatureAlert): string {
    switch (alert.alert_type) {
      case 'high_temp':
        return `Recommended Actions:
• Check refrigeration system immediately
• Verify door seals are intact
• Move products to backup storage if necessary
• Document corrective actions taken`;

      case 'low_temp':
        return `Recommended Actions:
• Check if freezer is functioning properly
• Verify temperature sensor calibration
• Ensure products are not frozen when they shouldn't be
• Document any product quality impacts`;

      case 'haccp_violation':
        return `Recommended Actions:
• IMMEDIATE HACCP CORRECTIVE ACTION REQUIRED
• Stop all operations in affected area
• Quarantine affected products
• Contact HACCP coordinator
• Document all actions in HACCP log
• Consider product disposition (use, rework, or discard)`;

      case 'sensor_offline':
        return `Recommended Actions:
• Check sensor power and connectivity
• Verify network connection
• Replace sensor if necessary
• Use backup monitoring method
• Document monitoring gap`;

      default:
        return 'Please investigate and take appropriate corrective action.';
    }
  }

  /**
   * Send email alert
   */
  private async sendEmailAlert(
    alert: TemperatureAlert,
    message: string
  ): Promise<void> {
    try {
      // TODO: Implement email service integration (SendGrid, AWS SES, etc.)
      console.log('📧 Email alert sent:', {
        alert: alert.id,
        message: `${message.substring(0, 100)  }...`
      });
      
      // For now, log to console - implement actual email service later
      // Example with SendGrid:
      // await sendGridClient.send({
      //   to: recipients,
      //   from: '<EMAIL>',
      //   subject: `Temperature Alert: ${alert.alert_type}`,
      //   text: message,
      //   html: this.formatEmailHTML(message)
      // });
      
    } catch (error) {
      console.error('Failed to send email alert:', error);
    }
  }

  /**
   * Send SMS alert
   */
  private async sendSMSAlert(
    alert: TemperatureAlert,
    message: string
  ): Promise<void> {
    try {
      // TODO: Implement SMS service integration (Twilio, AWS SNS, etc.)
      console.log('📱 SMS alert sent:', {
        alert: alert.id,
        message: `${message.substring(0, 50)  }...`
      });
      
    } catch (error) {
      console.error('Failed to send SMS alert:', error);
    }
  }

  /**
   * Send push notification alert
   */
  private async sendPushAlert(
    alert: TemperatureAlert,
    message: string
  ): Promise<void> {
    try {
      // TODO: Implement push notification service
      console.log('🔔 Push alert sent:', {
        alert: alert.id,
        message: `${message.substring(0, 50)  }...`
      });
      
    } catch (error) {
      console.error('Failed to send push alert:', error);
    }
  }

  /**
   * Send webhook alert
   */
  private async sendWebhookAlert(
    alert: TemperatureAlert,
    webhook: string,
    message: string
  ): Promise<void> {
    try {
      const payload = {
        alert_id: alert.id,
        alert_type: alert.alert_type,
        severity: alert.severity_level,
        temperature: alert.temperature,
        timestamp: alert.alert_timestamp,
        sensor_id: alert.sensor_id,
        message
      };

      const response = await fetch(webhook, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(payload)
      });

      if (!response.ok) {
        throw new Error(`Webhook failed: ${response.status} ${response.statusText}`);
      }

      console.log('🔗 Webhook alert sent successfully');
      
    } catch (error) {
      console.error('Failed to send webhook alert:', error);
    }
  }

  /**
   * Send escalation notifications
   */
  private async sendEscalationNotifications(
    alert: TemperatureAlert,
    recipients: string[],
    message: string
  ): Promise<void> {
    // For now, use email for escalation notifications
    console.log('📈 Escalation notifications sent to:', recipients);
    console.log('Message:', `${message.substring(0, 100)  }...`);
  }

  /**
   * Get alert configuration for sensor
   */
  private async getAlertConfig(sensorId: string): Promise<AlertConfig> {
    // TODO: Fetch from database or use default configuration
    // For now, return default configuration
    return {
      sensorId,
      thresholds: {},
      notifications: {
        email: true,
        sms: false,
        push: true,
        webhooks: []
      },
      escalation: {
        delays: [15, 30, 60], // 15 min, 30 min, 1 hour
        recipients: [
          ['<EMAIL>'],
          ['<EMAIL>', '<EMAIL>'],
          ['<EMAIL>', '<EMAIL>', '<EMAIL>']
        ]
      }
    };
  }

  /**
   * Create alert notification for UI
   */
  createNotification(alert: TemperatureAlert, sensor: Sensor, storageArea?: StorageArea): AlertNotification {
    const message = this.generateAlertMessage({ ...alert, sensors: { ...sensor, storage_areas: storageArea } } as any);
    
    return {
      alert,
      sensor,
      storageArea,
      message,
      actions: [
        {
          label: 'Acknowledge',
          action: () => this.acknowledgeAlert(alert.id)
        },
        {
          label: 'Resolve',
          action: () => this.resolveAlert(alert.id)
        },
        {
          label: 'Escalate',
          action: () => this.escalateAlert(alert.id)
        }
      ]
    };
  }

  /**
   * Acknowledge alert (stops escalation but keeps alert active)
   */
  async acknowledgeAlert(alertId: string): Promise<void> {
    try {
      this.clearEscalation(alertId);
      
      // TODO: Add acknowledged_at timestamp to database schema
      console.log('✅ Alert acknowledged:', alertId);
      
    } catch (error) {
      console.error('Failed to acknowledge alert:', error);
    }
  }

  /**
   * Resolve alert
   */
  async resolveAlert(alertId: string): Promise<void> {
    try {
      this.clearEscalation(alertId);
      
      const { error } = await supabase
        .from('temperature_alerts')
        .update({ resolved_timestamp: new Date().toISOString() })
        .eq('id', alertId);

      if (error) throw error;
      
      console.log('✅ Alert resolved:', alertId);
      
    } catch (error) {
      console.error('Failed to resolve alert:', error);
      throw error;
    }
  }

  /**
   * Escalate alert immediately
   */
  async escalateAlert(alertId: string): Promise<void> {
    try {
      // Clear existing escalation
      this.clearEscalation(alertId);
      
      // Trigger immediate escalation
      const alertConfig = await this.getAlertConfig(''); // TODO: Get actual sensor ID
      await this.processEscalation(alertId, 0, alertConfig.escalation);
      
      console.log('📈 Alert escalated:', alertId);
      
    } catch (error) {
      console.error('Failed to escalate alert:', error);
    }
  }

  /**
   * Get alert statistics for dashboard
   */
  async getAlertStatistics(timeRange: 'day' | 'week' | 'month' = 'day'): Promise<{
    total: number;
    critical: number;
    high: number;
    resolved: number;
    avgResolutionTime: number;
    haccpViolations: number;
  }> {
    try {
      const now = new Date();
      const startTime = new Date();
      
      switch (timeRange) {
        case 'day':
          startTime.setDate(now.getDate() - 1);
          break;
        case 'week':
          startTime.setDate(now.getDate() - 7);
          break;
        case 'month':
          startTime.setMonth(now.getMonth() - 1);
          break;
      }

      const { data: alerts, error } = await supabase
        .from('temperature_alerts')
        .select('*')
        .gte('alert_timestamp', startTime.toISOString());

      if (error) throw error;

      const total = alerts.length;
      const critical = alerts.filter(a => a.severity_level === 'critical').length;
      const high = alerts.filter(a => a.severity_level === 'high').length;
      const resolved = alerts.filter(a => a.resolved_timestamp).length;
      const haccpViolations = alerts.filter(a => a.alert_type === 'haccp_violation').length;
      
      // Calculate average resolution time
      const resolvedAlerts = alerts.filter(a => a.resolved_timestamp);
      const avgResolutionTime = resolvedAlerts.length > 0 
        ? resolvedAlerts.reduce((sum, alert) => {
            const alertTime = new Date(alert.alert_timestamp).getTime();
            const resolvedTime = new Date(alert.resolved_timestamp).getTime();
            return sum + (resolvedTime - alertTime);
          }, 0) / resolvedAlerts.length / 1000 / 60 // Convert to minutes
        : 0;

      return {
        total,
        critical,
        high,
        resolved,
        avgResolutionTime,
        haccpViolations
      };
      
    } catch (error) {
      console.error('Failed to get alert statistics:', error);
      return {
        total: 0,
        critical: 0,
        high: 0,
        resolved: 0,
        avgResolutionTime: 0,
        haccpViolations: 0
      };
    }
  }
}

// Export singleton instance
export const alertService = AlertService.getInstance();

// Setup Supabase real-time subscription for new alerts
if (typeof window !== 'undefined') {
  supabase
    .channel('temperature_alerts_notifications')
    .on(
      'postgres_changes',
      { event: 'INSERT', schema: 'public', table: 'temperature_alerts' },
      (payload) => {
        const newAlert = payload.new as TemperatureAlert;
        alertService.processAlert(newAlert.id);
      }
    )
    .subscribe();
}
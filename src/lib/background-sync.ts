/**
 * Background Sync Service
 * 
 * Handles periodic synchronization of temperature data from TempStick API,
 * background job scheduling, and system health monitoring.
 */

import { tempStickService } from './tempstick-service';
import { alertService } from './alert-service';
import { supabase } from './supabase';

interface SyncJob {
  id: string;
  name: string;
  interval: number; // milliseconds
  lastRun?: Date;
  nextRun?: Date;
  isRunning: boolean;
  enabled: boolean;
  handler: () => Promise<void>;
}

interface SyncStatus {
  lastSyncTime: Date | null;
  syncInProgress: boolean;
  successCount: number;
  errorCount: number;
  lastError?: string;
  systemHealth: 'healthy' | 'degraded' | 'down';
}

/**
 * Background Sync Manager
 */
export class BackgroundSyncManager {
  private static instance: BackgroundSyncManager;
  private jobs: Map<string, SyncJob> = new Map();
  private intervals: Map<string, NodeJS.Timeout> = new Map();
  private status: SyncStatus = {
    lastSyncTime: null,
    syncInProgress: false,
    successCount: 0,
    errorCount: 0,
    systemHealth: 'healthy'
  };

  static getInstance(): BackgroundSyncManager {
    if (!BackgroundSyncManager.instance) {
      BackgroundSyncManager.instance = new BackgroundSyncManager();
    }
    return BackgroundSyncManager.instance;
  }

  /**
   * Initialize background sync services
   */
  initialize(): void {
    console.log('🔄 Initializing background sync services...');
    
    // Register sync jobs
    this.registerJob({
      id: 'temperature-sync',
      name: 'Temperature Data Sync',
      interval: 5 * 60 * 1000, // 5 minutes
      enabled: true,
      isRunning: false,
      handler: this.syncTemperatureData.bind(this)
    });

    this.registerJob({
      id: 'sensor-sync',
      name: 'Sensor Configuration Sync',
      interval: 30 * 60 * 1000, // 30 minutes
      enabled: true,
      isRunning: false,
      handler: this.syncSensors.bind(this)
    });

    this.registerJob({
      id: 'system-health',
      name: 'System Health Check',
      interval: 2 * 60 * 1000, // 2 minutes
      enabled: true,
      isRunning: false,
      handler: this.checkSystemHealth.bind(this)
    });

    this.registerJob({
      id: 'alert-cleanup',
      name: 'Alert Cleanup',
      interval: 60 * 60 * 1000, // 1 hour
      enabled: true,
      isRunning: false,
      handler: this.cleanupResolvedAlerts.bind(this)
    });

    // Start all enabled jobs
    this.startAllJobs();
    
    // Setup browser visibility change handling
    if (typeof window !== 'undefined') {
      document.addEventListener('visibilitychange', this.handleVisibilityChange.bind(this));
    }
  }

  /**
   * Register a background job
   */
  registerJob(job: Omit<SyncJob, 'nextRun'>): void {
    const nextRun = new Date(Date.now() + job.interval);
    this.jobs.set(job.id, { ...job, nextRun });
    console.log(`📋 Registered job: ${job.name} (${job.interval / 1000}s interval)`);
  }

  /**
   * Start a specific job
   */
  startJob(jobId: string): void {
    const job = this.jobs.get(jobId);
    if (!job) {
      console.error(`Job not found: ${jobId}`);
      return;
    }

    if (!job.enabled) {
      console.log(`Job disabled: ${job.name}`);
      return;
    }

    // Clear existing interval if any
    this.stopJob(jobId);

    // Run immediately
    this.runJob(jobId);

    // Schedule recurring execution
    const interval = setInterval(() => {
      this.runJob(jobId);
    }, job.interval);

    this.intervals.set(jobId, interval);
    console.log(`▶️ Started job: ${job.name}`);
  }

  /**
   * Stop a specific job
   */
  stopJob(jobId: string): void {
    const interval = this.intervals.get(jobId);
    if (interval) {
      clearInterval(interval);
      this.intervals.delete(jobId);
      
      const job = this.jobs.get(jobId);
      if (job) {
        console.log(`⏹️ Stopped job: ${job.name}`);
      }
    }
  }

  /**
   * Start all enabled jobs
   */
  startAllJobs(): void {
    for (const [jobId, job] of this.jobs) {
      if (job.enabled) {
        this.startJob(jobId);
      }
    }
  }

  /**
   * Stop all jobs
   */
  stopAllJobs(): void {
    for (const jobId of this.jobs.keys()) {
      this.stopJob(jobId);
    }
  }

  /**
   * Run a specific job
   */
  private async runJob(jobId: string): Promise<void> {
    const job = this.jobs.get(jobId);
    if (!job || job.isRunning) return;

    try {
      job.isRunning = true;
      job.lastRun = new Date();
      job.nextRun = new Date(Date.now() + job.interval);

      console.log(`🏃 Running job: ${job.name}`);
      await job.handler();
      
      this.status.successCount++;
      console.log(`✅ Completed job: ${job.name}`);
      
    } catch (error) {
      this.status.errorCount++;
      this.status.lastError = error instanceof Error ? error.message : 'Unknown error';
      console.error(`❌ Job failed: ${job.name}`, error);
      
      // Update system health on repeated failures
      if (this.status.errorCount > 5) {
        this.status.systemHealth = 'degraded';
      }
      
    } finally {
      job.isRunning = false;
    }
  }

  /**
   * Sync temperature data from TempStick API
   */
  private async syncTemperatureData(): Promise<void> {
    try {
      this.status.syncInProgress = true;
      await tempStickService.scheduledSync();
      this.status.lastSyncTime = new Date();
      
      // Reset system health on successful sync
      if (this.status.systemHealth !== 'healthy') {
        this.status.systemHealth = 'healthy';
        console.log('🎉 System health restored to healthy');
      }
      
    } catch (error) {
      console.error('Temperature sync failed:', error);
      throw error;
    } finally {
      this.status.syncInProgress = false;
    }
  }

  /**
   * Sync sensor configuration from TempStick API
   */
  private async syncSensors(): Promise<void> {
    try {
      await tempStickService.syncSensors();
    } catch (error) {
      console.error('Sensor sync failed:', error);
      throw error;
    }
  }

  /**
   * Check system health
   */
  private async checkSystemHealth(): Promise<void> {
    try {
      // Check database connectivity
      const { error: dbError } = await supabase
        .from('sensors')
        .select('id')
        .limit(1);

      if (dbError) {
        throw new Error(`Database health check failed: ${dbError.message}`);
      }

      // Check TempStick API connectivity (simplified)
      const healthyThreshold = 10 * 60 * 1000; // 10 minutes
      const lastSyncAge = this.status.lastSyncTime 
        ? Date.now() - this.status.lastSyncTime.getTime()
        : Infinity;

      if (lastSyncAge > healthyThreshold) {
        this.status.systemHealth = 'degraded';
        console.warn('⚠️ System health degraded - last sync too old');
      } else {
        this.status.systemHealth = 'healthy';
      }

    } catch (error) {
      this.status.systemHealth = 'down';
      console.error('System health check failed:', error);
      throw error;
    }
  }

  /**
   * Cleanup old resolved alerts
   */
  private async cleanupResolvedAlerts(): Promise<void> {
    try {
      // Delete alerts older than 30 days that are resolved
      const thirtyDaysAgo = new Date();
      thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

      const { error } = await supabase
        .from('temperature_alerts')
        .delete()
        .lt('resolved_timestamp', thirtyDaysAgo.toISOString())
        .not('resolved_timestamp', 'is', null);

      if (error) {
        throw error;
      }

      console.log('🧹 Cleaned up old resolved alerts');

    } catch (error) {
      console.error('Alert cleanup failed:', error);
      throw error;
    }
  }

  /**
   * Handle browser visibility change (pause/resume sync)
   */
  private handleVisibilityChange(): void {
    if (document.hidden) {
      console.log('📱 Browser hidden - reducing sync frequency');
      // Optionally reduce sync frequency when tab is hidden
    } else {
      console.log('📱 Browser visible - resuming normal sync');
      // Optionally trigger immediate sync when tab becomes visible
      this.runJob('temperature-sync');
    }
  }

  /**
   * Get sync status for monitoring
   */
  getSyncStatus(): SyncStatus & { jobs: Array<{ id: string; name: string; lastRun?: Date; nextRun?: Date; isRunning: boolean }> } {
    const jobsStatus = Array.from(this.jobs.values()).map(job => ({
      id: job.id,
      name: job.name,
      lastRun: job.lastRun,
      nextRun: job.nextRun,
      isRunning: job.isRunning
    }));

    return {
      ...this.status,
      jobs: jobsStatus
    };
  }

  /**
   * Enable/disable a job
   */
  setJobEnabled(jobId: string, enabled: boolean): void {
    const job = this.jobs.get(jobId);
    if (!job) return;

    job.enabled = enabled;
    if (enabled) {
      this.startJob(jobId);
    } else {
      this.stopJob(jobId);
    }
  }

  /**
   * Update job interval
   */
  setJobInterval(jobId: string, interval: number): void {
    const job = this.jobs.get(jobId);
    if (!job) return;

    job.interval = interval;
    if (job.enabled) {
      this.stopJob(jobId);
      this.startJob(jobId);
    }
  }

  /**
   * Trigger manual sync
   */
  async triggerManualSync(): Promise<void> {
    console.log('🔧 Manual sync triggered');
    await this.runJob('temperature-sync');
    await this.runJob('sensor-sync');
  }

  /**
   * Shutdown all background processes
   */
  shutdown(): void {
    console.log('🔄 Shutting down background sync services...');
    this.stopAllJobs();
    
    if (typeof window !== 'undefined') {
      document.removeEventListener('visibilitychange', this.handleVisibilityChange.bind(this));
    }
  }
}

// Export singleton instance
export const backgroundSync = BackgroundSyncManager.getInstance();

// Auto-initialize in browser environment
if (typeof window !== 'undefined') {
  // Initialize after a short delay to allow other services to start
  setTimeout(() => {
    backgroundSync.initialize();
  }, 1000);

  // Cleanup on page unload
  window.addEventListener('beforeunload', () => {
    backgroundSync.shutdown();
  });
}

// React hook for monitoring sync status
import { useState, useEffect } from 'react';

export function useSyncStatus() {
  const [status, setStatus] = useState(backgroundSync.getSyncStatus());

  useEffect(() => {
    const interval = setInterval(() => {
      setStatus(backgroundSync.getSyncStatus());
    }, 1000); // Update every second

    return () => clearInterval(interval);
  }, []);

  return {
    ...status,
    triggerManualSync: backgroundSync.triggerManualSync.bind(backgroundSync),
    setJobEnabled: backgroundSync.setJobEnabled.bind(backgroundSync),
    setJobInterval: backgroundSync.setJobInterval.bind(backgroundSync)
  };
}
/**
 * Enhanced TempStick API Service
 * 
 * Handles integration with TempStick temperature monitoring sensors with:
 * - Robust error handling and retry logic
 * - API rate limiting and request throttling
 * - Comprehensive logging and monitoring
 * - Data quality validation
 * - Health check endpoints
 * 
 * @see https://tempstickapi.com/docs/
 */

import { supabase } from './supabase';
import type { 
  TempStickSensor, 
  TempStickReading, 
  TempStickApiResponse,
  Sensor,
  TemperatureReading,
  TemperatureAlert,
  SyncResponse,
  SystemHealth
} from '../types/tempstick';

// Enhanced error types for better error handling
export class TempStickApiError extends Error {
  constructor(
    message: string,
    public statusCode?: number,
    public retryable: boolean = false,
    public rateLimited: boolean = false
  ) {
    super(message);
    this.name = 'TempStickApiError';
  }
}

export class TempStickServiceError extends Error {
  constructor(
    message: string,
    public operation: string,
    public retryable: boolean = false
  ) {
    super(message);
    this.name = 'TempStickServiceError';
  }
}

// Rate limiting and retry configuration
interface RetryConfig {
  maxRetries: number;
  baseDelay: number;
  maxDelay: number;
  backoffMultiplier: number;
}

interface RateLimitConfig {
  requestsPerMinute: number;
  burstLimit: number;
}

// Service health monitoring
interface ServiceMetrics {
  totalRequests: number;
  successfulRequests: number;
  failedRequests: number;
  rateLimitedRequests: number;
  averageResponseTime: number;
  lastSyncTime: Date | null;
  lastErrorTime: Date | null;
  lastError: string | null;
}

/**
 * Enhanced TempStick API Client with rate limiting and retry logic
 */
class TempStickApiClient {
  private apiKey: string;
  private baseUrl: string;
  private retryConfig: RetryConfig;
  private rateLimitConfig: RateLimitConfig;
  private requestQueue: Array<{ timestamp: number; resolve: Function; reject: Function }> = [];
  private metrics: ServiceMetrics;

  constructor(apiKey: string, config?: { retry?: Partial<RetryConfig>; rateLimit?: Partial<RateLimitConfig>; baseUrl?: string }) {
    if (!apiKey) {
      throw new TempStickApiError('TempStick API key is required');
    }

    this.apiKey = apiKey;
    this.baseUrl = config?.baseUrl || '/api/tempstick';
    this.retryConfig = {
      maxRetries: 3,
      baseDelay: 1000,
      maxDelay: 30000,
      backoffMultiplier: 2,
      ...config?.retry
    };
    this.rateLimitConfig = {
      requestsPerMinute: 60,
      burstLimit: 10,
      ...config?.rateLimit
    };
    this.metrics = {
      totalRequests: 0,
      successfulRequests: 0,
      failedRequests: 0,
      rateLimitedRequests: 0,
      averageResponseTime: 0,
      lastSyncTime: null,
      lastErrorTime: null,
      lastError: null
    };
  }

  /**
   * Enhanced request method with retry logic and rate limiting
   */
  private async request<T>(endpoint: string, options?: RequestInit): Promise<TempStickApiResponse<T>> {
    const startTime = Date.now();
    let lastError: Error;

    for (let attempt = 0; attempt <= this.retryConfig.maxRetries; attempt++) {
      try {
        // Apply rate limiting
        await this.enforceRateLimit();
        
        const url = `${this.baseUrl}${endpoint}`;
        this.metrics.totalRequests++;
        
        const response = await fetch(url, {
          ...options,
          headers: {
            'X-API-KEY': this.apiKey,
            'Content-Type': 'application/json',
            'User-Agent': 'Seafood-Manager/1.0',
            'Accept': 'application/json',
            ...options?.headers,
          },
          timeout: 30000, // 30 second timeout
        });

        const responseTime = Date.now() - startTime;
        this.updateMetrics(responseTime, true);

        if (!response.ok) {
          const errorText = await response.text();
          const isRateLimited = response.status === 429;
          const isRetryable = response.status >= 500 || isRateLimited;
          
          if (isRateLimited) {
            this.metrics.rateLimitedRequests++;
            const retryAfter = response.headers.get('Retry-After');
            const delay = retryAfter ? parseInt(retryAfter) * 1000 : this.calculateDelay(attempt);
            
            if (attempt < this.retryConfig.maxRetries) {
              console.warn(`Rate limited, retrying after ${delay}ms (attempt ${attempt + 1}/${this.retryConfig.maxRetries + 1})`);
              await this.sleep(delay);
              continue;
            }
          }
          
          throw new TempStickApiError(
            `TempStick API error: ${response.status} ${response.statusText} - ${errorText}`,
            response.status,
            isRetryable,
            isRateLimited
          );
        }

        const data = await response.json();
        this.metrics.successfulRequests++;
        this.metrics.lastSyncTime = new Date();
        
        return data;
        
      } catch (error) {
        lastError = error as Error;
        this.updateMetrics(Date.now() - startTime, false, error as Error);
        
        if (error instanceof TempStickApiError && !error.retryable) {
          throw error;
        }
        
        if (attempt < this.retryConfig.maxRetries) {
          const delay = this.calculateDelay(attempt);
          console.warn(`Request failed, retrying after ${delay}ms (attempt ${attempt + 1}/${this.retryConfig.maxRetries + 1}):`, error.message);
          await this.sleep(delay);
        }
      }
    }
    
    throw new TempStickApiError(
      `Request failed after ${this.retryConfig.maxRetries + 1} attempts: ${lastError.message}`,
      undefined,
      false
    );
  }

  /**
   * Rate limiting enforcement
   */
  private async enforceRateLimit(): Promise<void> {
    const now = Date.now();
    const oneMinuteAgo = now - 60000;
    
    // Clean old requests from queue
    this.requestQueue = this.requestQueue.filter(req => req.timestamp > oneMinuteAgo);
    
    // Check if we're within rate limits
    if (this.requestQueue.length >= this.rateLimitConfig.requestsPerMinute) {
      const oldestRequest = this.requestQueue[0];
      const waitTime = 60000 - (now - oldestRequest.timestamp);
      
      if (waitTime > 0) {
        console.log(`Rate limit reached, waiting ${waitTime}ms`);
        await this.sleep(waitTime);
      }
    }
    
    // Add current request to queue
    this.requestQueue.push({
      timestamp: now,
      resolve: () => {},
      reject: () => {}
    });
  }

  /**
   * Calculate exponential backoff delay
   */
  private calculateDelay(attempt: number): number {
    const delay = this.retryConfig.baseDelay * Math.pow(this.retryConfig.backoffMultiplier, attempt);
    return Math.min(delay, this.retryConfig.maxDelay);
  }

  /**
   * Sleep utility
   */
  private sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * Update service metrics
   */
  private updateMetrics(responseTime: number, success: boolean, error?: Error): void {
    if (success) {
      this.metrics.averageResponseTime = 
        (this.metrics.averageResponseTime * this.metrics.successfulRequests + responseTime) / 
        (this.metrics.successfulRequests + 1);
    } else {
      this.metrics.failedRequests++;
      this.metrics.lastErrorTime = new Date();
      this.metrics.lastError = error?.message || 'Unknown error';
    }
  }

  /**
   * Get service health metrics
   */
  public getMetrics(): ServiceMetrics {
    return { ...this.metrics };
  }

  /**
   * Reset metrics (useful for monitoring)
   */
  public resetMetrics(): void {
    this.metrics = {
      totalRequests: 0,
      successfulRequests: 0,
      failedRequests: 0,
      rateLimitedRequests: 0,
      averageResponseTime: 0,
      lastSyncTime: null,
      lastErrorTime: null,
      lastError: null
    };
  }

  /**
   * Get all sensors from TempStick API with data validation
   */
  async getSensors(): Promise<TempStickSensor[]> {
    try {
      const response = await this.request<any>('/sensors/all');
      
      // Handle TempStick API response format according to official docs: { success: true, sensors: [...] }
      // Also handle alternative format: { type: 'success', data: { items: [...] } }
      const apiResponse = response as { sensors?: TempStickSensor[] } & TempStickApiResponse<{ items?: TempStickSensor[]; sensors?: TempStickSensor[] }>;
      const sensors = apiResponse.sensors || response.data?.items || response.data?.sensors || [];
      
      // Validate sensor data quality
      return sensors.filter((sensor: TempStickSensor) => this.validateSensorData(sensor));
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      throw new TempStickApiError(
        `Failed to fetch sensors: ${errorMessage}`,
        undefined,
        true
      );
    }
  }

  /**
   * Get latest readings for a specific sensor with validation
   */
  async getLatestReadings(sensorId: string, limit: number = 100): Promise<TempStickReading[]> {
    try {
      const response = await this.request<any>(
        `/readings/${encodeURIComponent(sensorId)}/${limit}`
      );
      
      // Handle TempStick API response format
      const readings = response.data?.items || [];
      
      // Convert to our format
      const convertedReadings = readings.map((reading: any) => ({
        temperature: reading.temp,
        humidity: reading.hum,
        timestamp: reading.date,
        sensor_id: sensorId,
        battery_level: reading.battery,
        signal_strength: reading.rssi
      }));
      
      // Validate and filter readings
      return convertedReadings.filter(reading => this.validateReadingData(reading));
    } catch (error) {
      throw new TempStickApiError(
        `Failed to fetch readings for sensor ${sensorId}: ${error.message}`,
        undefined,
        true
      );
    }
  }

  /**
   * Get historical readings for a sensor within a date range
   */
  async getReadingsForPeriod(
    sensorId: string,
    startDate: Date,
    endDate: Date
  ): Promise<TempStickReading[]> {
    try {
      // Validate date range
      if (startDate >= endDate) {
        throw new TempStickApiError('Start date must be before end date', undefined, false);
      }
      
      const maxRangeMs = 30 * 24 * 60 * 60 * 1000; // 30 days
      if (endDate.getTime() - startDate.getTime() > maxRangeMs) {
        throw new TempStickApiError('Date range cannot exceed 30 days', undefined, false);
      }
      
      const start = startDate.toISOString();
      const end = endDate.toISOString();
      
      const response = await this.request<{ readings: TempStickReading[] }>(
        `/sensors/${encodeURIComponent(sensorId)}/readings?start=${start}&end=${end}`
      );
      const readings = response.data?.readings || [];
      
      return readings.filter(reading => this.validateReadingData(reading));
    } catch (error) {
      throw new TempStickApiError(
        `Failed to fetch historical readings for sensor ${sensorId}: ${error.message}`,
        undefined,
        true
      );
    }
  }

  /**
   * Get sensor health status
   */
  async getSensorHealth(sensorId: string): Promise<{ online: boolean; batteryLevel: number; signalStrength: number }> {
    try {
      const response = await this.request<{ health: { online: boolean; batteryLevel: number; signalStrength: number } }>(`/sensors/${encodeURIComponent(sensorId)}/health`);
      return response.data?.health || { online: false, batteryLevel: 0, signalStrength: 0 };
    } catch (error) {
      console.warn(`Failed to get health for sensor ${sensorId}:`, error.message);
      return { online: false, batteryLevel: 0, signalStrength: 0 };
    }
  }

  /**
   * Batch get multiple sensors' latest readings
   */
  async getBatchLatestReadings(sensorIds: string[]): Promise<Record<string, TempStickReading[]>> {
    const results: Record<string, TempStickReading[]> = {};
    
    // Process in batches to avoid overwhelming the API
    const batchSize = 5;
    for (let i = 0; i < sensorIds.length; i += batchSize) {
      const batch = sensorIds.slice(i, i + batchSize);
      const promises = batch.map(async (sensorId) => {
        try {
          const readings = await this.getLatestReadings(sensorId, 10);
          return { sensorId, readings };
        } catch (error) {
          console.warn(`Failed to get readings for sensor ${sensorId}:`, error.message);
          return { sensorId, readings: [] };
        }
      });
      
      const batchResults = await Promise.all(promises);
      batchResults.forEach(({ sensorId, readings }) => {
        results[sensorId] = readings;
      });
      
      // Small delay between batches to be respectful to the API
      if (i + batchSize < sensorIds.length) {
        await this.sleep(100);
      }
    }
    
    return results;
  }

  /**
   * Validate sensor data quality
   */
  private validateSensorData(sensor: TempStickSensor): boolean {
    if (!sensor.sensor_id || typeof sensor.sensor_id !== 'string') {
      console.warn('Invalid sensor: missing or invalid sensor_id', sensor);
      return false;
    }
    
    if (!sensor.sensor_name) {
      console.warn('Invalid sensor: missing sensor_name', sensor);
      return false;
    }
    
    return true;
  }

  /**
   * Validate temperature reading data quality
   */
  private validateReadingData(reading: TempStickReading): boolean {
    // Check required fields
    if (!reading.sensor_id || !reading.timestamp) {
      console.warn('Invalid reading: missing sensor_id or timestamp', reading);
      return false;
    }
    
    // Validate temperature range (reasonable for food storage)
    if (typeof reading.temperature !== 'number' || 
        reading.temperature < -50 || reading.temperature > 150) {
      console.warn('Invalid reading: temperature out of reasonable range', reading);
      return false;
    }
    
    // Validate humidity if present
    if (reading.humidity !== undefined && 
        (typeof reading.humidity !== 'number' || reading.humidity < 0 || reading.humidity > 100)) {
      console.warn('Invalid reading: humidity out of valid range', reading);
      return false;
    }
    
    // Validate timestamp
    const readingTime = new Date(reading.timestamp);
    const now = new Date();
    const oneYearAgo = new Date(now.getTime() - 365 * 24 * 60 * 60 * 1000);
    
    if (readingTime > now || readingTime < oneYearAgo) {
      console.warn('Invalid reading: timestamp out of reasonable range', reading);
      return false;
    }
    
    return true;
  }

  /**
   * Test API connectivity and authentication
   */
  async testConnection(): Promise<{ success: boolean; latency: number; error?: string }> {
    const startTime = Date.now();
    
    try {
      await this.request('/sensors/all');
      const latency = Date.now() - startTime;
      
      return { success: true, latency };
    } catch (error) {
      const latency = Date.now() - startTime;
      return { 
        success: false, 
        latency, 
        error: error.message 
      };
    }
  }
}

/**
 * Enhanced TempStick Service for Seafood Manager Integration
 */
export class TempStickService {
  private apiClient: TempStickApiClient | null;
  private syncInProgress: boolean = false;
  private lastFullSync: Date | null = null;
  private healthCheckInterval: NodeJS.Timeout | null = null;
  private dataMode: 'real' | 'mock' | 'auto' = 'auto';
  private lastApiCall: number = 0;
  private minCallInterval: number = 2000; // Minimum 2 seconds between API calls

  constructor(config?: { 
    apiKey?: string; 
    retry?: Partial<RetryConfig>; 
    rateLimit?: Partial<RateLimitConfig>;
    enableHealthChecks?: boolean;
  }) {
    const apiKey = config?.apiKey || import.meta.env.VITE_TEMPSTICK_API_KEY;
    
    // Only create API client if we have an API key
    if (apiKey) {
      try {
        this.apiClient = new TempStickApiClient(apiKey, {
          retry: config?.retry,
          rateLimit: config?.rateLimit,
          baseUrl: 'http://localhost:3001/api/v1'
        });
      } catch (error) {
        console.error('Failed to initialize TempStick API client:', error.message);
        throw new TempStickServiceError('Failed to initialize TempStick API client', 'initialization');
      }
    } else {
      console.error('🔧 No TempStick API key found. Please configure VITE_TEMPSTICK_API_KEY environment variable.');
      throw new TempStickServiceError('TempStick API key is required', 'initialization');
    }

    // Start health checks if enabled
    if (config?.enableHealthChecks !== false) {
      this.startHealthChecks();
    }
  }

  /**
   * Start periodic health checks
   */
  private startHealthChecks(): void {
    // Check every 5 minutes
    this.healthCheckInterval = setInterval(async () => {
      try {
        await this.performHealthCheck();
      } catch (error) {
        console.error('Health check failed:', error);
      }
    }, 5 * 60 * 1000);
  }

  /**
   * Stop health checks
   */
  public stopHealthChecks(): void {
    if (this.healthCheckInterval) {
      clearInterval(this.healthCheckInterval);
      this.healthCheckInterval = null;
    }
  }

  /**
   * Perform comprehensive health check
   */
  public async performHealthCheck(): Promise<SystemHealth> {
    const startTime = Date.now();
    
    try {
      // Test API connectivity
      const apiTest = await this.apiClient.testConnection();
      
      // Get database health
      const dbHealth = await this.checkDatabaseHealth();
      
      // Get sensor status summary
      const sensorSummary = await this.getSensorSummary();
      
      // Get alert summary
      const alertSummary = await this.getAlertSummary();
      
      return {
        tempstickApi: {
          status: apiTest.success ? 'healthy' : 'down',
          latency: apiTest.latency,
          lastCheck: new Date().toISOString()
        },
        database: dbHealth,
        sensors: sensorSummary,
        alerts: alertSummary
      };
    } catch (error) {
      console.error('Health check failed:', error);
      return {
        tempstickApi: {
          status: 'down',
          latency: Date.now() - startTime,
          lastCheck: new Date().toISOString()
        },
        database: {
          status: 'down',
          activeConnections: 0,
          lastMigration: 'unknown'
        },
        sensors: {
          total: 0,
          online: 0,
          offline: 0,
          lowBattery: 0
        },
        alerts: {
          active: 0,
          unresolved: 0,
          critical: 0
        }
      };
    }
  }

  /**
   * Check database connectivity and health
   */
  private async checkDatabaseHealth(): Promise<SystemHealth['database']> {
    try {
      const { data, error } = await supabase
        .from('sensors')
        .select('id')
        .limit(1);
      
      if (error) {
        return {
          status: 'down',
          activeConnections: 0,
          lastMigration: 'unknown'
        };
      }
      
      return {
        status: 'healthy',
        activeConnections: 1, // Supabase handles connection pooling
        lastMigration: new Date().toISOString()
      };
    } catch (error) {
      return {
        status: 'down',
        activeConnections: 0,
        lastMigration: 'unknown'
      };
    }
  }

  /**
   * Get sensor status summary
   */
  private async getSensorSummary(): Promise<SystemHealth['sensors']> {
    try {
      const { data: sensors, error } = await supabase
        .from('sensors')
        .select('is_online, battery_level')
        .eq('is_active', true);
      
      if (error || !sensors) {
        return { total: 0, online: 0, offline: 0, lowBattery: 0 };
      }
      
      const total = sensors.length;
      const online = sensors.filter(s => s.is_online).length;
      const offline = total - online;
      const lowBattery = sensors.filter(s => s.battery_level && s.battery_level < 25).length;
      
      return { total, online, offline, lowBattery };
    } catch (error) {
      return { total: 0, online: 0, offline: 0, lowBattery: 0 };
    }
  }

  /**
   * Get alert status summary
   */
  private async getAlertSummary(): Promise<SystemHealth['alerts']> {
    try {
      const { data: alerts, error } = await supabase
        .from('temperature_alerts')
        .select('alert_status, severity')
        .eq('alert_status', 'active');
      
      if (error || !alerts) {
        return { active: 0, unresolved: 0, critical: 0 };
      }
      
      const active = alerts.length;
      const unresolved = alerts.filter(a => a.alert_status === 'active').length;
      const critical = alerts.filter(a => a.severity === 'critical' || a.severity === 'emergency').length;
      
      return { active, unresolved, critical };
    } catch (error) {
      return { active: 0, unresolved: 0, critical: 0 };
    }
  }

  /**
   * Enhanced sensor sync with comprehensive error handling
   */
  async syncSensors(): Promise<SyncResponse> {
    const startTime = Date.now();
    const result: SyncResponse = {
      success: false,
      syncedSensors: 0,
      newReadings: 0,
      newAlerts: 0,
      errors: [],
      lastSyncTime: new Date().toISOString()
    };

    try {
      console.log('🌡️ Starting enhanced sensor sync from TempStick API...');
      
      const tempStickSensors = await this.apiClient.getSensors();
      
      if (tempStickSensors.length === 0) {
        console.log('No sensors found in TempStick API');
        result.success = true;
        return result;
      }

      console.log(`Found ${tempStickSensors.length} sensors in TempStick API`);

      for (const tempStickSensor of tempStickSensors) {
        try {
          await this.syncSingleSensor(tempStickSensor);
          result.syncedSensors++;
        } catch (error) {
          const errorMsg = `Failed to sync sensor ${tempStickSensor.id}: ${error.message}`;
          console.error(errorMsg);
          result.errors.push(errorMsg);
        }
      }

      // Update sensor health status
      await this.updateSensorHealthStatus();

      const duration = Date.now() - startTime;
      console.log(`🎉 Sensor sync completed in ${duration}ms - ${result.syncedSensors}/${tempStickSensors.length} sensors synced`);
      
      result.success = result.errors.length === 0;
      return result;
      
    } catch (error) {
      const errorMsg = `Sensor sync failed: ${error.message}`;
      console.error('❌', errorMsg);
      result.errors.push(errorMsg);
      throw new TempStickServiceError(errorMsg, 'syncSensors', true);
    }
  }

  /**
   * Sync a single sensor with detailed error handling
   */
  private async syncSingleSensor(tempStickSensor: TempStickSensor): Promise<void> {
    try {
      // Check if sensor already exists in our database
      const { data: existingSensor, error: fetchError } = await supabase
        .from('sensors')
        .select('*')
        .eq('sensor_id', tempStickSensor.id)
        .maybeSingle();

      if (fetchError) {
        throw new TempStickServiceError(
          `Database query failed for sensor ${tempStickSensor.id}: ${fetchError.message}`,
          'syncSingleSensor'
        );
      }

      if (!existingSensor) {
        // Create new sensor record
        const newSensor = {
          sensor_id: tempStickSensor.id,
          name: tempStickSensor.name || `TempStick ${tempStickSensor.id}`,
          location: tempStickSensor.location || 'Unknown Location',
          is_online: tempStickSensor.status === 'online',
          battery_level: tempStickSensor.battery_level || null,
          is_active: true,
        };

        const { error: insertError } = await supabase
          .from('sensors')
          .insert([newSensor]);

        if (insertError) {
          throw new TempStickServiceError(
            `Failed to create sensor ${tempStickSensor.id}: ${insertError.message}`,
            'syncSingleSensor'
          );
        }

        console.log(`✅ Created sensor: ${newSensor.name}`);
      } else {
        // Update existing sensor with latest data
        const updates = {
          name: tempStickSensor.name || existingSensor.name,
          location: tempStickSensor.location || existingSensor.location,
          is_online: tempStickSensor.status === 'online',
          battery_level: tempStickSensor.battery_level || existingSensor.battery_level,
          updated_at: new Date().toISOString()
        };

        const { error: updateError } = await supabase
          .from('sensors')
          .update(updates)
          .eq('sensor_id', tempStickSensor.id);

        if (updateError) {
          throw new TempStickServiceError(
            `Failed to update sensor ${tempStickSensor.id}: ${updateError.message}`,
            'syncSingleSensor'
          );
        }

        console.log(`🔄 Updated sensor: ${updates.name}`);
      }
    } catch (error) {
      if (error instanceof TempStickServiceError) {
        throw error;
      }
      throw new TempStickServiceError(
        `Unexpected error syncing sensor ${tempStickSensor.id}: ${error.message}`,
        'syncSingleSensor'
      );
    }
  }

  /**
   * Update sensor health status based on latest data
   */
  private async updateSensorHealthStatus(): Promise<void> {
    try {
      const { data: sensors, error } = await supabase
        .from('sensors')
        .select('id, sensor_id, is_active')
        .eq('is_active', true);

      if (error || !sensors) {
        console.warn('Failed to fetch sensors for health update:', error?.message);
        return;
      }

      for (const sensor of sensors) {
        try {
          const health = await this.apiClient.getSensorHealth(sensor.sensor_id);
          
          await supabase
            .from('sensors')
            .update({
              is_online: health.online,
              battery_level: health.batteryLevel,
              updated_at: new Date().toISOString()
            })
            .eq('id', sensor.id);
            
        } catch (error) {
          console.warn(`Failed to update health for sensor ${sensor.sensor_id}:`, error.message);
        }
      }
    } catch (error) {
      console.warn('Failed to update sensor health status:', error.message);
    }
  }

  /**
   * Enhanced temperature readings sync for all active sensors
   */
  async syncAllTemperatureReadings(): Promise<SyncResponse> {
    if (this.syncInProgress) {
      throw new TempStickServiceError('Sync already in progress', 'syncAllTemperatureReadings');
    }

    this.syncInProgress = true;
    const startTime = Date.now();
    const result: SyncResponse = {
      success: false,
      syncedSensors: 0,
      newReadings: 0,
      newAlerts: 0,
      errors: [],
      lastSyncTime: new Date().toISOString()
    };

    try {
      console.log('🌡️ Starting enhanced temperature readings sync...');

      // Get all active sensors from database
      const { data: sensors, error } = await supabase
        .from('sensors')
        .select('id, sensor_id, name, is_active')
        .eq('is_active', true);

      if (error) {
        throw new TempStickServiceError(
          `Failed to fetch sensors: ${error.message}`,
          'syncAllTemperatureReadings'
        );
      }

      if (!sensors || sensors.length === 0) {
        console.log('No active sensors found');
        result.success = true;
        return result;
      }

      console.log(`Syncing temperature readings for ${sensors.length} sensors...`);

      // Process sensors in batches to avoid overwhelming the API
      const batchSize = 3;
      for (let i = 0; i < sensors.length; i += batchSize) {
        const batch = sensors.slice(i, i + batchSize);
        
        const batchPromises = batch.map(async (sensor) => {
          try {
            const sensorResult = await this.syncTemperatureReadingsForSensor(
              sensor.id, 
              sensor.sensor_id
            );
            result.syncedSensors++;
            result.newReadings += sensorResult.newReadings;
            result.newAlerts += sensorResult.newAlerts;
            return sensorResult;
          } catch (error) {
            const errorMsg = `Failed to sync readings for sensor ${sensor.name} (${sensor.sensor_id}): ${error.message}`;
            console.error(errorMsg);
            result.errors.push(errorMsg);
            return { newReadings: 0, newAlerts: 0 };
          }
        });

        await Promise.all(batchPromises);
        
        // Small delay between batches
        if (i + batchSize < sensors.length) {
          await new Promise(resolve => setTimeout(resolve, 500));
        }
      }

      this.lastFullSync = new Date();
      const duration = Date.now() - startTime;
      
      console.log(`🎉 Temperature readings sync completed in ${duration}ms`);
      console.log(`   📊 ${result.syncedSensors}/${sensors.length} sensors synced`);
      console.log(`   📈 ${result.newReadings} new readings`);
      console.log(`   🚨 ${result.newAlerts} new alerts`);
      
      result.success = result.errors.length === 0;
      return result;
      
    } catch (error) {
      const errorMsg = `Temperature readings sync failed: ${error.message}`;
      console.error('❌', errorMsg);
      result.errors.push(errorMsg);
      throw new TempStickServiceError(errorMsg, 'syncAllTemperatureReadings', true);
    } finally {
      this.syncInProgress = false;
    }
  }

  /**
   * Sync temperature readings for a specific sensor
   */
  private async syncTemperatureReadingsForSensor(
    sensorId: string,
    tempstickSensorId: string
  ): Promise<{ newReadings: number; newAlerts: number }> {
    try {
      // Get latest reading timestamp to avoid duplicates
      const { data: latestReading } = await supabase
        .from('temperature_readings')
        .select('recorded_at')
        .eq('sensor_id', sensorId)
        .order('recorded_at', { ascending: false })
        .limit(1)
        .single();

      // Fetch latest readings from TempStick API
      const tempstickReadings = await this.apiClient.getLatestReadings(tempstickSensorId);
      
      if (tempstickReadings.length === 0) {
        return;
      }

      // Filter out readings we already have
      const latestTimestamp = latestReading?.recorded_at
        ? new Date(latestReading.recorded_at)
        : new Date(0);

      const newReadings = tempstickReadings.filter(reading => 
        new Date(reading.timestamp) > latestTimestamp
      );

      if (newReadings.length === 0) {
        console.log(`No new readings for sensor ${sensorId}`);
        return { newReadings: 0, newAlerts: 0 };
      }

      // Get current user ID
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) {
        throw new TempStickServiceError('User not authenticated', 'syncTemperatureReadingsForSensor');
      }

      // Convert TempStick readings to our database format
      const readings = newReadings.map(reading => ({
        user_id: user.id,
        sensor_id: sensorId,
        temp_celsius: reading.temperature,
        temp_fahrenheit: (reading.temperature * 9/5) + 32,
        humidity: reading.humidity,
        recorded_at: reading.timestamp,
      }));

      // Insert new readings
      const { error } = await supabase
        .from('temperature_readings')
        .insert(readings);

      if (error) {
        console.error(`Failed to insert readings for sensor ${sensorId}:`, error);
      } else {
        console.log(`✅ Inserted ${readings.length} readings for sensor ${sensorId}`);
        
        // Check for alert conditions
        const newAlerts = await this.checkAlertConditions(sensorId, readings);
        
        return { newReadings: readings.length, newAlerts };
      }
    } catch (error) {
      console.error(`Failed to sync readings for sensor ${sensorId}:`, error);
      throw new TempStickServiceError(
        `Failed to sync readings for sensor ${sensorId}: ${error instanceof Error ? error.message : 'Unknown error'}`,
        'syncTemperatureReadingsForSensor',
        true
      );
    }
  }

  /**
   * Check temperature readings against thresholds and create alerts
   */
  private async checkAlertConditions(
    sensorId: string,
    readings: Array<{
      user_id: string;
      sensor_id: string;
      temp_celsius: number;
      temp_fahrenheit: number;
      humidity: number;
      recorded_at: string;
    }>
  ): Promise<number> {
    try {
      // Get sensor thresholds and storage area requirements
      const { data: sensor, error } = await supabase
        .from('sensors')
        .select(`
          *,
          storage_areas (
            required_temp_min,
            required_temp_max,
            haccp_control_point
          )
        `)
        .eq('id', sensorId)
        .single();

      if (error || !sensor) {
        return 0;
      }

      const alerts: any[] = [];

      for (const reading of readings) {
        // Check sensor-specific thresholds
        if (sensor.temp_min_threshold && reading.temp_celsius < sensor.temp_min_threshold) {
          alerts.push({
            user_id: reading.user_id,
            sensor_id: sensorId,
            alert_type: 'low_temp',
            severity: 'critical',
            title: 'Low Temperature Alert',
            message: `Temperature ${reading.temp_celsius}°C is below minimum threshold of ${sensor.temp_min_threshold}°C`,
            threshold_value: sensor.temp_min_threshold,
            actual_value: reading.temp_celsius,
            deviation: sensor.temp_min_threshold - reading.temp_celsius,
            first_detected_at: reading.recorded_at,
            haccp_violation: true,
            product_safety_risk: 'high',
          });
        }

        if (sensor.temp_max_threshold && reading.temp_celsius > sensor.temp_max_threshold) {
          alerts.push({
            user_id: reading.user_id,
            sensor_id: sensorId,
            alert_type: 'high_temp',
            severity: 'critical',
            title: 'High Temperature Alert',
            message: `Temperature ${reading.temp_celsius}°C is above maximum threshold of ${sensor.temp_max_threshold}°C`,
            threshold_value: sensor.temp_max_threshold,
            actual_value: reading.temp_celsius,
            deviation: reading.temp_celsius - sensor.temp_max_threshold,
            first_detected_at: reading.recorded_at,
            haccp_violation: true,
            product_safety_risk: 'high',
          });
        }

        // Check storage area requirements (HACCP compliance)
        const storageArea = sensor.storage_areas;
        if (storageArea) {
          if (storageArea.required_temp_min && reading.temp_celsius < storageArea.required_temp_min) {
            alerts.push({
              user_id: reading.user_id,
              sensor_id: sensorId,
              storage_area_id: storageArea.id,
              alert_type: 'low_temp',
              severity: storageArea.haccp_control_point ? 'critical' : 'warning',
              title: 'HACCP Temperature Violation - Low',
              message: `Temperature ${reading.temp_celsius}°C is below required minimum of ${storageArea.required_temp_min}°C for ${storageArea.name}`,
              threshold_value: storageArea.required_temp_min,
              actual_value: reading.temp_celsius,
              deviation: storageArea.required_temp_min - reading.temp_celsius,
              first_detected_at: reading.recorded_at,
              haccp_violation: true,
              regulatory_notification_required: storageArea.haccp_control_point,
              product_safety_risk: 'high',
            });
          }

          if (storageArea.required_temp_max && reading.temp_celsius > storageArea.required_temp_max) {
            alerts.push({
              user_id: reading.user_id,
              sensor_id: sensorId,
              storage_area_id: storageArea.id,
              alert_type: 'high_temp',
              severity: storageArea.haccp_control_point ? 'critical' : 'warning',
              title: 'HACCP Temperature Violation - High',
              message: `Temperature ${reading.temp_celsius}°C is above required maximum of ${storageArea.required_temp_max}°C for ${storageArea.name}`,
              threshold_value: storageArea.required_temp_max,
              actual_value: reading.temp_celsius,
              deviation: reading.temp_celsius - storageArea.required_temp_max,
              first_detected_at: reading.recorded_at,
              haccp_violation: true,
              regulatory_notification_required: storageArea.haccp_control_point,
              product_safety_risk: 'high',
            });
          }
        }
      }

      // Insert alerts if any were generated
      if (alerts.length > 0) {
        const { error: alertError } = await supabase
          .from('temperature_alerts')
          .insert(alerts);

        if (alertError) {
          console.error('Failed to create temperature alerts:', alertError);
        } else {
          console.log(`🚨 Created ${alerts.length} temperature alerts for sensor ${sensorId}`);
          return alerts.length;
        }
      }
      
      return 0;
    } catch (error) {
      console.error('Failed to check alert conditions:', error);
      return 0;
    }
  }

  /**
   * Get current temperature readings for dashboard
   */
  async getCurrentReadings(): Promise<TemperatureReading[]> {
    const { data, error } = await supabase
      .from('temperature_readings')
      .select(`
        *,
        sensors (
          name,
          storage_areas (
            name,
            area_type
          )
        )
      `)
      .order('recorded_at', { ascending: false })
      .limit(50); // Get latest 50 readings across all sensors

    if (error) {
      console.error('Failed to fetch current readings:', error);
      return [];
    }

    return data || [];
  }

  /**
   * Get temperature readings for a specific time range
   */
  async getReadingsForDateRange(
    startDate: Date,
    endDate: Date,
    sensorIds?: string[]
  ): Promise<TemperatureReading[]> {
    let query = supabase
      .from('temperature_readings')
      .select(`
        *,
        sensors (
          name,
          location_description,
          tempstick_sensor_id
        )
      `)
      .gte('recorded_at', startDate.toISOString())
      .lte('recorded_at', endDate.toISOString())
      .order('recorded_at', { ascending: true });

    if (sensorIds && sensorIds.length > 0) {
      query = query.in('sensor_id', sensorIds);
    }

    const { data, error } = await query;

    if (error) {
      console.error('Failed to fetch readings for date range:', error);
      return [];
    }

    return data || [];
  }

  /**
   * Get active temperature alerts
   */
  async getActiveAlerts(): Promise<TemperatureAlert[]> {
    const { data, error } = await supabase
      .from('temperature_alerts')
      .select(`
        *,
        sensors (
          name,
          storage_areas (
            name,
            area_type
          )
        )
      `)
      .is('resolved_timestamp', null)
      .order('alert_timestamp', { ascending: false });

    if (error) {
      console.error('Failed to fetch active alerts:', error);
      return [];
    }

    return data || [];
  }

  /**
   * Resolve a temperature alert
   */
  async resolveAlert(alertId: string): Promise<void> {
    const { error } = await supabase
      .from('temperature_alerts')
      .update({ resolved_timestamp: new Date().toISOString() })
      .eq('id', alertId);

    if (error) {
      console.error('Failed to resolve alert:', error);
      throw error;
    }
  }

  /**
   * Enhanced scheduled sync with comprehensive monitoring
   */
  async scheduledSync(): Promise<SyncResponse> {
    const startTime = Date.now();
    const result: SyncResponse = {
      success: false,
      syncedSensors: 0,
      newReadings: 0,
      newAlerts: 0,
      errors: [],
      lastSyncTime: new Date().toISOString()
    };

    try {
      console.log('🔄 Starting enhanced scheduled TempStick sync...');
      
      // First sync sensors (in case new ones were added)
      const sensorResult = await this.syncSensors();
      result.syncedSensors = sensorResult.syncedSensors;
      result.errors.push(...sensorResult.errors);
      
      // Then sync temperature readings
      const readingsResult = await this.syncAllTemperatureReadings();
      result.newReadings = readingsResult.newReadings;
      result.newAlerts = readingsResult.newAlerts;
      result.errors.push(...readingsResult.errors);
      
      const duration = Date.now() - startTime;
      console.log(`✅ Scheduled sync completed in ${duration}ms`);
      console.log(`   📊 ${result.syncedSensors} sensors synced`);
      console.log(`   📈 ${result.newReadings} new readings`);
      console.log(`   🚨 ${result.newAlerts} new alerts`);
      
      result.success = result.errors.length === 0;
      return result;
      
    } catch (error) {
      const errorMsg = `Scheduled sync failed: ${error instanceof Error ? error.message : 'Unknown error'}`;
      console.error('❌', errorMsg);
      result.errors.push(errorMsg);
      throw new TempStickServiceError(errorMsg, 'scheduledSync', true);
    }
  }

  /**
   * Get service metrics and health information
   */
  public getServiceMetrics(): {
    api: ServiceMetrics;
    sync: {
      inProgress: boolean;
      lastFullSync: Date | null;
    };
  } {
    return {
      api: this.apiClient.getMetrics(),
      sync: {
        inProgress: this.syncInProgress,
        lastFullSync: this.lastFullSync
      }
    };
  }

  /**
   * Reset service metrics
   */
  public resetMetrics(): void {
    this.apiClient.resetMetrics();
  }

  /**
   * Get all sensors from TempStick API with rate limiting
   */
  public async getAllSensors(): Promise<any[]> {
    if (!this.apiClient) {
      throw new TempStickServiceError('TempStick API client not initialized', 'getAllSensors');
    }

    // Simple rate limiting - wait if last call was too recent
    const now = Date.now();
    const timeSinceLastCall = now - this.lastApiCall;
    if (timeSinceLastCall < this.minCallInterval) {
      const waitTime = this.minCallInterval - timeSinceLastCall;
      console.log(`⏱️  Rate limiting: waiting ${waitTime}ms before API call`);
      await new Promise(resolve => setTimeout(resolve, waitTime));
    }
    this.lastApiCall = Date.now();

    try {
      // Get sensors from TempStick API
      const tempStickSensors = await this.apiClient.getSensors();
      if (tempStickSensors.length > 0) {
        console.log('✅ Successfully retrieved', tempStickSensors.length, 'sensors from TempStick API');
        return tempStickSensors.map(sensor => ({
          sensor_id: sensor.sensor_id,
          sensor_name: sensor.sensor_name,
          location: sensor.sensor_name, // Use sensor name as location fallback
          status: sensor.offline === '1' ? 'offline' : 'online',
          last_temp: sensor.last_temp,
          last_humidity: sensor.last_humidity,
          last_reading: sensor.last_checkin,
          battery_level: sensor.battery_pct,
          rssi: sensor.rssi
        }));
      } else {
        console.log('⚠️ No sensors found in TempStick API response');
        return [];
      }
    } catch (error) {
      throw new TempStickServiceError(`Failed to get sensors: ${error.message}`, 'getAllSensors', true);
    }
  }



  /**
   * Get latest readings for a sensor from TempStick API
   */
  public async getLatestReadings(sensorId: string, limit: number = 100): Promise<TempStickReading[]> {
    if (!this.apiClient) {
      throw new TempStickServiceError('TempStick API client not initialized', 'getLatestReadings');
    }

    try {
      // Get readings from TempStick API
      const readings = await this.apiClient.getLatestReadings(sensorId, limit);
      if (readings && readings.length > 0) {
        console.log(`✅ Retrieved ${readings.length} readings for sensor ${sensorId}`);
        return readings;
      } else {
        console.log(`⚠️ No readings found for sensor ${sensorId}`);
        return [];
      }
    } catch (error) {
      throw new TempStickServiceError(`Failed to get readings for sensor ${sensorId}: ${error.message}`, 'getLatestReadings', true);
    }
  }



  /**
   * Set data source mode
   */
  public setDataMode(mode: 'real' | 'mock' | 'auto'): void {
    this.dataMode = mode;
    console.log(`🔧 TempStick data mode set to: ${mode}`);
  }

  /**
   * Get current data mode
   */
  public getDataMode(): 'real' | 'mock' | 'auto' {
    return this.dataMode;
  }



  /**
   * Graceful shutdown
   */
  public shutdown(): void {
    this.stopHealthChecks();
    console.log('TempStick service shutdown completed');
  }
}

// Export singleton instance with enhanced configuration
export const tempStickService = new TempStickService({
  enableHealthChecks: true,
  retry: {
    maxRetries: 3,
    baseDelay: 1000,
    maxDelay: 30000,
    backoffMultiplier: 2
  },
  rateLimit: {
    requestsPerMinute: 60,
    burstLimit: 10
  }
});

// Export classes and types for testing and advanced usage
export { 
  TempStickApiClient,
  type RetryConfig,
  type RateLimitConfig,
  type ServiceMetrics
};
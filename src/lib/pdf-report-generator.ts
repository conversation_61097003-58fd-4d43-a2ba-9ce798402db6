/**
 * PDF Report Generator for Temperature Monitoring
 * 
 * Professional PDF generation service for HACCP-compliant temperature monitoring reports.
 * Generates charts, tables, and formatted reports suitable for regulatory submissions.
 */

import type {
  TemperatureReportData,
  TemperatureReportParams,
  TemperatureReading,
  TemperatureAlert,
  Sensor,
  StorageArea
} from '../types/tempstick';

// PDF generation types
interface PDFChart {
  type: 'line' | 'bar' | 'pie' | 'scatter';
  title: string;
  data: ChartDataPoint[];
  width: number;
  height: number;
  options?: ChartOptions;
}

interface ChartDataPoint {
  x: number | string;
  y: number;
  label?: string;
  color?: string;
  metadata?: Record<string, any>;
}

interface ChartOptions {
  xLabel?: string;
  yLabel?: string;
  colors?: string[];
  showGrid?: boolean;
  showLegend?: boolean;
  dateFormat?: string;
  temperatureUnit?: 'F' | 'C';
  alertThresholds?: {
    min?: number;
    max?: number;
  };
}

interface PDFSection {
  type: 'cover' | 'summary' | 'chart' | 'table' | 'text' | 'compliance' | 'alerts';
  title?: string;
  content: any;
  pageBreak?: boolean;
}

interface PDFTemplate {
  name: string;
  description: string;
  sections: PDFSection[];
  metadata: {
    title: string;
    subject: string;
    author: string;
    keywords: string[];
  };
}

interface PDFGenerationProgress {
  stage: 'preparing' | 'charts' | 'data' | 'rendering' | 'complete' | 'error';
  progress: number; // 0-100
  currentTask?: string;
  estimatedTimeRemaining?: number;
  error?: string;
}

type PDFGenerationCallback = (progress: PDFGenerationProgress) => void;

/**
 * PDF Report Generation Service
 */
export class PDFReportGenerator {
  private static instance: PDFReportGenerator;
  private canvas: HTMLCanvasElement;
  private ctx: CanvasRenderingContext2D;
  private progressCallback?: PDFGenerationCallback;

  constructor() {
    // Create off-screen canvas for chart generation
    this.canvas = document.createElement('canvas');
    this.ctx = this.canvas.getContext('2d')!;
    this.canvas.width = 800;
    this.canvas.height = 400;
  }

  static getInstance(): PDFReportGenerator {
    if (!PDFReportGenerator.instance) {
      PDFReportGenerator.instance = new PDFReportGenerator();
    }
    return PDFReportGenerator.instance;
  }

  /**
   * Generate PDF report with progress tracking
   */
  async generateReport(
    reportData: TemperatureReportData,
    params: TemperatureReportParams,
    template: string = 'comprehensive',
    progressCallback?: PDFGenerationCallback
  ): Promise<Uint8Array> {
    this.progressCallback = progressCallback;

    try {
      this.updateProgress({ stage: 'preparing', progress: 0, currentTask: 'Initializing report generation' });

      // Import jsPDF dynamically for bundle optimization
      const { default: jsPDF } = await import('jspdf');
      await import('jspdf-autotable');

      const pdf = new jsPDF('portrait', 'mm', 'a4');
      const pdfTemplate = this.getTemplate(template, reportData, params);

      this.updateProgress({ stage: 'preparing', progress: 10, currentTask: 'Loading template configuration' });

      // Set document metadata
      pdf.setProperties({
        title: pdfTemplate.metadata.title,
        subject: pdfTemplate.metadata.subject,
        author: pdfTemplate.metadata.author,
        keywords: pdfTemplate.metadata.keywords.join(', '),
        creator: 'Seafood Manager Temperature Monitoring System'
      });

      let currentPage = 1;
      const totalSections = pdfTemplate.sections.length;

      for (let i = 0; i < pdfTemplate.sections.length; i++) {
        const section = pdfTemplate.sections[i];
        const sectionProgress = Math.floor((i / totalSections) * 80) + 10; // 10-90% for sections

        this.updateProgress({
          stage: i < totalSections / 2 ? 'charts' : 'data',
          progress: sectionProgress,
          currentTask: `Generating ${section.type} section: ${section.title || 'Content'}`
        });

        if (i > 0 && section.pageBreak) {
          pdf.addPage();
          currentPage++;
        }

        await this.renderSection(pdf, section, params);

        // Small delay to prevent blocking
        await new Promise(resolve => setTimeout(resolve, 50));
      }

      this.updateProgress({ stage: 'rendering', progress: 90, currentTask: 'Finalizing PDF document' });

      // Add page numbers and footers
      this.addPageNumbersAndFooters(pdf, currentPage);

      this.updateProgress({ stage: 'complete', progress: 100, currentTask: 'PDF generation completed' });

      return pdf.output('arraybuffer');
    } catch (error) {
      this.updateProgress({
        stage: 'error',
        progress: 0,
        error: error instanceof Error ? error.message : 'Unknown error occurred'
      });
      throw error;
    }
  }

  /**
   * Generate specific chart as image buffer
   */
  async generateChart(
    type: PDFChart['type'],
    data: ChartDataPoint[],
    options: ChartOptions = {},
    dimensions: { width: number; height: number } = { width: 800, height: 400 }
  ): Promise<string> {
    this.canvas.width = dimensions.width;
    this.canvas.height = dimensions.height;

    // Clear canvas
    this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);
    this.ctx.fillStyle = '#ffffff';
    this.ctx.fillRect(0, 0, this.canvas.width, this.canvas.height);

    // Set up chart area
    const padding = { top: 40, right: 40, bottom: 60, left: 80 };
    const chartArea = {
      x: padding.left,
      y: padding.top,
      width: this.canvas.width - padding.left - padding.right,
      height: this.canvas.height - padding.top - padding.bottom
    };

    switch (type) {
      case 'line':
        this.drawLineChart(data, chartArea, options);
        break;
      case 'bar':
        this.drawBarChart(data, chartArea, options);
        break;
      case 'pie':
        this.drawPieChart(data, chartArea, options);
        break;
      case 'scatter':
        this.drawScatterChart(data, chartArea, options);
        break;
    }

    return this.canvas.toDataURL('image/png');
  }

  /**
   * Get report template configuration
   */
  private getTemplate(templateName: string, reportData: TemperatureReportData, params: TemperatureReportParams): PDFTemplate {
    const templates: Record<string, PDFTemplate> = {
      comprehensive: this.getComprehensiveTemplate(reportData, params),
      daily: this.getDailySummaryTemplate(reportData, params),
      haccp: this.getHACCPComplianceTemplate(reportData, params),
      performance: this.getSensorPerformanceTemplate(reportData, params),
      alerts: this.getAlertHistoryTemplate(reportData, params)
    };

    return templates[templateName] || templates.comprehensive;
  }

  /**
   * Comprehensive report template
   */
  private getComprehensiveTemplate(reportData: TemperatureReportData, params: TemperatureReportParams): PDFTemplate {
    const startDate = new Date(reportData.summary.reportPeriod.start);
    const endDate = new Date(reportData.summary.reportPeriod.end);

    return {
      name: 'comprehensive',
      description: 'Complete temperature monitoring report with all sections',
      metadata: {
        title: `Temperature Monitoring Report - ${startDate.toLocaleDateString()} to ${endDate.toLocaleDateString()}`,
        subject: 'HACCP Temperature Compliance and Monitoring Report',
        author: 'Pacific Cloud Seafoods - Seafood Manager',
        keywords: ['temperature', 'monitoring', 'HACCP', 'compliance', 'seafood', 'food safety']
      },
      sections: [
        {
          type: 'cover',
          content: {
            title: 'Temperature Monitoring Report',
            subtitle: `${startDate.toLocaleDateString()} - ${endDate.toLocaleDateString()}`,
            organization: 'Pacific Cloud Seafoods',
            generatedDate: new Date(),
            reportType: 'Comprehensive Analysis',
            confidentialityLevel: 'Internal Use'
          },
          pageBreak: true
        },
        {
          type: 'summary',
          title: 'Executive Summary',
          content: reportData.summary,
          pageBreak: true
        },
        {
          type: 'chart',
          title: 'Temperature Trends Overview',
          content: {
            type: 'line',
            data: this.prepareTemperatureTrendData(reportData.sensorData),
            options: {
              xLabel: 'Time',
              yLabel: 'Temperature (°F)',
              showGrid: true,
              showLegend: true,
              temperatureUnit: 'F',
              dateFormat: 'MM/dd HH:mm'
            }
          }
        },
        {
          type: 'table',
          title: 'Sensor Performance Summary',
          content: {
            headers: ['Sensor', 'Location', 'Avg Temp', 'Min Temp', 'Max Temp', 'Readings', 'Alerts', 'Status'],
            rows: reportData.sensorData.map(sensor => [
              sensor.sensor.name,
              sensor.sensor.location,
              `${sensor.statistics.avgTemp.toFixed(1)}°F`,
              `${sensor.statistics.minTemp.toFixed(1)}°F`,
              `${sensor.statistics.maxTemp.toFixed(1)}°F`,
              sensor.statistics.readingsCount.toString(),
              sensor.statistics.alertsCount.toString(),
              sensor.statistics.alertsCount === 0 ? 'Good' : 'Alerts Present'
            ])
          }
        },
        ...(params.includeHACCPData && reportData.haccpCompliance.length > 0 ? [{
          type: 'compliance' as const,
          title: 'HACCP Compliance Analysis',
          content: reportData.haccpCompliance,
          pageBreak: true
        }] : []),
        ...(params.includeAlerts && reportData.alerts.length > 0 ? [{
          type: 'alerts' as const,
          title: 'Alert History and Analysis',
          content: reportData.alerts,
          pageBreak: true
        }] : []),
        {
          type: 'chart',
          title: 'Temperature Distribution by Sensor',
          content: {
            type: 'bar',
            data: this.prepareTemperatureDistributionData(reportData.sensorData),
            options: {
              xLabel: 'Sensors',
              yLabel: 'Average Temperature (°F)',
              showGrid: true,
              temperatureUnit: 'F'
            }
          }
        }
      ]
    };
  }

  /**
   * Daily summary template
   */
  private getDailySummaryTemplate(reportData: TemperatureReportData, params: TemperatureReportParams): PDFTemplate {
    return {
      name: 'daily',
      description: '24-hour temperature monitoring summary',
      metadata: {
        title: `Daily Temperature Summary - ${new Date().toLocaleDateString()}`,
        subject: 'Daily Temperature Monitoring Report',
        author: 'Pacific Cloud Seafoods - Seafood Manager',
        keywords: ['daily', 'temperature', 'summary', 'monitoring']
      },
      sections: [
        {
          type: 'cover',
          content: {
            title: 'Daily Temperature Summary',
            subtitle: new Date().toLocaleDateString(),
            organization: 'Pacific Cloud Seafoods',
            generatedDate: new Date(),
            reportType: '24-Hour Summary'
          }
        },
        {
          type: 'summary',
          title: 'Daily Overview',
          content: reportData.summary
        },
        {
          type: 'chart',
          title: '24-Hour Temperature Trends',
          content: {
            type: 'line',
            data: this.prepareHourlyTrendData(reportData.sensorData),
            options: {
              xLabel: 'Hour',
              yLabel: 'Temperature (°F)',
              showGrid: true,
              temperatureUnit: 'F',
              dateFormat: 'HH:mm'
            }
          }
        },
        {
          type: 'table',
          title: 'Sensor Status',
          content: {
            headers: ['Sensor', 'Current Temp', 'Min/Max', 'Status'],
            rows: reportData.sensorData.map(sensor => [
              sensor.sensor.name,
              `${sensor.readings[sensor.readings.length - 1]?.temperature.toFixed(1) || 'N/A'}°F`,
              `${sensor.statistics.minTemp.toFixed(1)}°F / ${sensor.statistics.maxTemp.toFixed(1)}°F`,
              sensor.statistics.alertsCount === 0 ? '✓ Normal' : `⚠ ${sensor.statistics.alertsCount} alerts`
            ])
          }
        }
      ]
    };
  }

  /**
   * HACCP compliance template
   */
  private getHACCPComplianceTemplate(reportData: TemperatureReportData, params: TemperatureReportParams): PDFTemplate {
    return {
      name: 'haccp',
      description: 'HACCP compliance and critical control point monitoring',
      metadata: {
        title: 'HACCP Temperature Compliance Report',
        subject: 'Critical Control Point Temperature Monitoring',
        author: 'Pacific Cloud Seafoods - Seafood Manager',
        keywords: ['HACCP', 'compliance', 'critical control point', 'food safety', 'temperature']
      },
      sections: [
        {
          type: 'cover',
          content: {
            title: 'HACCP Compliance Report',
            subtitle: 'Critical Control Point Temperature Monitoring',
            organization: 'Pacific Cloud Seafoods',
            generatedDate: new Date(),
            reportType: 'HACCP Compliance Analysis'
          },
          pageBreak: true
        },
        {
          type: 'compliance',
          title: 'HACCP Compliance Overview',
          content: reportData.haccpCompliance
        },
        {
          type: 'chart',
          title: 'Compliance Rate by Storage Area',
          content: {
            type: 'pie',
            data: this.prepareComplianceDistributionData(reportData.haccpCompliance),
            options: {
              showLegend: true
            }
          }
        },
        {
          type: 'alerts',
          title: 'HACCP Violations and Corrective Actions',
          content: reportData.alerts.filter(alert => alert.alert_type === 'haccp_violation')
        }
      ]
    };
  }

  /**
   * Sensor performance template
   */
  private getSensorPerformanceTemplate(reportData: TemperatureReportData, params: TemperatureReportParams): PDFTemplate {
    return {
      name: 'performance',
      description: 'Detailed sensor performance and statistics analysis',
      metadata: {
        title: 'Sensor Performance Analysis Report',
        subject: 'Temperature Sensor Performance and Statistics',
        author: 'Pacific Cloud Seafoods - Seafood Manager',
        keywords: ['sensor', 'performance', 'statistics', 'analysis', 'temperature']
      },
      sections: [
        {
          type: 'cover',
          content: {
            title: 'Sensor Performance Analysis',
            subtitle: 'Long-term Trends and Statistics',
            organization: 'Pacific Cloud Seafoods',
            generatedDate: new Date(),
            reportType: 'Performance Analysis'
          },
          pageBreak: true
        },
        {
          type: 'chart',
          title: 'Sensor Reliability Overview',
          content: {
            type: 'bar',
            data: this.prepareSensorReliabilityData(reportData.sensorData),
            options: {
              xLabel: 'Sensors',
              yLabel: 'Uptime Percentage',
              showGrid: true
            }
          }
        },
        {
          type: 'table',
          title: 'Detailed Sensor Statistics',
          content: {
            headers: ['Sensor', 'Location', 'Total Readings', 'Avg Temp', 'Std Dev', 'Min/Max', 'Alerts', 'Uptime'],
            rows: reportData.sensorData.map(sensor => {
              const stdDev = this.calculateStandardDeviation(sensor.readings.map(r => r.temperature));
              const uptime = ((sensor.statistics.readingsCount / this.getExpectedReadings(params)) * 100).toFixed(1);
              
              return [
                sensor.sensor.name,
                sensor.sensor.location,
                sensor.statistics.readingsCount.toString(),
                `${sensor.statistics.avgTemp.toFixed(1)}°F`,
                `${stdDev.toFixed(2)}°F`,
                `${sensor.statistics.minTemp.toFixed(1)}°F / ${sensor.statistics.maxTemp.toFixed(1)}°F`,
                sensor.statistics.alertsCount.toString(),
                `${uptime}%`
              ];
            })
          }
        }
      ]
    };
  }

  /**
   * Alert history template
   */
  private getAlertHistoryTemplate(reportData: TemperatureReportData, params: TemperatureReportParams): PDFTemplate {
    return {
      name: 'alerts',
      description: 'Comprehensive alert history and resolution tracking',
      metadata: {
        title: 'Temperature Alert History Report',
        subject: 'Alert Documentation and Analysis',
        author: 'Pacific Cloud Seafoods - Seafood Manager',
        keywords: ['alerts', 'violations', 'corrective actions', 'temperature', 'history']
      },
      sections: [
        {
          type: 'cover',
          content: {
            title: 'Alert History Report',
            subtitle: 'Comprehensive Alert Documentation',
            organization: 'Pacific Cloud Seafoods',
            generatedDate: new Date(),
            reportType: 'Alert Analysis'
          },
          pageBreak: true
        },
        {
          type: 'chart',
          title: 'Alert Trends Over Time',
          content: {
            type: 'line',
            data: this.prepareAlertTrendData(reportData.alerts),
            options: {
              xLabel: 'Time',
              yLabel: 'Number of Alerts',
              showGrid: true
            }
          }
        },
        {
          type: 'alerts',
          title: 'Detailed Alert History',
          content: reportData.alerts
        }
      ]
    };
  }

  /**
   * Render a PDF section
   */
  private async renderSection(pdf: any, section: PDFSection, params: TemperatureReportParams): Promise<void> {
    switch (section.type) {
      case 'cover':
        this.renderCoverPage(pdf, section.content);
        break;
      case 'summary':
        this.renderSummarySection(pdf, section.title!, section.content);
        break;
      case 'chart':
        await this.renderChartSection(pdf, section.title!, section.content);
        break;
      case 'table':
        this.renderTableSection(pdf, section.title!, section.content);
        break;
      case 'compliance':
        this.renderComplianceSection(pdf, section.title!, section.content);
        break;
      case 'alerts':
        this.renderAlertsSection(pdf, section.title!, section.content);
        break;
      case 'text':
        this.renderTextSection(pdf, section.title!, section.content);
        break;
    }
  }

  /**
   * Render cover page
   */
  private renderCoverPage(pdf: any, content: any): void {
    const pageHeight = pdf.internal.pageSize.height;
    const pageWidth = pdf.internal.pageSize.width;

    // Company logo area (placeholder)
    pdf.setFillColor(59, 130, 246); // Blue header
    pdf.rect(0, 0, pageWidth, 40, 'F');

    // White text on blue background
    pdf.setTextColor(255, 255, 255);
    pdf.setFont('helvetica', 'bold');
    pdf.setFontSize(24);
    pdf.text(content.organization, pageWidth / 2, 25, { align: 'center' });

    // Reset text color
    pdf.setTextColor(0, 0, 0);

    // Main title
    pdf.setFont('helvetica', 'bold');
    pdf.setFontSize(32);
    pdf.text(content.title, pageWidth / 2, pageHeight / 2 - 30, { align: 'center' });

    // Subtitle
    pdf.setFont('helvetica', 'normal');
    pdf.setFontSize(18);
    pdf.text(content.subtitle, pageWidth / 2, pageHeight / 2 - 10, { align: 'center' });

    // Report type
    pdf.setFontSize(14);
    pdf.setTextColor(100, 100, 100);
    pdf.text(content.reportType, pageWidth / 2, pageHeight / 2 + 10, { align: 'center' });

    // Generated date
    pdf.setFontSize(12);
    pdf.text(`Generated: ${content.generatedDate.toLocaleString()}`, pageWidth / 2, pageHeight - 40, { align: 'center' });

    // Confidentiality notice
    if (content.confidentialityLevel) {
      pdf.setFontSize(10);
      pdf.setTextColor(200, 0, 0);
      pdf.text(`Confidentiality: ${content.confidentialityLevel}`, pageWidth / 2, pageHeight - 20, { align: 'center' });
    }
  }

  /**
   * Render summary section
   */
  private renderSummarySection(pdf: any, title: string, summary: any): void {
    let yPos = 40;

    // Section title
    pdf.setFont('helvetica', 'bold');
    pdf.setFontSize(18);
    pdf.setTextColor(0, 0, 0);
    pdf.text(title, 20, yPos);
    yPos += 20;

    // Summary cards in a grid
    const cardWidth = 45;
    const cardHeight = 25;
    const spacing = 5;

    const summaryData = [
      { label: 'Sensors Monitored', value: summary.sensorsIncluded, color: [59, 130, 246] },
      { label: 'Total Readings', value: summary.totalReadings, color: [34, 197, 94] },
      { label: 'Total Alerts', value: summary.totalAlerts, color: summary.totalAlerts > 0 ? [239, 68, 68] : [34, 197, 94] },
      { label: 'Compliance Rate', value: `${summary.complianceRate.toFixed(1)}%`, color: summary.complianceRate >= 95 ? [34, 197, 94] : [239, 68, 68] }
    ];

    for (let i = 0; i < summaryData.length; i++) {
      const x = 20 + (i % 2) * (cardWidth + spacing);
      const y = yPos + Math.floor(i / 2) * (cardHeight + spacing);

      // Card background
      pdf.setFillColor(248, 249, 250);
      pdf.rect(x, y, cardWidth, cardHeight, 'F');

      // Card border
      pdf.setDrawColor(229, 231, 235);
      pdf.rect(x, y, cardWidth, cardHeight);

      // Value
      pdf.setFont('helvetica', 'bold');
      pdf.setFontSize(20);
      pdf.setTextColor(summaryData[i].color[0], summaryData[i].color[1], summaryData[i].color[2]);
      pdf.text(summaryData[i].value.toString(), x + cardWidth / 2, y + 12, { align: 'center' });

      // Label
      pdf.setFont('helvetica', 'normal');
      pdf.setFontSize(10);
      pdf.setTextColor(107, 114, 128);
      pdf.text(summaryData[i].label, x + cardWidth / 2, y + 20, { align: 'center' });
    }

    // Report period
    yPos += 60;
    pdf.setFont('helvetica', 'normal');
    pdf.setFontSize(12);
    pdf.setTextColor(0, 0, 0);
    pdf.text(`Report Period: ${summary.reportPeriod.start} to ${summary.reportPeriod.end}`, 20, yPos);
  }

  /**
   * Render chart section
   */
  private async renderChartSection(pdf: any, title: string, chartConfig: any): Promise<void> {
    let yPos = 40;

    // Section title
    pdf.setFont('helvetica', 'bold');
    pdf.setFontSize(16);
    pdf.text(title, 20, yPos);
    yPos += 20;

    // Generate chart
    const chartImage = await this.generateChart(
      chartConfig.type,
      chartConfig.data,
      chartConfig.options,
      { width: 600, height: 300 }
    );

    // Add chart to PDF
    pdf.addImage(chartImage, 'PNG', 20, yPos, 170, 85);
  }

  /**
   * Render table section
   */
  private renderTableSection(pdf: any, title: string, tableData: any): void {
    let yPos = 40;

    // Section title
    pdf.setFont('helvetica', 'bold');
    pdf.setFontSize(16);
    pdf.text(title, 20, yPos);
    yPos += 15;

    // Auto table
    (pdf as any).autoTable({
      head: [tableData.headers],
      body: tableData.rows,
      startY: yPos,
      styles: {
        fontSize: 10,
        cellPadding: 3
      },
      headStyles: {
        fillColor: [59, 130, 246],
        textColor: 255,
        fontStyle: 'bold'
      },
      alternateRowStyles: {
        fillColor: [248, 249, 250]
      }
    });
  }

  /**
   * Render compliance section
   */
  private renderComplianceSection(pdf: any, title: string, complianceData: any[]): void {
    let yPos = 40;

    // Section title
    pdf.setFont('helvetica', 'bold');
    pdf.setFontSize(16);
    pdf.text(title, 20, yPos);
    yPos += 20;

    // Compliance overview
    const overallCompliance = complianceData.reduce((acc, area) => acc + area.complianceRate, 0) / complianceData.length;
    
    pdf.setFont('helvetica', 'normal');
    pdf.setFontSize(12);
    pdf.text(`Overall HACCP Compliance Rate: ${overallCompliance.toFixed(1)}%`, 20, yPos);
    yPos += 15;

    // Compliance table
    const tableData = {
      headers: ['Storage Area', 'Type', 'Compliance Rate', 'Violations', 'Critical Violations', 'Status'],
      rows: complianceData.map(area => [
        area.storageArea.name,
        area.storageArea.area_type,
        `${area.complianceRate.toFixed(1)}%`,
        area.violationsCount.toString(),
        area.criticalViolations.length.toString(),
        area.complianceRate >= 95 ? 'Compliant' : 'Needs Attention'
      ])
    };

    (pdf as any).autoTable({
      head: [tableData.headers],
      body: tableData.rows,
      startY: yPos,
      styles: {
        fontSize: 10,
        cellPadding: 3
      },
      headStyles: {
        fillColor: [239, 68, 68],
        textColor: 255,
        fontStyle: 'bold'
      },
      bodyStyles: {
        textColor(data: any) {
          if (data.column.index === 5) {
            return data.cell.raw === 'Compliant' ? [34, 197, 94] : [239, 68, 68];
          }
          return [0, 0, 0];
        }
      }
    });
  }

  /**
   * Render alerts section
   */
  private renderAlertsSection(pdf: any, title: string, alerts: TemperatureAlert[]): void {
    let yPos = 40;

    // Section title
    pdf.setFont('helvetica', 'bold');
    pdf.setFontSize(16);
    pdf.text(title, 20, yPos);
    yPos += 15;

    if (alerts.length === 0) {
      pdf.setFont('helvetica', 'normal');
      pdf.setFontSize(12);
      pdf.setTextColor(34, 197, 94);
      pdf.text('✓ No alerts during this period', 20, yPos);
      return;
    }

    // Alert summary
    const criticalAlerts = alerts.filter(a => a.severity_level === 'critical').length;
    const haccpViolations = alerts.filter(a => a.alert_type === 'haccp_violation').length;

    pdf.setFont('helvetica', 'normal');
    pdf.setFontSize(12);
    pdf.setTextColor(0, 0, 0);
    pdf.text(`Total Alerts: ${alerts.length} | Critical: ${criticalAlerts} | HACCP Violations: ${haccpViolations}`, 20, yPos);
    yPos += 15;

    // Alerts table (limited to first 50 for space)
    const displayAlerts = alerts.slice(0, 50);
    const tableData = {
      headers: ['Time', 'Sensor', 'Alert Type', 'Temperature', 'Severity', 'Status'],
      rows: displayAlerts.map(alert => [
        new Date(alert.alert_timestamp).toLocaleString(),
        alert.sensors?.name || 'Unknown',
        alert.alert_type.replace(/_/g, ' '),
        alert.temperature ? `${alert.temperature.toFixed(1)}°F` : 'N/A',
        alert.severity_level.toUpperCase(),
        alert.resolved_timestamp ? 'Resolved' : 'Active'
      ])
    };

    (pdf as any).autoTable({
      head: [tableData.headers],
      body: tableData.rows,
      startY: yPos,
      styles: {
        fontSize: 9,
        cellPadding: 2
      },
      headStyles: {
        fillColor: [245, 158, 11],
        textColor: 255,
        fontStyle: 'bold'
      },
      bodyStyles: {
        textColor(data: any) {
          if (data.column.index === 4) { // Severity column
            switch (data.cell.raw) {
              case 'CRITICAL': return [239, 68, 68];
              case 'HIGH': return [245, 158, 11];
              case 'MEDIUM': return [156, 163, 175];
              default: return [0, 0, 0];
            }
          }
          if (data.column.index === 5) { // Status column
            return data.cell.raw === 'Resolved' ? [34, 197, 94] : [239, 68, 68];
          }
          return [0, 0, 0];
        }
      }
    });

    if (alerts.length > 50) {
      const finalY = (pdf as any).lastAutoTable.finalY + 10;
      pdf.setFont('helvetica', 'italic');
      pdf.setFontSize(10);
      pdf.setTextColor(107, 114, 128);
      pdf.text(`Note: Showing first 50 of ${alerts.length} alerts. Full data available in raw export.`, 20, finalY);
    }
  }

  /**
   * Render text section
   */
  private renderTextSection(pdf: any, title: string, content: string): void {
    let yPos = 40;

    // Section title
    pdf.setFont('helvetica', 'bold');
    pdf.setFontSize(16);
    pdf.text(title, 20, yPos);
    yPos += 20;

    // Content
    pdf.setFont('helvetica', 'normal');
    pdf.setFontSize(11);
    
    const lines = pdf.splitTextToSize(content, 170);
    pdf.text(lines, 20, yPos);
  }

  /**
   * Add page numbers and footers
   */
  private addPageNumbersAndFooters(pdf: any, totalPages: number): void {
    for (let i = 1; i <= totalPages; i++) {
      pdf.setPage(i);
      
      // Page number
      pdf.setFont('helvetica', 'normal');
      pdf.setFontSize(10);
      pdf.setTextColor(107, 114, 128);
      pdf.text(`Page ${i} of ${totalPages}`, pdf.internal.pageSize.width - 30, pdf.internal.pageSize.height - 10, { align: 'right' });
      
      // Footer
      pdf.text('Pacific Cloud Seafoods - Seafood Manager', 20, pdf.internal.pageSize.height - 10);
      pdf.text('Confidential - Internal Use Only', pdf.internal.pageSize.width / 2, pdf.internal.pageSize.height - 10, { align: 'center' });
    }
  }

  /**
   * Draw line chart on canvas
   */
  private drawLineChart(data: ChartDataPoint[], chartArea: any, options: ChartOptions): void {
    if (data.length === 0) return;

    const { ctx } = this;
    
    // Find data ranges
    const xValues = data.map(d => typeof d.x === 'number' ? d.x : new Date(d.x).getTime());
    const yValues = data.map(d => d.y);
    const xMin = Math.min(...xValues);
    const xMax = Math.max(...xValues);
    const yMin = Math.min(...yValues) - 5;
    const yMax = Math.max(...yValues) + 5;

    // Draw grid
    if (options.showGrid) {
      ctx.strokeStyle = '#e5e7eb';
      ctx.lineWidth = 1;
      
      // Vertical grid lines
      for (let i = 0; i <= 10; i++) {
        const x = chartArea.x + (chartArea.width / 10) * i;
        ctx.beginPath();
        ctx.moveTo(x, chartArea.y);
        ctx.lineTo(x, chartArea.y + chartArea.height);
        ctx.stroke();
      }
      
      // Horizontal grid lines
      for (let i = 0; i <= 10; i++) {
        const y = chartArea.y + (chartArea.height / 10) * i;
        ctx.beginPath();
        ctx.moveTo(chartArea.x, y);
        ctx.lineTo(chartArea.x + chartArea.width, y);
        ctx.stroke();
      }
    }

    // Draw axes
    ctx.strokeStyle = '#374151';
    ctx.lineWidth = 2;
    ctx.beginPath();
    // X-axis
    ctx.moveTo(chartArea.x, chartArea.y + chartArea.height);
    ctx.lineTo(chartArea.x + chartArea.width, chartArea.y + chartArea.height);
    // Y-axis
    ctx.moveTo(chartArea.x, chartArea.y);
    ctx.lineTo(chartArea.x, chartArea.y + chartArea.height);
    ctx.stroke();

    // Draw alert thresholds
    if (options.alertThresholds) {
      ctx.strokeStyle = '#ef4444';
      ctx.lineWidth = 2;
      ctx.setLineDash([5, 5]);

      if (options.alertThresholds.min) {
        const y = chartArea.y + chartArea.height - ((options.alertThresholds.min - yMin) / (yMax - yMin)) * chartArea.height;
        ctx.beginPath();
        ctx.moveTo(chartArea.x, y);
        ctx.lineTo(chartArea.x + chartArea.width, y);
        ctx.stroke();
      }

      if (options.alertThresholds.max) {
        const y = chartArea.y + chartArea.height - ((options.alertThresholds.max - yMin) / (yMax - yMin)) * chartArea.height;
        ctx.beginPath();
        ctx.moveTo(chartArea.x, y);
        ctx.lineTo(chartArea.x + chartArea.width, y);
        ctx.stroke();
      }
    }

    // Reset line dash
    ctx.setLineDash([]);

    // Draw data line
    ctx.strokeStyle = options.colors?.[0] || '#3b82f6';
    ctx.lineWidth = 3;
    ctx.beginPath();

    data.forEach((point, index) => {
      const x = chartArea.x + ((typeof point.x === 'number' ? point.x : new Date(point.x).getTime()) - xMin) / (xMax - xMin) * chartArea.width;
      const y = chartArea.y + chartArea.height - (point.y - yMin) / (yMax - yMin) * chartArea.height;

      if (index === 0) {
        ctx.moveTo(x, y);
      } else {
        ctx.lineTo(x, y);
      }
    });

    ctx.stroke();

    // Draw data points
    ctx.fillStyle = options.colors?.[0] || '#3b82f6';
    data.forEach(point => {
      const x = chartArea.x + ((typeof point.x === 'number' ? point.x : new Date(point.x).getTime()) - xMin) / (xMax - xMin) * chartArea.width;
      const y = chartArea.y + chartArea.height - (point.y - yMin) / (yMax - yMin) * chartArea.height;

      ctx.beginPath();
      ctx.arc(x, y, 3, 0, 2 * Math.PI);
      ctx.fill();
    });

    // Draw labels
    ctx.fillStyle = '#374151';
    ctx.font = '12px sans-serif';
    ctx.textAlign = 'center';

    // X-axis labels
    if (options.xLabel) {
      ctx.fillText(options.xLabel, chartArea.x + chartArea.width / 2, this.canvas.height - 20);
    }

    // Y-axis labels
    if (options.yLabel) {
      ctx.save();
      ctx.translate(15, chartArea.y + chartArea.height / 2);
      ctx.rotate(-Math.PI / 2);
      ctx.fillText(options.yLabel, 0, 0);
      ctx.restore();
    }

    // Value labels on axes
    ctx.font = '10px sans-serif';
    
    // Y-axis values
    ctx.textAlign = 'right';
    for (let i = 0; i <= 5; i++) {
      const value = yMin + (yMax - yMin) * (i / 5);
      const y = chartArea.y + chartArea.height - (i / 5) * chartArea.height;
      ctx.fillText(`${value.toFixed(1)}°${options.temperatureUnit || 'F'}`, chartArea.x - 5, y + 4);
    }

    // X-axis values (simplified)
    ctx.textAlign = 'center';
    for (let i = 0; i <= 5; i++) {
      const valueIndex = Math.floor((data.length - 1) * (i / 5));
      const point = data[valueIndex];
      if (point) {
        const x = chartArea.x + (chartArea.width / 5) * i;
        let label = '';
        if (typeof point.x === 'string') {
          const date = new Date(point.x);
          label = options.dateFormat === 'HH:mm' ? 
            date.toLocaleTimeString('en-US', { hour: '2-digit', minute: '2-digit' }) :
            date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' });
        } else {
          label = point.x.toString();
        }
        ctx.fillText(label, x, chartArea.y + chartArea.height + 15);
      }
    }
  }

  /**
   * Draw bar chart on canvas
   */
  private drawBarChart(data: ChartDataPoint[], chartArea: any, options: ChartOptions): void {
    if (data.length === 0) return;

    const { ctx } = this;
    const barWidth = chartArea.width / data.length * 0.8;
    const barSpacing = chartArea.width / data.length * 0.2;
    
    const yValues = data.map(d => d.y);
    const yMin = Math.min(0, Math.min(...yValues) - 5);
    const yMax = Math.max(...yValues) + 5;

    // Draw grid
    if (options.showGrid) {
      ctx.strokeStyle = '#e5e7eb';
      ctx.lineWidth = 1;
      
      for (let i = 0; i <= 10; i++) {
        const y = chartArea.y + (chartArea.height / 10) * i;
        ctx.beginPath();
        ctx.moveTo(chartArea.x, y);
        ctx.lineTo(chartArea.x + chartArea.width, y);
        ctx.stroke();
      }
    }

    // Draw bars
    data.forEach((point, index) => {
      const x = chartArea.x + barSpacing / 2 + index * (barWidth + barSpacing);
      const barHeight = (point.y - yMin) / (yMax - yMin) * chartArea.height;
      const y = chartArea.y + chartArea.height - barHeight;

      ctx.fillStyle = point.color || options.colors?.[index % (options.colors?.length || 1)] || '#3b82f6';
      ctx.fillRect(x, y, barWidth, barHeight);

      // Value label on top of bar
      ctx.fillStyle = '#374151';
      ctx.font = '10px sans-serif';
      ctx.textAlign = 'center';
      ctx.fillText(`${point.y.toFixed(1)}`, x + barWidth / 2, y - 5);

      // X-axis label
      ctx.fillText(point.label || point.x.toString(), x + barWidth / 2, chartArea.y + chartArea.height + 15);
    });

    // Draw axes
    ctx.strokeStyle = '#374151';
    ctx.lineWidth = 2;
    ctx.beginPath();
    ctx.moveTo(chartArea.x, chartArea.y + chartArea.height);
    ctx.lineTo(chartArea.x + chartArea.width, chartArea.y + chartArea.height);
    ctx.moveTo(chartArea.x, chartArea.y);
    ctx.lineTo(chartArea.x, chartArea.y + chartArea.height);
    ctx.stroke();

    // Axis labels
    ctx.fillStyle = '#374151';
    ctx.font = '12px sans-serif';
    ctx.textAlign = 'center';

    if (options.xLabel) {
      ctx.fillText(options.xLabel, chartArea.x + chartArea.width / 2, this.canvas.height - 20);
    }

    if (options.yLabel) {
      ctx.save();
      ctx.translate(15, chartArea.y + chartArea.height / 2);
      ctx.rotate(-Math.PI / 2);
      ctx.fillText(options.yLabel, 0, 0);
      ctx.restore();
    }
  }

  /**
   * Draw pie chart on canvas
   */
  private drawPieChart(data: ChartDataPoint[], chartArea: any, options: ChartOptions): void {
    if (data.length === 0) return;

    const { ctx } = this;
    const centerX = chartArea.x + chartArea.width / 2;
    const centerY = chartArea.y + chartArea.height / 2;
    const radius = Math.min(chartArea.width, chartArea.height) / 2 - 20;

    const total = data.reduce((sum, point) => sum + point.y, 0);
    let currentAngle = -Math.PI / 2; // Start at top

    const colors = options.colors || [
      '#3b82f6', '#ef4444', '#10b981', '#f59e0b', '#8b5cf6', 
      '#f97316', '#06b6d4', '#84cc16', '#ec4899', '#64748b'
    ];

    data.forEach((point, index) => {
      const sliceAngle = (point.y / total) * 2 * Math.PI;
      const color = point.color || colors[index % colors.length];

      // Draw slice
      ctx.fillStyle = color;
      ctx.beginPath();
      ctx.moveTo(centerX, centerY);
      ctx.arc(centerX, centerY, radius, currentAngle, currentAngle + sliceAngle);
      ctx.closePath();
      ctx.fill();

      // Draw slice border
      ctx.strokeStyle = '#ffffff';
      ctx.lineWidth = 2;
      ctx.stroke();

      // Draw percentage label
      const labelAngle = currentAngle + sliceAngle / 2;
      const labelX = centerX + Math.cos(labelAngle) * (radius * 0.7);
      const labelY = centerY + Math.sin(labelAngle) * (radius * 0.7);
      
      const percentage = ((point.y / total) * 100).toFixed(1);
      
      ctx.fillStyle = '#ffffff';
      ctx.font = 'bold 12px sans-serif';
      ctx.textAlign = 'center';
      ctx.fillText(`${percentage}%`, labelX, labelY);

      currentAngle += sliceAngle;
    });

    // Draw legend
    if (options.showLegend) {
      const legendX = chartArea.x + chartArea.width + 20;
      let legendY = chartArea.y + 20;

      ctx.font = '11px sans-serif';
      ctx.textAlign = 'left';

      data.forEach((point, index) => {
        const color = point.color || colors[index % colors.length];
        
        // Legend color box
        ctx.fillStyle = color;
        ctx.fillRect(legendX, legendY - 8, 12, 12);
        
        // Legend text
        ctx.fillStyle = '#374151';
        ctx.fillText(`${point.label || point.x} (${point.y})`, legendX + 18, legendY);
        
        legendY += 18;
      });
    }
  }

  /**
   * Draw scatter chart on canvas
   */
  private drawScatterChart(data: ChartDataPoint[], chartArea: any, options: ChartOptions): void {
    if (data.length === 0) return;

    const { ctx } = this;
    
    // Similar to line chart but with points only
    const xValues = data.map(d => typeof d.x === 'number' ? d.x : new Date(d.x).getTime());
    const yValues = data.map(d => d.y);
    const xMin = Math.min(...xValues);
    const xMax = Math.max(...xValues);
    const yMin = Math.min(...yValues) - 5;
    const yMax = Math.max(...yValues) + 5;

    // Draw grid and axes (same as line chart)
    if (options.showGrid) {
      ctx.strokeStyle = '#e5e7eb';
      ctx.lineWidth = 1;
      
      for (let i = 0; i <= 10; i++) {
        const x = chartArea.x + (chartArea.width / 10) * i;
        ctx.beginPath();
        ctx.moveTo(x, chartArea.y);
        ctx.lineTo(x, chartArea.y + chartArea.height);
        ctx.stroke();
        
        const y = chartArea.y + (chartArea.height / 10) * i;
        ctx.beginPath();
        ctx.moveTo(chartArea.x, y);
        ctx.lineTo(chartArea.x + chartArea.width, y);
        ctx.stroke();
      }
    }

    // Draw data points
    ctx.fillStyle = options.colors?.[0] || '#3b82f6';
    data.forEach(point => {
      const x = chartArea.x + ((typeof point.x === 'number' ? point.x : new Date(point.x).getTime()) - xMin) / (xMax - xMin) * chartArea.width;
      const y = chartArea.y + chartArea.height - (point.y - yMin) / (yMax - yMin) * chartArea.height;

      ctx.beginPath();
      ctx.arc(x, y, 4, 0, 2 * Math.PI);
      ctx.fill();
    });
  }

  // Data preparation methods
  private prepareTemperatureTrendData(sensorData: any[]): ChartDataPoint[] {
    const allReadings: ChartDataPoint[] = [];
    
    sensorData.forEach(sensor => {
      sensor.readings.forEach((reading: TemperatureReading) => {
        allReadings.push({
          x: reading.reading_timestamp,
          y: reading.temperature,
          label: sensor.sensor.name,
          metadata: { sensorId: sensor.sensor.id, sensorName: sensor.sensor.name }
        });
      });
    });

    // Sort by timestamp and limit to prevent overcrowding
    return allReadings
      .sort((a, b) => new Date(a.x as string).getTime() - new Date(b.x as string).getTime())
      .filter((_, index) => index % Math.ceil(allReadings.length / 100) === 0) // Sample data points
      .slice(0, 100);
  }

  private prepareTemperatureDistributionData(sensorData: any[]): ChartDataPoint[] {
    return sensorData.map(sensor => ({
      x: sensor.sensor.name,
      y: sensor.statistics.avgTemp,
      label: sensor.sensor.name,
      color: sensor.statistics.alertsCount > 0 ? '#ef4444' : '#3b82f6'
    }));
  }

  private prepareHourlyTrendData(sensorData: any[]): ChartDataPoint[] {
    const hourlyData = new Map<number, { total: number; count: number }>();

    sensorData.forEach(sensor => {
      sensor.readings.forEach((reading: TemperatureReading) => {
        const hour = new Date(reading.reading_timestamp).getHours();
        if (!hourlyData.has(hour)) {
          hourlyData.set(hour, { total: 0, count: 0 });
        }
        const data = hourlyData.get(hour)!;
        data.total += reading.temperature;
        data.count++;
      });
    });

    return Array.from(hourlyData.entries()).map(([hour, data]) => ({
      x: hour,
      y: data.total / data.count,
      label: `${hour}:00`
    }));
  }

  private prepareComplianceDistributionData(haccpCompliance: any[]): ChartDataPoint[] {
    return haccpCompliance.map(area => ({
      x: area.storageArea.name,
      y: area.complianceRate,
      label: `${area.storageArea.name}: ${area.complianceRate.toFixed(1)}%`,
      color: area.complianceRate >= 95 ? '#10b981' : area.complianceRate >= 90 ? '#f59e0b' : '#ef4444'
    }));
  }

  private prepareSensorReliabilityData(sensorData: any[]): ChartDataPoint[] {
    return sensorData.map(sensor => {
      // Calculate uptime based on expected readings
      const expectedReadings = 24 * 4; // Assuming readings every 15 minutes for last 24 hours
      const uptime = Math.min(100, (sensor.statistics.readingsCount / expectedReadings) * 100);
      
      return {
        x: sensor.sensor.name,
        y: uptime,
        label: sensor.sensor.name,
        color: uptime >= 95 ? '#10b981' : uptime >= 90 ? '#f59e0b' : '#ef4444'
      };
    });
  }

  private prepareAlertTrendData(alerts: TemperatureAlert[]): ChartDataPoint[] {
    const alertsByHour = new Map<string, number>();
    
    alerts.forEach(alert => {
      const hourKey = new Date(alert.alert_timestamp).toISOString().slice(0, 13); // YYYY-MM-DDTHH
      alertsByHour.set(hourKey, (alertsByHour.get(hourKey) || 0) + 1);
    });

    return Array.from(alertsByHour.entries())
      .sort(([a], [b]) => a.localeCompare(b))
      .map(([hourKey, count]) => ({
        x: `${hourKey  }:00:00Z`,
        y: count,
        label: new Date(`${hourKey  }:00:00Z`).toLocaleString()
      }));
  }

  // Utility methods
  private calculateStandardDeviation(values: number[]): number {
    const mean = values.reduce((sum, value) => sum + value, 0) / values.length;
    const squaredDifferences = values.map(value => Math.pow(value - mean, 2));
    const avgSquaredDiff = squaredDifferences.reduce((sum, value) => sum + value, 0) / values.length;
    return Math.sqrt(avgSquaredDiff);
  }

  private getExpectedReadings(params: TemperatureReportParams): number {
    const hours = Math.abs(new Date(params.endDate).getTime() - new Date(params.startDate).getTime()) / (1000 * 60 * 60);
    return Math.floor(hours * 4); // Assuming readings every 15 minutes
  }

  private updateProgress(progress: PDFGenerationProgress): void {
    if (this.progressCallback) {
      this.progressCallback(progress);
    }
  }
}

// Export singleton instance
export const pdfReportGenerator = PDFReportGenerator.getInstance();

export type { PDFChart, ChartDataPoint, ChartOptions, PDFSection, PDFTemplate, PDFGenerationProgress, PDFGenerationCallback };
import { useState, useEffect, useRef, type ReactNode } from 'react';
import type { ActiveView } from '../contexts/NavigationContext';
import { useTheme } from '../contexts/ThemeContext';
import {
  LayoutDashboard,
  Package,
  BarChart2,
  MessageSquare,
  Settings,
  ChevronLeft,
  ChevronRight,
  LogOut,
  Users,
  Building2,
  Upload,
  User,
  ClipboardCheck,
  Calendar,
  FileText,
  Mic,
  Sun,
  Moon,
  Monitor,
  Thermometer
} from 'lucide-react';

export interface SidebarProps {
  activeView: ActiveView;
  setActiveView: (view: ActiveView) => void;
  onSignOut: () => void;
  userEmail?: string;
  className?: string;
}

export default function Sidebar({ activeView, setActiveView, onSignOut, userEmail, className }: SidebarProps) {
  const [isCollapsed, setIsCollapsed] = useState(false);
  const [haccpOpen, setHaccpOpen] = useState(true);
  const [temperatureOpen, setTemperatureOpen] = useState(true);
  const haccpRef = useRef<HTMLDivElement>(null);
  const temperatureRef = useRef<HTMLDivElement>(null);
  const { theme, setTheme } = useTheme();

  const getThemeIcon = () => {
    switch (theme) {
      case 'light':
        return <Sun size={20} />;
      case 'dark':
        return <Moon size={20} />;
      case 'system':
        return <Monitor size={20} />;
      default:
        return <Monitor size={20} />;
    }
  };

  const cycleTheme = () => {
    const themes = ['light', 'dark', 'system'] as const;
    const currentIndex = themes.indexOf(theme);
    const nextIndex = (currentIndex + 1) % themes.length;
    setTheme(themes[nextIndex]);
  };

  // Close submenus when clicking outside (only when collapsed)
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (isCollapsed) {
        if (haccpOpen && haccpRef.current && !haccpRef.current.contains(event.target as Node)) {
          setHaccpOpen(false);
        }
        if (temperatureOpen && temperatureRef.current && !temperatureRef.current.contains(event.target as Node)) {
          setTemperatureOpen(false);
        }
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, [isCollapsed, haccpOpen, temperatureOpen]);

  const navItems: Array<{ icon: ReactNode; label: string; view: ActiveView }> = [
    { icon: <LayoutDashboard size={24} />, label: 'Dashboard', view: 'Dashboard' },
    { icon: <Package size={24} />, label: 'Inventory', view: 'Inventory' },
    { icon: <FileText size={24} />, label: 'Events', view: 'Events' },
    { icon: <Mic size={24} />, label: 'Voice Management', view: 'Voice Management' },
    { icon: <Building2 size={24} />, label: 'Vendors', view: 'Vendors' },
    { icon: <Users size={24} />, label: 'Customers', view: 'Customers' },
    { icon: <BarChart2 size={24} />, label: 'Analytics', view: 'Analytics' },
    { icon: <MessageSquare size={24} />, label: 'Messages', view: 'Messages' },
    { icon: <Settings size={24} />, label: 'Settings', view: 'Settings' },
    { icon: <Upload size={24} />, label: 'Import', view: 'Import' }
  ];

  return (
    <nav data-testid="sidebar" className={`h-screen bg-gray-800 text-white transition-all duration-300 ${isCollapsed ? 'w-20' : 'w-64'} ${className ?? ''}`}>
      <div className="flex flex-col h-full p-4">
        <div className="mb-6">
          {!isCollapsed && (
            <div>
              <h1 className="text-lg font-bold">Pacific Cloud</h1>
              <h2 className="text-sm text-gray-400">Seafoods</h2>
              <p className="text-xs text-gray-500">Inventory Manager</p>
            </div>
          )}
        </div>

        <button
          className="flex items-center justify-end cursor-pointer mb-6"
          onClick={() => setIsCollapsed(!isCollapsed)}
          aria-label={isCollapsed ? 'Expand sidebar' : 'Collapse sidebar'}
        >
          {isCollapsed ? <ChevronRight size={24} /> : <ChevronLeft size={24} />}
        </button>

        <div className="flex-1 space-y-2">
          {navItems.map((item) => (
            <button
              key={item.view}
              className={`w-full flex items-center space-x-2 p-3 rounded-lg transition-colors
                ${activeView === item.view ? 'bg-gray-700' : 'hover:bg-gray-700'}`}
              onClick={() => setActiveView(item.view)}
              aria-current={activeView === item.view ? 'page' : undefined}
            >
              <span className="min-w-[24px]">{item.icon}</span>
              {!isCollapsed && <span>{item.label}</span>}
            </button>
          ))}

          {/* HACCP group */}
          <div ref={haccpRef} className="space-y-1 relative">
            <button
              className={`w-full flex items-center justify-between p-3 rounded-lg transition-colors hover:bg-gray-700`}
              onClick={() => setHaccpOpen((v) => !v)}
              aria-expanded={haccpOpen ? 'true' : 'false'}
              aria-controls="haccp-subnav"
            >
              <div className="flex items-center space-x-2">
                <span className="min-w-[24px]"><ClipboardCheck size={24} /></span>
                {!isCollapsed && <span>HACCP Events</span>}
              </div>
              {!isCollapsed && (
                <span className="text-gray-300">{haccpOpen ? <ChevronLeft size={18} className="rotate-90" /> : <ChevronRight size={18} />}</span>
              )}
            </button>

            {haccpOpen && (
              <div id="haccp-subnav" className={`space-y-1 ${isCollapsed ? 'absolute left-20 top-0 bg-gray-800 rounded-lg shadow-lg p-2 min-w-[200px] z-50' : 'pl-10'}`}>
                <button
                  className={`w-full flex items-center space-x-2 p-2 rounded-lg transition-colors text-white ${activeView === 'HACCP: Batches' ? 'bg-gray-700' : 'hover:bg-gray-700'}`}
                  onClick={() => setActiveView('HACCP: Batches')}
                  aria-current={activeView === 'HACCP: Batches' ? 'page' : undefined}
                >
                  <span className="min-w-[20px]"><Package size={20} /></span>
                  <span className="text-sm">Batches</span>
                </button>
                <button
                  className={`w-full flex items-center space-x-2 p-2 rounded-lg transition-colors text-white ${activeView === 'HACCP: Events' || activeView === 'HACCP Events' ? 'bg-gray-700' : 'hover:bg-gray-700'}`}
                  onClick={() => setActiveView('HACCP: Events')}
                  aria-current={activeView === 'HACCP: Events' ? 'page' : undefined}
                >
                  <span className="min-w-[20px]"><ClipboardCheck size={20} /></span>
                  <span className="text-sm">Events</span>
                </button>
                <button
                  className={`w-full flex items-center space-x-2 p-2 rounded-lg transition-colors text-white ${activeView === 'HACCP: Calendar' ? 'bg-gray-700' : 'hover:bg-gray-700'}`}
                  onClick={() => setActiveView('HACCP: Calendar')}
                  aria-current={activeView === 'HACCP: Calendar' ? 'page' : undefined}
                >
                  <span className="min-w-[20px]"><Calendar size={20} /></span>
                  <span className="text-sm">Calendar</span>
                </button>
              </div>
            )}
          </div>

          {/* Temperature Monitoring group */}
          <div ref={temperatureRef} className="space-y-1 relative">
            <button
              className={`w-full flex items-center justify-between p-3 rounded-lg transition-colors hover:bg-gray-700`}
              onClick={() => setTemperatureOpen((v) => !v)}
              aria-expanded={temperatureOpen ? 'true' : 'false'}
              aria-controls="temperature-subnav"
            >
              <div className="flex items-center space-x-2">
                <span className="min-w-[24px]"><Thermometer size={24} /></span>
                {!isCollapsed && <span>Temperature</span>}
              </div>
              {!isCollapsed && (
                <span className="text-gray-300">{temperatureOpen ? <ChevronLeft size={18} className="rotate-90" /> : <ChevronRight size={18} />}</span>
              )}
            </button>

            {temperatureOpen && (
              <div id="temperature-subnav" className={`space-y-1 ${isCollapsed ? 'absolute left-20 top-0 bg-gray-800 rounded-lg shadow-lg p-2 min-w-[200px] z-50' : 'pl-10'}`}>
                <button
                  className={`w-full flex items-center space-x-2 p-2 rounded-lg transition-colors text-white ${activeView === 'Temperature Monitoring' || activeView === 'Temperature: Dashboard' ? 'bg-gray-700' : 'hover:bg-gray-700'}`}
                  onClick={() => setActiveView('Temperature Monitoring')}
                  aria-current={activeView === 'Temperature Monitoring' ? 'page' : undefined}
                >
                  <span className="min-w-[20px]"><LayoutDashboard size={20} /></span>
                  <span className="text-sm">Dashboard</span>
                </button>
                <button
                  className={`w-full flex items-center space-x-2 p-2 rounded-lg transition-colors text-white ${activeView === 'Sensor Management' || activeView === 'Temperature: Sensors' ? 'bg-gray-700' : 'hover:bg-gray-700'}`}
                  onClick={() => setActiveView('Sensor Management')}
                  aria-current={activeView === 'Sensor Management' ? 'page' : undefined}
                >
                  <span className="min-w-[20px]"><Settings size={20} /></span>
                  <span className="text-sm">Sensors</span>
                </button>
              </div>
            )}
          </div>
        </div>

        {userEmail && (
          <div className={`flex items-center gap-2 mb-4 ${isCollapsed ? 'justify-center' : ''}`}>
            <div className="bg-gray-700 p-2 rounded-full">
              <User size={20} />
            </div>
            {!isCollapsed && (
              <div className="text-sm text-gray-400 truncate">
                {userEmail}
              </div>
            )}
          </div>
        )}

        <button
          className="w-full flex items-center space-x-2 p-3 rounded-lg transition-colors hover:bg-gray-700"
          onClick={cycleTheme}
          title={`Current theme: ${theme}. Click to cycle.`}
        >
          <span className="min-w-[24px]">{getThemeIcon()}</span>
          {!isCollapsed && <span>Theme: {theme}</span>}
        </button>

        <button
          className="w-full flex items-center space-x-2 p-3 rounded-lg transition-colors hover:bg-gray-700"
          onClick={onSignOut}
        >
          <span className="min-w-[24px]"><LogOut size={24} /></span>
          {!isCollapsed && <span>Sign Out</span>}
        </button>
      </div>
    </nav>
  );
}

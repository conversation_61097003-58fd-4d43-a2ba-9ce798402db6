import { useState, useEffect, useCallback, useMemo } from 'react';
import { supabase } from '../../lib/supabase';
import { Card } from '../ui/card';
import { Button } from '../ui/button';
import { Alert } from '../ui/alert';
import { Thermometer, AlertTriangle, CheckCircle, TrendingUp, Activity } from 'lucide-react';

// Enhanced CCP interfaces for production-grade monitoring
interface AdvancedCCP {
  id: string;
  ccp_number: string;
  ccp_name: string;
  process_step: string;
  hazard_controlled: string;
  critical_limits: {
    temperature_min?: number;
    temperature_max?: number;
    ph_min?: number;
    ph_max?: number;
    time_min?: number;
    time_max?: number;
    salt_concentration_min?: number;
    water_activity_max?: number;
    [key: string]: number | string | undefined;
  };
  monitoring_frequency: 'continuous' | 'hourly' | 'per_batch' | 'daily' | 'weekly';
  monitoring_method: string;
  monitoring_equipment: string;
  responsible_person: string;
  verification_frequency: string;
  is_active: boolean;
  product_id?: string;
  species_specific_limits?: Record<string, any>;
  regulatory_basis: string[];
}

interface RealTimeMonitoringLog {
  id: string;
  ccp_id: string;
  lot_id?: string;
  batch_number?: string;
  monitoring_timestamp: string;
  measurements: Record<string, number | string>;
  within_critical_limits: boolean;
  deviation_details?: string;
  monitored_by: string;
  monitoring_equipment_id: string;
  notes?: string;
  environmental_conditions?: {
    ambient_temp?: number;
    humidity?: number;
    time_of_day?: string;
  };
  corrective_action_required: boolean;
  corrective_action_taken?: string;
  verification_status: 'pending' | 'verified' | 'failed';
  created_at: string;
}


interface EquipmentCalibration {
  id: string;
  equipment_id: string;
  equipment_name: string;
  last_calibration: string;
  next_calibration_due: string;
  calibration_status: 'current' | 'due' | 'overdue';
  accuracy_tolerance: number;
}

interface AdvancedCCPMonitoringProps {
  productId?: string;
  lotId?: string;
  batchNumber?: string;
  mode?: 'standard' | 'audit' | 'validation';
}

export default function AdvancedCCPMonitoring({ 
  productId, 
  lotId, 
  batchNumber
}: AdvancedCCPMonitoringProps) {
  // State management
  const [ccps, setCcps] = useState<AdvancedCCP[]>([]);
  const [selectedCcp, setSelectedCcp] = useState<AdvancedCCP | null>(null);
  const [monitoringLogs, setMonitoringLogs] = useState<RealTimeMonitoringLog[]>([]);
  const [equipment, setEquipment] = useState<EquipmentCalibration[]>([]);
  
  // Form state
  const [isRecording, setIsRecording] = useState(false);
  const [measurements, setMeasurements] = useState<Record<string, string>>({});
  const [monitoredBy, setMonitoredBy] = useState('');
  const [equipmentId, setEquipmentId] = useState('');
  const [notes, setNotes] = useState('');
  const [environmentalData, setEnvironmentalData] = useState({
    ambient_temp: '',
    humidity: '',
  });
  
  // UI state
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [realTimeMode, setRealTimeMode] = useState(false);
  const [showTrends, setShowTrends] = useState(false);
  const [alertsOnly, setAlertsOnly] = useState(false);

  // Load CCPs with enhanced data
  const loadAdvancedCCPs = useCallback(async () => {
    try {
      setLoading(true);
      let query = supabase
        .from('critical_control_points')
        .select(`
          *,
          hazard_analysis!inner(
            hazard_type,
            severity,
            likelihood,
            preventive_measures
          )
        `)
        .eq('is_active', true)
        .order('ccp_number');

      if (productId) {
        query = query.eq('product_id', productId);
      }

      const { data, error } = await query;
      if (error) throw error;

      const enhancedCcps: AdvancedCCP[] = (data || []).map(ccp => ({
        ...ccp,
        regulatory_basis: [
          '21 CFR 123 (Seafood HACCP)',
          'FDA Food Code',
          'FDA Guidance for Industry'
        ]
      }));

      setCcps(enhancedCcps);
      if (enhancedCcps.length > 0) {
        setSelectedCcp(enhancedCcps[0]);
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load CCPs');
    } finally {
      setLoading(false);
    }
  }, [productId]);

  // Load monitoring logs with enhanced details
  const loadMonitoringLogs = useCallback(async (ccpId: string) => {
    try {
      const { data, error } = await supabase
        .from('ccp_monitoring_logs')
        .select(`
          *,
          corrective_actions(
            action_description,
            action_timestamp,
            performed_by,
            verification_completed
          )
        `)
        .eq('ccp_id', ccpId)
        .order('monitoring_timestamp', { ascending: false })
        .limit(50);

      if (error) throw error;

      const enhancedLogs: RealTimeMonitoringLog[] = (data || []).map(log => ({
        ...log,
        corrective_action_required: !log.within_critical_limits,
        verification_status: log.within_critical_limits ? 'verified' : 
                           log.corrective_actions?.length > 0 ? 'pending' : 'failed'
      }));

      setMonitoringLogs(enhancedLogs);
    } catch (err) {
      console.error('Failed to load monitoring logs:', err);
    }
  }, []);


  // Load equipment calibration status
  const loadEquipmentStatus = useCallback(async () => {
    try {
      const { data, error } = await supabase
        .from('equipment_calibrations')
        .select('*')
        .order('next_calibration_due');

      if (error) throw error;

      const equipmentWithStatus: EquipmentCalibration[] = (data || []).map(item => {
        const dueDate = new Date(item.next_calibration_due);
        const now = new Date();
        const daysDifference = Math.ceil((dueDate.getTime() - now.getTime()) / (1000 * 3600 * 24));
        
        let status: 'current' | 'due' | 'overdue';
        if (daysDifference < 0) status = 'overdue';
        else if (daysDifference <= 7) status = 'due';
        else status = 'current';

        return {
          ...item,
          calibration_status: status
        };
      });

      setEquipment(equipmentWithStatus);
    } catch (err) {
      console.error('Failed to load equipment status:', err);
    }
  }, []);

  // Enhanced critical limits validation
  const validateCriticalLimits = useCallback((
    measurements: Record<string, number | string>, 
    limits: AdvancedCCP['critical_limits']
  ): { withinLimits: boolean; violations: string[] } => {
    const violations: string[] = [];

    for (const [key, value] of Object.entries(measurements)) {
      const numValue = parseFloat(String(value));
      if (isNaN(numValue)) continue;

      const minKey = `${key}_min` as keyof typeof limits;
      const maxKey = `${key}_max` as keyof typeof limits;

      if (limits[minKey] !== undefined && numValue < Number(limits[minKey])) {
        violations.push(`${key} below minimum (${numValue} < ${limits[minKey]})`);
      }
      if (limits[maxKey] !== undefined && numValue > Number(limits[maxKey])) {
        violations.push(`${key} above maximum (${numValue} > ${limits[maxKey]})`);
      }
    }

    return {
      withinLimits: violations.length === 0,
      violations
    };
  }, []);

  // Enhanced monitoring recording with automatic corrective action triggers
  const recordAdvancedMonitoring = async () => {
    if (!selectedCcp || !monitoredBy.trim() || !equipmentId.trim()) {
      setError('Please select CCP, enter monitor name, and select equipment');
      return;
    }

    try {
      setIsRecording(true);
      
      // Convert and validate measurements
      const numericMeasurements: Record<string, any> = {};
      for (const [key, value] of Object.entries(measurements)) {
        if (value.trim() !== '') {
          const numValue = parseFloat(value);
          numericMeasurements[key] = isNaN(numValue) ? value : numValue;
        }
      }

      const validation = validateCriticalLimits(numericMeasurements, selectedCcp.critical_limits);

      // Enhanced log data with environmental conditions
      const logData = {
        ccp_id: selectedCcp.id,
        lot_id: lotId || null,
        batch_number: batchNumber || null,
        measurements: numericMeasurements,
        within_critical_limits: validation.withinLimits,
        deviation_details: validation.violations.length > 0 ? validation.violations.join('; ') : null,
        monitored_by: monitoredBy.trim(),
        monitoring_equipment_id: equipmentId,
        notes: notes.trim() || null,
        environmental_conditions: {
          ambient_temp: environmentalData.ambient_temp ? parseFloat(environmentalData.ambient_temp) : null,
          humidity: environmentalData.humidity ? parseFloat(environmentalData.humidity) : null,
          time_of_day: new Date().toTimeString().slice(0, 5)
        },
        monitoring_timestamp: new Date().toISOString()
      };

      const { error } = await supabase
        .from('ccp_monitoring_logs')
        .insert([logData]);

      if (error) throw error;

      // If deviation detected, automatically trigger corrective action workflow
      if (!validation.withinLimits) {
        await triggerCorrectiveActionWorkflow(selectedCcp, validation.violations);
      }

      // Reset form
      setMeasurements({});
      setNotes('');
      setEnvironmentalData({ ambient_temp: '', humidity: '' });
      initializeMeasurements(selectedCcp.critical_limits);
      
      // Reload data
      await loadMonitoringLogs(selectedCcp.id);
      
      setError(null);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to record monitoring');
    } finally {
      setIsRecording(false);
    }
  };

  // Automatic corrective action workflow trigger
  const triggerCorrectiveActionWorkflow = async (ccp: AdvancedCCP, violations: string[]) => {
    try {
      // Create immediate corrective action record
      const correctiveActionData = {
        ccp_id: ccp.id,
        action_type: 'immediate',
        action_description: `Critical limit violation detected: ${violations.join(', ')}. Immediate investigation and corrective measures required.`,
        product_disposition: 'hold',
        performed_by: monitoredBy,
        action_timestamp: new Date().toISOString(),
        verification_required: true,
        verification_completed: false
      };

      await supabase
        .from('corrective_actions')
        .insert([correctiveActionData]);

      // Create high-priority compliance alert
      await supabase
        .rpc('create_compliance_alert', {
          p_alert_type: 'ccp_deviation',
          p_severity: 'critical',
          p_title: `CCP Deviation: ${ccp.ccp_name}`,
          p_description: `Critical control point ${ccp.ccp_number} has exceeded limits. Violations: ${violations.join(', ')}`,
          p_source_table: 'ccp_monitoring_logs',
          p_source_id: selectedCcp?.id,
          p_metadata: {
            violations,
            ccp_number: ccp.ccp_number,
            monitored_by: monitoredBy
          }
        });

    } catch (err) {
      console.error('Failed to trigger corrective action workflow:', err);
    }
  };

  const initializeMeasurements = (criticalLimits: AdvancedCCP['critical_limits']) => {
    const initialMeasurements: Record<string, string> = {};
    Object.keys(criticalLimits).forEach(key => {
      if (!key.includes('_min') && !key.includes('_max')) {
        initialMeasurements[key] = '';
      }
    });
    setMeasurements(initialMeasurements);
  };

  // Calculate compliance metrics
  const complianceMetrics = useMemo(() => {
    if (monitoringLogs.length === 0) return null;

    const totalLogs = monitoringLogs.length;
    const compliantLogs = monitoringLogs.filter(log => log.within_critical_limits).length;
    const recentDeviations = monitoringLogs
      .filter(log => !log.within_critical_limits)
      .filter(log => {
        const logDate = new Date(log.monitoring_timestamp);
        const sevenDaysAgo = new Date();
        sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);
        return logDate >= sevenDaysAgo;
      }).length;

    return {
      complianceRate: ((compliantLogs / totalLogs) * 100).toFixed(1),
      totalMonitoring: totalLogs,
      recentDeviations,
      lastMonitoring: monitoringLogs[0]?.monitoring_timestamp
    };
  }, [monitoringLogs]);

  // Effects
  useEffect(() => {
    loadAdvancedCCPs();
    loadEquipmentStatus();
  }, [loadAdvancedCCPs, loadEquipmentStatus]);

  useEffect(() => {
    if (selectedCcp) {
      loadMonitoringLogs(selectedCcp.id);
      initializeMeasurements(selectedCcp.critical_limits);
    }
  }, [selectedCcp, loadMonitoringLogs]);

  // Auto-refresh in real-time mode
  useEffect(() => {
    if (!realTimeMode || !selectedCcp) return;

    const interval = setInterval(() => {
      loadMonitoringLogs(selectedCcp.id);
    }, 30000); // Refresh every 30 seconds

    return () => clearInterval(interval);
  }, [realTimeMode, selectedCcp, loadMonitoringLogs]);

  // Utility functions
  const formatMeasurement = (key: string, value: any): string => {
    if (typeof value === 'number') {
      if (key.includes('temp')) return `${value}°C`;
      if (key.includes('ph')) return `pH ${value}`;
      if (key.includes('time')) return `${value} min`;
      if (key.includes('salt')) return `${value}%`;
      if (key.includes('water_activity')) return `aw ${value}`;
      return value.toString();
    }
    return value?.toString() || '';
  };

  const getCCPStatusColor = (): string => {
    const recentLogs = monitoringLogs.filter(log => {
      const logDate = new Date(log.monitoring_timestamp);
      const oneDayAgo = new Date();
      oneDayAgo.setDate(oneDayAgo.getDate() - 1);
      return logDate >= oneDayAgo;
    });

    if (recentLogs.length === 0) return 'border-gray-300 bg-gray-50';
    if (recentLogs.some(log => !log.within_critical_limits)) return 'border-red-300 bg-red-50';
    return 'border-green-300 bg-green-50';
  };

  if (loading) {
    return (
      <div className="p-6">
        <div className="flex items-center gap-3">
          <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
          <span>Loading Advanced CCP Monitoring System...</span>
        </div>
      </div>
    );
  }

  if (ccps.length === 0) {
    return (
      <Card className="p-6">
        <h3 className="text-lg font-semibold mb-4">Advanced CCP Monitoring</h3>
        <p className="text-gray-600">No Critical Control Points configured for this product.</p>
        <Button className="mt-4" onClick={() => {}}>
          Configure CCPs
        </Button>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {error && (
        <Alert className="border-red-200 bg-red-50">
          <AlertTriangle className="h-4 w-4" />
          <div className="text-red-800">{error}</div>
        </Alert>
      )}

      {/* Header with controls */}
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-xl font-semibold">Advanced CCP Monitoring</h2>
          <p className="text-sm text-gray-600">
            Real-time HACCP compliance monitoring with automated alerts
          </p>
        </div>
        <div className="flex gap-3">
          <Button
            variant="outline"
            size="sm"
            onClick={() => setRealTimeMode(!realTimeMode)}
          >
            <Activity className="h-4 w-4 mr-2" />
            {realTimeMode ? 'Disable' : 'Enable'} Real-Time
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => setShowTrends(!showTrends)}
          >
            <TrendingUp className="h-4 w-4 mr-2" />
            {showTrends ? 'Hide' : 'Show'} Trends
          </Button>
        </div>
      </div>

      {/* Compliance Overview */}
      {complianceMetrics && (
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Card className="p-4 text-center">
            <div className="text-2xl font-bold text-green-600">
              {complianceMetrics.complianceRate}%
            </div>
            <div className="text-sm text-gray-600">Compliance Rate</div>
          </Card>
          <Card className="p-4 text-center">
            <div className="text-2xl font-bold text-blue-600">
              {complianceMetrics.totalMonitoring}
            </div>
            <div className="text-sm text-gray-600">Total Logs</div>
          </Card>
          <Card className="p-4 text-center">
            <div className="text-2xl font-bold text-red-600">
              {complianceMetrics.recentDeviations}
            </div>
            <div className="text-sm text-gray-600">Recent Deviations</div>
          </Card>
          <Card className="p-4 text-center">
            <div className="text-sm font-medium text-gray-700">Last Monitoring</div>
            <div className="text-xs text-gray-500">
              {complianceMetrics.lastMonitoring ? 
                new Date(complianceMetrics.lastMonitoring).toLocaleString() : 
                'No data'
              }
            </div>
          </Card>
        </div>
      )}

      {/* Equipment Status Bar */}
      {equipment.length > 0 && (
        <Card className="p-4">
          <h4 className="font-medium mb-3">Equipment Calibration Status</h4>
          <div className="flex flex-wrap gap-2">
            {equipment.map((item) => (
              <div
                key={item.id}
                className={`px-3 py-1 rounded-full text-xs font-medium ${
                  item.calibration_status === 'overdue'
                    ? 'bg-red-100 text-red-800'
                    : item.calibration_status === 'due'
                    ? 'bg-yellow-100 text-yellow-800'
                    : 'bg-green-100 text-green-800'
                }`}
              >
                {item.equipment_name} ({item.calibration_status})
              </div>
            ))}
          </div>
        </Card>
      )}

      {/* CCP Selection Grid */}
      <Card className="p-4">
        <h3 className="text-lg font-semibold mb-4">Critical Control Points</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {ccps.map((ccp) => (
            <button
              key={ccp.id}
              onClick={() => setSelectedCcp(ccp)}
              className={`p-4 rounded-lg border-2 text-left transition-all ${
                selectedCcp?.id === ccp.id
                  ? 'border-blue-500 bg-blue-50'
                  : getCCPStatusColor()
              }`}
            >
              <div className="flex items-center justify-between mb-2">
                <div className="font-semibold text-sm">{ccp.ccp_number}</div>
                <div className="flex items-center gap-1">
                  {ccp.monitoring_frequency === 'continuous' && (
                    <Activity className="h-4 w-4 text-blue-500" />
                  )}
                  <Thermometer className="h-4 w-4 text-gray-400" />
                </div>
              </div>
              <div className="text-sm text-gray-700 mb-2">{ccp.ccp_name}</div>
              <div className="text-xs text-gray-500 mb-1">{ccp.process_step}</div>
              <div className="text-xs text-gray-500">
                Monitor: {ccp.monitoring_frequency}
              </div>
              <div className="text-xs text-gray-500">
                Equipment: {ccp.monitoring_equipment}
              </div>
            </button>
          ))}
        </div>
      </Card>

      {selectedCcp && (
        <>
          {/* Selected CCP Details */}
          <Card className="p-4">
            <div className="flex justify-between items-start mb-4">
              <div>
                <h4 className="text-lg font-semibold">{selectedCcp.ccp_number}: {selectedCcp.ccp_name}</h4>
                <div className="text-sm text-gray-600 mt-1">
                  Process Step: {selectedCcp.process_step} | Hazard: {selectedCcp.hazard_controlled}
                </div>
              </div>
              <div className="text-xs text-gray-500">
                Frequency: {selectedCcp.monitoring_frequency}
              </div>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <h5 className="font-medium text-sm mb-2">Critical Limits</h5>
                <div className="bg-yellow-50 border border-yellow-200 rounded p-3">
                  {Object.entries(selectedCcp.critical_limits).map(([key, value]) => (
                    <div key={key} className="text-xs mb-1">
                      <span className="font-medium">{key.replace(/_/g, ' ')}: </span>
                      {formatMeasurement(key, value)}
                    </div>
                  ))}
                </div>
              </div>
              <div>
                <h5 className="font-medium text-sm mb-2">Regulatory Basis</h5>
                <div className="bg-blue-50 border border-blue-200 rounded p-3">
                  {selectedCcp.regulatory_basis.map((ref, idx) => (
                    <div key={idx} className="text-xs mb-1">{ref}</div>
                  ))}
                </div>
              </div>
            </div>
          </Card>

          {/* Enhanced Recording Form */}
          <Card className="p-4">
            <h4 className="font-semibold mb-4">Record Monitoring</h4>
            <div className="space-y-4">
              {/* Measurements Grid */}
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {Object.keys(selectedCcp.critical_limits)
                  .filter(key => !key.includes('_min') && !key.includes('_max'))
                  .map((measurementKey) => (
                    <div key={measurementKey}>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        {measurementKey.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}
                      </label>
                      <input
                        type="number"
                        step="0.1"
                        value={measurements[measurementKey] || ''}
                        onChange={(e) => setMeasurements(prev => ({
                          ...prev,
                          [measurementKey]: e.target.value
                        }))}
                        className="w-full rounded-lg border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                        placeholder="Enter measurement"
                      />
                      <div className="text-xs text-gray-500 mt-1">
                        Range: {formatMeasurement(`${measurementKey}_min`, selectedCcp.critical_limits[`${measurementKey}_min`])} - {formatMeasurement(`${measurementKey}_max`, selectedCcp.critical_limits[`${measurementKey}_max`])}
                      </div>
                    </div>
                  ))
                }
              </div>

              {/* Environmental Conditions */}
              <div className="border-t pt-4">
                <h5 className="font-medium text-sm mb-3">Environmental Conditions</h5>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Ambient Temperature (°C)
                    </label>
                    <input
                      type="number"
                      step="0.1"
                      value={environmentalData.ambient_temp}
                      onChange={(e) => setEnvironmentalData(prev => ({
                        ...prev,
                        ambient_temp: e.target.value
                      }))}
                      className="w-full rounded-lg border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                      placeholder="Optional"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Relative Humidity (%)
                    </label>
                    <input
                      type="number"
                      step="0.1"
                      value={environmentalData.humidity}
                      onChange={(e) => setEnvironmentalData(prev => ({
                        ...prev,
                        humidity: e.target.value
                      }))}
                      className="w-full rounded-lg border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                      placeholder="Optional"
                    />
                  </div>
                </div>
              </div>

              {/* Monitor Details */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Monitored By *
                  </label>
                  <input
                    type="text"
                    value={monitoredBy}
                    onChange={(e) => setMonitoredBy(e.target.value)}
                    className="w-full rounded-lg border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                    placeholder="Enter your full name"
                    required
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Equipment Used *
                  </label>
                  <select
                    value={equipmentId}
                    onChange={(e) => setEquipmentId(e.target.value)}
                    className="w-full rounded-lg border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                    required
                  >
                    <option value="">Select Equipment</option>
                    {equipment
                      .filter(eq => eq.calibration_status !== 'overdue')
                      .map(eq => (
                        <option key={eq.equipment_id} value={eq.equipment_id}>
                          {eq.equipment_name} ({eq.calibration_status})
                        </option>
                      ))
                    }
                  </select>
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Notes & Observations
                </label>
                <textarea
                  value={notes}
                  onChange={(e) => setNotes(e.target.value)}
                  rows={3}
                  className="w-full rounded-lg border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                  placeholder="Record any observations, deviations, or corrective actions taken..."
                />
              </div>

              <Button
                onClick={recordAdvancedMonitoring}
                disabled={isRecording || !monitoredBy.trim() || !equipmentId}
                className="w-full"
                size="lg"
              >
                {isRecording ? (
                  <div className="flex items-center gap-2">
                    <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                    Recording Monitoring Data...
                  </div>
                ) : (
                  <div className="flex items-center gap-2">
                    <CheckCircle className="h-4 w-4" />
                    Record CCP Monitoring
                  </div>
                )}
              </Button>
            </div>
          </Card>

          {/* Real-time Monitoring Logs */}
          <Card className="p-4">
            <div className="flex justify-between items-center mb-4">
              <h4 className="font-semibold">Monitoring History</h4>
              <div className="flex gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setAlertsOnly(!alertsOnly)}
                >
                  {alertsOnly ? 'Show All' : 'Alerts Only'}
                </Button>
                {realTimeMode && (
                  <div className="flex items-center gap-2 text-sm text-green-600">
                    <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                    Real-time
                  </div>
                )}
              </div>
            </div>
            
            <div className="space-y-3 max-h-96 overflow-y-auto">
              {monitoringLogs
                .filter(log => !alertsOnly || !log.within_critical_limits)
                .map((log) => (
                  <div
                    key={log.id}
                    className={`p-4 rounded-lg border-l-4 ${
                      log.within_critical_limits
                        ? 'border-green-500 bg-green-50'
                        : 'border-red-500 bg-red-50'
                    }`}
                  >
                    <div className="flex justify-between items-start mb-2">
                      <div className="flex-1">
                        <div className="flex items-center gap-2">
                          <span className="text-sm font-medium">
                            {new Date(log.monitoring_timestamp).toLocaleString()}
                          </span>
                          <div className={`text-xs px-2 py-1 rounded ${
                            log.within_critical_limits
                              ? 'bg-green-100 text-green-800'
                              : 'bg-red-100 text-red-800'
                          }`}>
                            {log.within_critical_limits ? 'COMPLIANT' : 'DEVIATION'}
                          </div>
                        </div>
                        <div className="text-xs text-gray-600">
                          Monitored by: {log.monitored_by} | Equipment: {log.monitoring_equipment_id}
                        </div>
                      </div>
                    </div>

                    <div className="grid grid-cols-2 md:grid-cols-4 gap-2 mb-2">
                      {Object.entries(log.measurements).map(([key, value]) => (
                        <div key={key} className="text-xs">
                          <span className="font-medium">{key}: </span>
                          <span className={
                            !log.within_critical_limits && log.deviation_details?.includes(key)
                              ? 'text-red-700 font-semibold'
                              : ''
                          }>
                            {formatMeasurement(key, value)}
                          </span>
                        </div>
                      ))}
                    </div>

                    {log.environmental_conditions && (
                      <div className="text-xs text-gray-600 mb-2">
                        Environment: 
                        {log.environmental_conditions.ambient_temp && 
                          ` ${log.environmental_conditions.ambient_temp}°C`}
                        {log.environmental_conditions.humidity && 
                          ` ${log.environmental_conditions.humidity}% RH`}
                      </div>
                    )}

                    {log.deviation_details && (
                      <div className="text-xs text-red-700 bg-red-100 p-2 rounded mt-2">
                        <span className="font-semibold">Deviations: </span>{log.deviation_details}
                      </div>
                    )}

                    {log.notes && (
                      <div className="text-xs text-gray-700 mt-2">
                        <span className="font-medium">Notes: </span>{log.notes}
                      </div>
                    )}

                    {log.corrective_action_taken && (
                      <div className="text-xs text-blue-700 bg-blue-100 p-2 rounded mt-2">
                        <span className="font-semibold">Corrective Action: </span>
                        {log.corrective_action_taken}
                      </div>
                    )}
                  </div>
                ))
              }
              {monitoringLogs.length === 0 && (
                <p className="text-gray-600 text-center py-6">
                  No monitoring logs recorded yet for this CCP.
                </p>
              )}
            </div>
          </Card>
        </>
      )}
    </div>
  );
}
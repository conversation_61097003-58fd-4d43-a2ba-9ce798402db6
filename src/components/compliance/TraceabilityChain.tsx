import { useState, useEffect } from 'react';
import { supabase } from '../../lib/supabase';
import { Card } from '../ui/card';
import { Button } from '../ui/button';
import { Alert } from '../ui/alert';

interface TraceabilityEvent {
  id: string;
  event_type: string;
  event_time: string;
  actor_partner_name?: string;
  actor_location_name?: string;
  transporter_partner_name?: string;
  vessel_name?: string;
  fishing_area_name?: string;
  gear_type?: string;
  catch_method?: string;
  production_method?: string;
  reference_doc?: string;
  temperature_data?: Record<string, any>;
  notes?: string;
  event_quantity?: number;
  event_unit?: string;
  lot_role: string;
}

interface Lot {
  id: string;
  tlc: string;
  product_name: string;
  origin_country?: string;
  harvest_or_prod_date?: string;
  landing_date?: string;
  initial_qty?: number;
  uom?: string;
  status: string;
}

interface GDSTValidation {
  event_id: string;
  event_type: string;
  missing_kdes: string[];
  completeness_score: number;
  gdst_compliant: boolean;
}

interface TraceabilityChainProps {
  lotId?: string;
  tlc?: string;
}

export default function TraceabilityChain({ lotId, tlc }: TraceabilityChainProps) {
  const [lot, setLot] = useState<Lot | null>(null);
  const [traceabilityEvents, setTraceabilityEvents] = useState<TraceabilityEvent[]>([]);
  const [gdstValidation, setGdstValidation] = useState<GDSTValidation[]>([]);
  const [searchTlc, setSearchTlc] = useState(tlc || '');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [exportFormat, setExportFormat] = useState<'json' | 'csv' | 'pdf'>('json');

  useEffect(() => {
    if (lotId || tlc) {
      loadTraceabilityChain();
    }
  }, [lotId, tlc]);

  const loadTraceabilityChain = async (searchTlcValue?: string) => {
    try {
      setLoading(true);
      setError(null);

      // First, find the lot
      let lotQuery = supabase
        .from('lots')
        .select(`
          id, tlc, status, origin_country, harvest_or_prod_date, 
          landing_date, initial_qty, uom,
          "Products"(name)
        `);

      if (lotId) {
        lotQuery = lotQuery.eq('id', lotId);
      } else if (searchTlcValue || tlc) {
        lotQuery = lotQuery.eq('tlc', searchTlcValue || tlc);
      } else {
        throw new Error('Either lot ID or TLC is required');
      }
      
      lotQuery = lotQuery.single();

      const { data: lotData, error: lotError } = await lotQuery;
      if (lotError) throw lotError;
      if (!lotData) throw new Error('Lot not found');

      const lotInfo: Lot = {
        id: lotData.id,
        tlc: lotData.tlc,
        product_name: (lotData.Products as any)?.name || 'Unknown Product',
        origin_country: lotData.origin_country,
        harvest_or_prod_date: lotData.harvest_or_prod_date,
        landing_date: lotData.landing_date,
        initial_qty: lotData.initial_qty,
        uom: lotData.uom,
        status: lotData.status
      };
      setLot(lotInfo);

      // Get all traceability events for this lot
      const { data: eventsData, error: eventsError } = await supabase
        .from('gdst_traceability_export')
        .select('*')
        .eq('lot_id', lotData.id)
        .order('event_time');

      if (eventsError) throw eventsError;

      const events: TraceabilityEvent[] = (eventsData || []).map(event => ({
        id: event.event_id,
        event_type: event.event_type,
        event_time: event.event_time,
        actor_partner_name: event.actor_name,
        actor_location_name: event.location_name,
        transporter_partner_name: event.transporter_name,
        vessel_name: event.vessel_name,
        fishing_area_name: event.fishing_area_name,
        gear_type: event.gear_type,
        catch_method: event.catch_method,
        production_method: event.production_method,
        reference_doc: event.reference_doc,
        temperature_data: event.temperature_data,
        notes: event.notes,
        event_quantity: event.event_quantity,
        event_unit: event.event_unit,
        lot_role: event.lot_role
      }));

      setTraceabilityEvents(events);

      // Validate GDST compliance for each event
      const validations: GDSTValidation[] = [];
      for (const event of events) {
        try {
          const { data: validationData } = await supabase
            .rpc('validate_gdst_kdes', { event_id: event.id });
          
          if (validationData) {
            validations.push(validationData);
          }
        } catch (validationError) {
          console.warn('GDST validation failed for event:', event.id, validationError);
        }
      }
      setGdstValidation(validations);

    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load traceability chain');
    } finally {
      setLoading(false);
    }
  };

  const handleSearch = () => {
    if (searchTlc.trim()) {
      loadTraceabilityChain(searchTlc.trim());
    }
  };

  const exportTraceabilityData = async () => {
    if (!lot || traceabilityEvents.length === 0) return;

    const exportData = {
      lot,
      traceability_events: traceabilityEvents,
      gdst_validation: gdstValidation,
      export_timestamp: new Date().toISOString(),
      export_format: exportFormat
    };

    try {
      switch (exportFormat) {
        case 'json':
          downloadJson(exportData, `traceability_${lot.tlc}.json`);
          break;
        case 'csv':
          downloadCsv(traceabilityEvents, `traceability_${lot.tlc}.csv`);
          break;
        case 'pdf':
          // TODO: Implement PDF export
          alert('PDF export not yet implemented');
          break;
      }
    } catch (err) {
      setError('Failed to export data');
    }
  };

  const downloadJson = (data: any, filename: string) => {
    const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = filename;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  const downloadCsv = (events: TraceabilityEvent[], filename: string) => {
    const headers = [
      'Event Type', 'Event Time', 'Actor', 'Location', 'Vessel', 'Gear Type',
      'Quantity', 'Unit', 'Reference Doc', 'Notes'
    ];

    const rows = events.map(event => [
      event.event_type,
      event.event_time,
      event.actor_partner_name || '',
      event.actor_location_name || '',
      event.vessel_name || '',
      event.gear_type || '',
      event.event_quantity || '',
      event.event_unit || '',
      event.reference_doc || '',
      event.notes || ''
    ]);

    const csvContent = [headers, ...rows]
      .map(row => row.map(field => `"${field}"`).join(','))
      .join('\n');

    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = filename;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  const getEventIcon = (eventType: string) => {
    switch (eventType) {
      case 'harvest': return '🎣';
      case 'landing': return '🚢';
      case 'receiving': return '📦';
      case 'shipping': return '🚛';
      case 'transformation': return '🔄';
      default: return '📋';
    }
  };

  const getComplianceColor = (validation?: GDSTValidation) => {
    if (!validation) return 'bg-gray-100 text-gray-600';
    if (validation.gdst_compliant) return 'bg-green-100 text-green-800';
    if (validation.completeness_score >= 60) return 'bg-yellow-100 text-yellow-800';
    return 'bg-red-100 text-red-800';
  };

  return (
    <div className="space-y-6">
      {/* Search Section */}
      <Card className="p-4">
        <h2 className="text-lg font-semibold mb-4">Traceability Chain Lookup</h2>
        <div className="flex gap-4">
          <div className="flex-1">
            <input
              type="text"
              value={searchTlc}
              onChange={(e) => setSearchTlc(e.target.value)}
              placeholder="Enter Traceability Lot Code (TLC)"
              className="w-full rounded-lg border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
              onKeyDown={(e) => e.key === 'Enter' && handleSearch()}
            />
          </div>
          <Button 
            onClick={handleSearch}
            disabled={!searchTlc.trim() || loading}
          >
            {loading ? 'Searching...' : 'Search'}
          </Button>
        </div>
      </Card>

      {error && (
        <Alert className="border-red-200 bg-red-50">
          <div className="text-red-800">{error}</div>
        </Alert>
      )}

      {lot && (
        <>
          {/* Lot Information */}
          <Card className="p-4">
            <div className="flex justify-between items-start">
              <div>
                <h3 className="text-lg font-semibold">Lot Information</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mt-4 text-sm">
                  <div>
                    <span className="font-medium">TLC:</span>
                    <div className="text-blue-600 font-mono">{lot.tlc}</div>
                  </div>
                  <div>
                    <span className="font-medium">Product:</span>
                    <div>{lot.product_name}</div>
                  </div>
                  <div>
                    <span className="font-medium">Origin:</span>
                    <div>{lot.origin_country || 'Not specified'}</div>
                  </div>
                  <div>
                    <span className="font-medium">Status:</span>
                    <div className={`inline-flex px-2 py-1 rounded text-xs ${
                      lot.status === 'active' ? 'bg-green-100 text-green-800' :
                      lot.status === 'consumed' ? 'bg-blue-100 text-blue-800' :
                      'bg-gray-100 text-gray-800'
                    }`}>
                      {lot.status}
                    </div>
                  </div>
                  {lot.harvest_or_prod_date && (
                    <div>
                      <span className="font-medium">Harvest/Production:</span>
                      <div>{new Date(lot.harvest_or_prod_date).toLocaleDateString()}</div>
                    </div>
                  )}
                  {lot.landing_date && (
                    <div>
                      <span className="font-medium">Landing:</span>
                      <div>{new Date(lot.landing_date).toLocaleDateString()}</div>
                    </div>
                  )}
                  {lot.initial_qty && (
                    <div>
                      <span className="font-medium">Initial Quantity:</span>
                      <div>{lot.initial_qty} {lot.uom}</div>
                    </div>
                  )}
                </div>
              </div>
              <div className="flex gap-2">
                <select
                  value={exportFormat}
                  onChange={(e) => setExportFormat(e.target.value as 'json' | 'csv' | 'pdf')}
                  className="rounded border-gray-300 text-sm"
                >
                  <option value="json">JSON</option>
                  <option value="csv">CSV</option>
                  <option value="pdf">PDF</option>
                </select>
                <Button 
                  onClick={exportTraceabilityData}
                  size="sm"
                  disabled={traceabilityEvents.length === 0}
                >
                  Export
                </Button>
              </div>
            </div>
          </Card>

          {/* Traceability Events */}
          <Card className="p-4">
            <h3 className="text-lg font-semibold mb-4">Traceability Chain</h3>
            {traceabilityEvents.length === 0 ? (
              <p className="text-gray-600">No traceability events found for this lot.</p>
            ) : (
              <div className="space-y-4">
                {traceabilityEvents.map((event, index) => {
                  const validation = gdstValidation.find(v => v.event_id === event.id);
                  return (
                    <div key={event.id} className="relative">
                      {/* Timeline connector */}
                      {index < traceabilityEvents.length - 1 && (
                        <div className="absolute left-6 top-12 w-0.5 h-16 bg-gray-300" />
                      )}
                      
                      <div className="flex items-start gap-4">
                        {/* Event icon */}
                        <div className="flex-shrink-0 w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center">
                          <span className="text-lg">{getEventIcon(event.event_type)}</span>
                        </div>
                        
                        {/* Event details */}
                        <div className="flex-1 bg-gray-50 rounded-lg p-4">
                          <div className="flex justify-between items-start">
                            <div className="flex-1">
                              <div className="flex items-center gap-2">
                                <h4 className="font-medium capitalize">
                                  {event.event_type.replace('_', ' ')}
                                </h4>
                                <span className={`text-xs px-2 py-1 rounded ${getComplianceColor(validation)}`}>
                                  {validation ? `${validation.completeness_score}% Complete` : 'Not Validated'}
                                </span>
                              </div>
                              <div className="text-sm text-gray-600 mt-1">
                                {new Date(event.event_time).toLocaleString()}
                              </div>
                              
                              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3 mt-3 text-sm">
                                {event.actor_partner_name && (
                                  <div>
                                    <span className="font-medium">Actor:</span> {event.actor_partner_name}
                                  </div>
                                )}
                                {event.actor_location_name && (
                                  <div>
                                    <span className="font-medium">Location:</span> {event.actor_location_name}
                                  </div>
                                )}
                                {event.vessel_name && (
                                  <div>
                                    <span className="font-medium">Vessel:</span> {event.vessel_name}
                                  </div>
                                )}
                                {event.gear_type && (
                                  <div>
                                    <span className="font-medium">Gear:</span> {event.gear_type}
                                  </div>
                                )}
                                {event.catch_method && (
                                  <div>
                                    <span className="font-medium">Method:</span> {event.catch_method}
                                  </div>
                                )}
                                {event.production_method && (
                                  <div>
                                    <span className="font-medium">Production:</span> {event.production_method}
                                  </div>
                                )}
                                {event.event_quantity && (
                                  <div>
                                    <span className="font-medium">Quantity:</span> {event.event_quantity} {event.event_unit}
                                  </div>
                                )}
                                {event.reference_doc && (
                                  <div>
                                    <span className="font-medium">Reference:</span> {event.reference_doc}
                                  </div>
                                )}
                                {event.transporter_partner_name && (
                                  <div>
                                    <span className="font-medium">Transporter:</span> {event.transporter_partner_name}
                                  </div>
                                )}
                              </div>
                              
                              {event.temperature_data && Object.keys(event.temperature_data).length > 0 && (
                                <div className="mt-3 p-2 bg-blue-50 rounded text-sm">
                                  <span className="font-medium">Temperature Data:</span>
                                  <div className="mt-1">
                                    {Object.entries(event.temperature_data).map(([key, value]) => (
                                      <span key={key} className="mr-3">
                                        {key}: {value}°C
                                      </span>
                                    ))}
                                  </div>
                                </div>
                              )}
                              
                              {event.notes && (
                                <div className="mt-3 text-sm">
                                  <span className="font-medium">Notes:</span> {event.notes}
                                </div>
                              )}
                              
                              {validation && validation.missing_kdes.length > 0 && (
                                <div className="mt-3 p-2 bg-yellow-50 rounded text-sm">
                                  <span className="font-medium text-yellow-800">Missing GDST KDEs:</span>
                                  <div className="mt-1 text-yellow-700">
                                    {validation.missing_kdes.join(', ')}
                                  </div>
                                </div>
                              )}
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  );
                })}
              </div>
            )}
          </Card>

          {/* GDST Compliance Summary */}
          {gdstValidation.length > 0 && (
            <Card className="p-4">
              <h3 className="text-lg font-semibold mb-4">GDST Compliance Summary</h3>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="text-center">
                  <div className="text-2xl font-bold text-blue-600">
                    {gdstValidation.length}
                  </div>
                  <div className="text-sm text-gray-600">Total Events</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-green-600">
                    {gdstValidation.filter(v => v.gdst_compliant).length}
                  </div>
                  <div className="text-sm text-gray-600">GDST Compliant</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-orange-600">
                    {Math.round(gdstValidation.reduce((sum, v) => sum + v.completeness_score, 0) / gdstValidation.length)}%
                  </div>
                  <div className="text-sm text-gray-600">Avg Completeness</div>
                </div>
              </div>
            </Card>
          )}
        </>
      )}
    </div>
  );
}
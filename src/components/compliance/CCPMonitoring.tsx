import { useState, useEffect, useCallback } from 'react';
import { supabase } from '../../lib/supabase';
import { Card } from '../ui/card';
import { Button } from '../ui/button';
import { Alert } from '../ui/alert';

interface CriticalControlPoint {
  id: string;
  ccp_number: string;
  ccp_name: string;
  process_step: string;
  hazard_controlled: string;
  critical_limits: Record<string, number | string>;
  monitoring_frequency: string;
  monitoring_method: string;
  responsible_person: string;
  is_active: boolean;
}

interface MonitoringLog {
  id: string;
  ccp_id: string;
  monitoring_timestamp: string;
  measurements: Record<string, number | string>;
  within_critical_limits: boolean;
  deviation_details?: string;
  monitored_by: string;
  notes?: string;
}

interface CCPMonitoringProps {
  productId?: string;
  lotId?: string;
}

export default function CCPMonitoring({ productId, lotId }: CCPMonitoringProps) {
  const [ccps, setCcps] = useState<CriticalControlPoint[]>([]);
  const [selectedCcp, setSelectedCcp] = useState<CriticalControlPoint | null>(null);
  const [recentLogs, setRecentLogs] = useState<MonitoringLog[]>([]);
  const [isRecording, setIsRecording] = useState(false);
  const [measurements, setMeasurements] = useState<Record<string, string>>({});
  const [monitoredBy, setMonitoredBy] = useState('');
  const [notes, setNotes] = useState('');
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const loadCCPs = useCallback(async () => {
    try {
      setLoading(true);
      let query = supabase
        .from('critical_control_points')
        .select('*')
        .eq('is_active', true)
        .order('ccp_number');

      if (productId) {
        query = query.eq('product_id', productId);
      }

      const { data, error } = await query;
      if (error) throw error;

      setCcps(data || []);
      if (data && data.length > 0) {
        setSelectedCcp(data[0]);
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load CCPs');
    } finally {
      setLoading(false);
    }
  }, [productId]);

  useEffect(() => {
    loadCCPs();
  }, [loadCCPs]);

  useEffect(() => {
    if (selectedCcp) {
      loadRecentLogs(selectedCcp.id);
      initializeMeasurements(selectedCcp.critical_limits);
    }
  }, [selectedCcp]);

  const loadRecentLogs = async (ccpId: string) => {
    try {
      const { data, error } = await supabase
        .from('ccp_monitoring_logs')
        .select('*')
        .eq('ccp_id', ccpId)
        .order('monitoring_timestamp', { ascending: false })
        .limit(10);

      if (error) throw error;
      setRecentLogs(data || []);
    } catch (err) {
      console.error('Failed to load monitoring logs:', err);
    }
  };

  const initializeMeasurements = (criticalLimits: Record<string, number | string>) => {
    const initialMeasurements: Record<string, string> = {};
    Object.keys(criticalLimits).forEach(key => {
      if (!key.includes('_min') && !key.includes('_max')) {
        initialMeasurements[key] = '';
      }
    });
    setMeasurements(initialMeasurements);
  };

  const checkCriticalLimits = (measurements: Record<string, number | string>, limits: Record<string, number | string>): boolean => {
    for (const key in measurements) {
      const value = parseFloat(String(measurements[key]));
      if (isNaN(value)) continue;

      const minKey = `${key}_min`;
      const maxKey = `${key}_max`;

      if (limits[minKey] !== undefined && value < parseFloat(String(limits[minKey]))) {
        return false;
      }
      if (limits[maxKey] !== undefined && value > parseFloat(String(limits[maxKey]))) {
        return false;
      }
    }
    return true;
  };

  const recordMonitoring = async () => {
    if (!selectedCcp || !monitoredBy.trim()) {
      setError('Please select a CCP and enter monitor name');
      return;
    }

    try {
      setIsRecording(true);
      
      // Convert string measurements to numbers where possible
      const numericMeasurements: Record<string, any> = {};
      for (const [key, value] of Object.entries(measurements)) {
        const numValue = parseFloat(value);
        numericMeasurements[key] = isNaN(numValue) ? value : numValue;
      }

      const withinLimits = checkCriticalLimits(numericMeasurements, selectedCcp.critical_limits);

      const logData = {
        ccp_id: selectedCcp.id,
        lot_id: lotId || null,
        measurements: numericMeasurements,
        within_critical_limits: withinLimits,
        monitored_by: monitoredBy.trim(),
        notes: notes.trim() || null,
        monitoring_timestamp: new Date().toISOString()
      };

      const { error } = await supabase
        .from('ccp_monitoring_logs')
        .insert([logData]);

      if (error) throw error;

      // Clear form
      setMeasurements({});
      setNotes('');
      initializeMeasurements(selectedCcp.critical_limits);
      
      // Reload logs
      await loadRecentLogs(selectedCcp.id);
      
      setError(null);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to record monitoring');
    } finally {
      setIsRecording(false);
    }
  };

  const formatMeasurement = (key: string, value: any): string => {
    if (typeof value === 'number') {
      if (key.includes('temp')) return `${value}°C`;
      if (key.includes('ph')) return `pH ${value}`;
      if (key.includes('time')) return `${value} min`;
      return value.toString();
    }
    return value?.toString() || '';
  };

  if (loading) {
    return (
      <div className="p-4">
        <div className="animate-pulse">Loading CCPs...</div>
      </div>
    );
  }

  if (ccps.length === 0) {
    return (
      <Card className="p-6">
        <h3 className="text-lg font-semibold mb-4">CCP Monitoring</h3>
        <p className="text-gray-600">No Critical Control Points configured for this product.</p>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {error && (
        <Alert className="border-red-200 bg-red-50">
          <div className="text-red-800">{error}</div>
        </Alert>
      )}

      {/* CCP Selection */}
      <Card className="p-4">
        <h3 className="text-lg font-semibold mb-4">Critical Control Point Monitoring</h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          {ccps.map((ccp) => (
            <button
              key={ccp.id}
              onClick={() => setSelectedCcp(ccp)}
              className={`p-3 rounded-lg border text-left transition-colors ${
                selectedCcp?.id === ccp.id
                  ? 'border-blue-500 bg-blue-50'
                  : 'border-gray-200 hover:border-gray-300'
              }`}
            >
              <div className="font-medium text-sm">{ccp.ccp_number}</div>
              <div className="text-sm text-gray-600">{ccp.ccp_name}</div>
              <div className="text-xs text-gray-500 mt-1">{ccp.process_step}</div>
            </button>
          ))}
        </div>
      </Card>

      {selectedCcp && (
        <>
          {/* Current CCP Details */}
          <Card className="p-4">
            <h4 className="font-semibold mb-3">{selectedCcp.ccp_number}: {selectedCcp.ccp_name}</h4>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
              <div>
                <span className="font-medium">Process Step:</span> {selectedCcp.process_step}
              </div>
              <div>
                <span className="font-medium">Hazard Controlled:</span> {selectedCcp.hazard_controlled}
              </div>
              <div>
                <span className="font-medium">Monitoring Method:</span> {selectedCcp.monitoring_method}
              </div>
              <div>
                <span className="font-medium">Frequency:</span> {selectedCcp.monitoring_frequency}
              </div>
              <div className="md:col-span-2">
                <span className="font-medium">Critical Limits:</span>
                <div className="mt-1 p-2 bg-yellow-50 rounded border">
                  {Object.entries(selectedCcp.critical_limits).map(([key, value]) => (
                    <div key={key} className="text-xs">
                      {key.replace(/_/g, ' ')}: {formatMeasurement(key, value)}
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </Card>

          {/* Recording Form */}
          <Card className="p-4">
            <h4 className="font-semibold mb-4">Record Monitoring</h4>
            <div className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {Object.keys(selectedCcp.critical_limits)
                  .filter(key => !key.includes('_min') && !key.includes('_max'))
                  .map((measurementKey) => (
                    <div key={measurementKey}>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        {measurementKey.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}
                      </label>
                      <input
                        type="number"
                        step="0.1"
                        value={measurements[measurementKey] || ''}
                        onChange={(e) => setMeasurements(prev => ({
                          ...prev,
                          [measurementKey]: e.target.value
                        }))}
                        className="w-full rounded-lg border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                        placeholder="Enter measurement"
                      />
                      <div className="text-xs text-gray-500 mt-1">
                        Limits: {formatMeasurement(`${measurementKey}_min`, selectedCcp.critical_limits[`${measurementKey}_min`])} - {formatMeasurement(`${measurementKey}_max`, selectedCcp.critical_limits[`${measurementKey}_max`])}
                      </div>
                    </div>
                  ))
                }
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Monitored By *
                </label>
                <input
                  type="text"
                  value={monitoredBy}
                  onChange={(e) => setMonitoredBy(e.target.value)}
                  className="w-full rounded-lg border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                  placeholder="Enter your name"
                  required
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Notes
                </label>
                <textarea
                  value={notes}
                  onChange={(e) => setNotes(e.target.value)}
                  rows={3}
                  className="w-full rounded-lg border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                  placeholder="Additional observations or notes..."
                />
              </div>

              <Button
                onClick={recordMonitoring}
                disabled={isRecording || !monitoredBy.trim()}
                className="w-full"
              >
                {isRecording ? 'Recording...' : 'Record Monitoring'}
              </Button>
            </div>
          </Card>

          {/* Recent Logs */}
          <Card className="p-4">
            <h4 className="font-semibold mb-4">Recent Monitoring Logs</h4>
            <div className="space-y-2">
              {recentLogs.length === 0 ? (
                <p className="text-gray-600 text-sm">No monitoring logs recorded yet.</p>
              ) : (
                recentLogs.map((log) => (
                  <div
                    key={log.id}
                    className={`p-3 rounded border ${
                      log.within_critical_limits
                        ? 'border-green-200 bg-green-50'
                        : 'border-red-200 bg-red-50'
                    }`}
                  >
                    <div className="flex justify-between items-start">
                      <div className="flex-1">
                        <div className="text-sm font-medium">
                          {new Date(log.monitoring_timestamp).toLocaleString()}
                        </div>
                        <div className="text-xs text-gray-600">
                          Monitored by: {log.monitored_by}
                        </div>
                        <div className="mt-1">
                          {Object.entries(log.measurements).map(([key, value]) => (
                            <span key={key} className="text-xs mr-3">
                              {key}: {formatMeasurement(key, value)}
                            </span>
                          ))}
                        </div>
                        {log.notes && (
                          <div className="text-xs text-gray-600 mt-1">
                            Notes: {log.notes}
                          </div>
                        )}
                        {log.deviation_details && (
                          <div className="text-xs text-red-600 mt-1">
                            Deviation: {log.deviation_details}
                          </div>
                        )}
                      </div>
                      <div className={`text-xs px-2 py-1 rounded ${
                        log.within_critical_limits
                          ? 'bg-green-100 text-green-800'
                          : 'bg-red-100 text-red-800'
                      }`}>
                        {log.within_critical_limits ? 'Within Limits' : 'DEVIATION'}
                      </div>
                    </div>
                  </div>
                ))
              )}
            </div>
          </Card>
        </>
      )}
    </div>
  );
}
import { useState, useEffect, useCallback } from 'react';
import { supabase } from '../../lib/supabase';
import { Card } from '../ui/card';
import { Button } from '../ui/button';
import { Alert } from '../ui/alert';
import { Badge } from '../ui/badge';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from '../ui/tabs';
import { 
  Thermometer,
  Droplets,
  Gauge,
  Activity,
  AlertTriangle,
  CheckCircle,
  Zap,
  Wifi,
  WifiOff,
  Settings,
  Bell,
  Monitor,
  Cpu,
  Eye,
  Database
} from 'lucide-react';
import { LineChart as RechartsLineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer } from 'recharts';

// Real-time monitoring interfaces for IoT integration
interface IoTSensor {
  id: string;
  sensor_id: string;
  name: string;
  location: SensorLocation;
  sensor_type: 'temperature' | 'humidity' | 'ph' | 'pressure' | 'weight' | 'flow_rate' | 'dissolved_oxygen';
  measurement_unit: string;
  status: 'online' | 'offline' | 'error' | 'maintenance';
  last_reading: number;
  last_reading_timestamp: string;
  battery_level?: number;
  signal_strength?: number;
  calibration_due_date?: string;
  critical_limits: {
    min: number;
    max: number;
    warning_min: number;
    warning_max: number;
  };
  alert_settings: AlertSettings;
  metadata: SensorMetadata;
}

interface SensorLocation {
  zone: string;
  area: string;
  coordinates?: { x: number; y: number; z?: number };
  facility_map_reference?: string;
  description?: string;
}

interface AlertSettings {
  email_notifications: boolean;
  sms_notifications: boolean;
  in_app_notifications: boolean;
  escalation_delay_minutes: number;
  recipients: string[];
  custom_alert_conditions?: string[];
}

interface SensorMetadata {
  manufacturer: string;
  model: string;
  serial_number: string;
  installation_date: string;
  warranty_expiry_date?: string;
  maintenance_schedule?: string;
  firmware_version?: string;
  communication_protocol: 'wifi' | 'bluetooth' | 'zigbee' | 'lora' | 'cellular';
  power_source: 'battery' | 'wired' | 'solar';
}

// RealTimeReading interface commented out - not used in current implementation
// Will be enabled when real-time data processing is implemented

interface MonitoringAlert {
  id: string;
  alert_type: 'sensor_offline' | 'limit_exceeded' | 'battery_low' | 'calibration_due' | 'data_quality_poor';
  sensor_id: string;
  severity: 'info' | 'warning' | 'critical';
  title: string;
  description: string;
  triggered_at: string;
  acknowledged: boolean;
  acknowledged_by?: string;
  acknowledged_at?: string;
  resolved: boolean;
  resolved_at?: string;
  auto_resolved: boolean;
  escalated: boolean;
}

interface SystemStatus {
  total_sensors: number;
  online_sensors: number;
  offline_sensors: number;
  sensors_in_error: number;
  active_alerts: number;
  data_points_collected_today: number;
  system_uptime_percentage: number;
  last_system_backup: string;
  database_health: 'healthy' | 'degraded' | 'critical';
  api_response_time_ms: number;
}

interface MonitoringZone {
  id: string;
  name: string;
  description: string;
  zone_type: 'cold_storage' | 'processing_floor' | 'receiving_dock' | 'packaging_area' | 'warehouse' | 'office';
  target_conditions: {
    temperature_range?: { min: number; max: number };
    humidity_range?: { min: number; max: number };
    ph_range?: { min: number; max: number };
  };
  sensors: IoTSensor[];
  compliance_requirements: string[];
  regulatory_classification: string;
}

interface SystemStatus {
  overall_status: 'healthy' | 'warning' | 'critical';
  total_sensors: number;
  active_alerts: number;
  offline_sensors: number;
  online_sensors: number;
  sensors_in_error: number;
  data_collection_rate: number;
  data_points_collected_today: number;
  system_uptime_percentage: number;
  last_system_check: string;
  last_system_backup: string;
  database_health: string;
  api_response_time_ms: number;
}

interface TrendDataPoint {
  time: string;
  temperature: number;
  humidity: number;
  ph: number;
}

export default function RealTimeMonitoringDashboard() {
  // State management
  const [sensors, setSensors] = useState<IoTSensor[]>([]);
  const [alerts, setAlerts] = useState<MonitoringAlert[]>([]);
  const [, setMonitoringZones] = useState<MonitoringZone[]>([]);
  const [systemStatus, setSystemStatus] = useState<SystemStatus>({
    overall_status: 'healthy',
    total_sensors: 0,
    active_alerts: 0,
    offline_sensors: 0,
    online_sensors: 0,
    sensors_in_error: 0,
    data_collection_rate: 0,
    data_points_collected_today: 0,
    system_uptime_percentage: 100,
    last_system_check: new Date().toISOString(),
    last_system_backup: new Date().toISOString(),
    database_health: 'healthy',
    api_response_time_ms: 100
  });
  const [trendData, setTrendData] = useState<TrendDataPoint[]>([]);
  const [, setSelectedZone] = useState<MonitoringZone | null>(null);
  const [, setSelectedSensor] = useState<IoTSensor | null>(null);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState('overview');
  const [autoRefresh, setAutoRefresh] = useState(true);
  const [refreshInterval] = useState(5000); // 5 seconds

  // Load sensors and monitoring data
  const loadMonitoringData = useCallback(async () => {
    try {
      setLoading(true);

      // Load IoT sensors with latest readings
      const { data: sensorsData, error: sensorsError } = await supabase
        .from('iot_sensors')
        .select(`
          *,
          latest_reading:sensor_readings!inner(*)
        `)
        .order('name');

      if (sensorsError) {
        console.warn('Sensors table not found, using mock data');
        // Initialize with mock data for demonstration
        initializeMockData();
        return;
      }

      setSensors(sensorsData || []);

      // Load active alerts
      const { data: alertsData } = await supabase
        .from('monitoring_alerts')
        .select('*')
        .eq('resolved', false)
        .order('triggered_at', { ascending: false });

      setAlerts(alertsData || []);

      // Load monitoring zones
      const { data: zonesData } = await supabase
        .from('monitoring_zones')
        .select(`
          *,
          sensors:iot_sensors(*)
        `);

      setMonitoringZones(zonesData || []);

      // Calculate system status
      calculateSystemStatus(sensorsData || [], alertsData || []);

    } catch (err) {
      console.warn('Database tables not found, initializing with mock data');
      initializeMockData();
    } finally {
      setLoading(false);
    }
  }, []);

  // Initialize with mock data for demonstration
  const initializeMockData = () => {
    // Mock IoT sensors
    const mockSensors: IoTSensor[] = [
      {
        id: 'sensor-1',
        sensor_id: 'TEMP-001',
        name: 'Cold Storage A - Temperature',
        location: {
          zone: 'Cold Storage',
          area: 'Section A',
          description: 'Primary cold storage temperature sensor'
        },
        sensor_type: 'temperature',
        measurement_unit: '°C',
        status: 'online',
        last_reading: -1.8,
        last_reading_timestamp: new Date().toISOString(),
        battery_level: 87,
        signal_strength: 92,
        critical_limits: {
          min: -2.0,
          max: 2.0,
          warning_min: -1.5,
          warning_max: 1.5
        },
        alert_settings: {
          email_notifications: true,
          sms_notifications: true,
          in_app_notifications: true,
          escalation_delay_minutes: 15,
          recipients: ['<EMAIL>']
        },
        metadata: {
          manufacturer: 'SensorTech Pro',
          model: 'ST-T100',
          serial_number: 'ST001234',
          installation_date: '2024-01-15',
          communication_protocol: 'wifi',
          power_source: 'battery',
          firmware_version: '2.1.3'
        }
      },
      {
        id: 'sensor-2',
        sensor_id: 'HUM-001',
        name: 'Processing Floor - Humidity',
        location: {
          zone: 'Processing',
          area: 'Main Floor',
          description: 'Processing area humidity monitoring'
        },
        sensor_type: 'humidity',
        measurement_unit: '%RH',
        status: 'online',
        last_reading: 65.2,
        last_reading_timestamp: new Date().toISOString(),
        battery_level: 78,
        signal_strength: 88,
        critical_limits: {
          min: 45.0,
          max: 75.0,
          warning_min: 50.0,
          warning_max: 70.0
        },
        alert_settings: {
          email_notifications: true,
          sms_notifications: false,
          in_app_notifications: true,
          escalation_delay_minutes: 30,
          recipients: ['<EMAIL>']
        },
        metadata: {
          manufacturer: 'HygroSense',
          model: 'HS-H200',
          serial_number: 'HS005678',
          installation_date: '2024-02-01',
          communication_protocol: 'zigbee',
          power_source: 'wired',
          firmware_version: '1.8.2'
        }
      },
      {
        id: 'sensor-3',
        sensor_id: 'PH-001',
        name: 'Brine Solution - pH Monitor',
        location: {
          zone: 'Processing',
          area: 'Brining Station',
          description: 'Brine solution pH monitoring for cured products'
        },
        sensor_type: 'ph',
        measurement_unit: 'pH',
        status: 'offline',
        last_reading: 4.2,
        last_reading_timestamp: new Date(Date.now() - 1800000).toISOString(), // 30 minutes ago
        battery_level: 23,
        signal_strength: 45,
        critical_limits: {
          min: 3.5,
          max: 6.0,
          warning_min: 4.0,
          warning_max: 5.5
        },
        alert_settings: {
          email_notifications: true,
          sms_notifications: true,
          in_app_notifications: true,
          escalation_delay_minutes: 10,
          recipients: ['<EMAIL>', '<EMAIL>']
        },
        metadata: {
          manufacturer: 'AquaProbe',
          model: 'AP-PH300',
          serial_number: 'AP009876',
          installation_date: '2024-01-20',
          communication_protocol: 'wifi',
          power_source: 'battery',
          firmware_version: '3.2.1'
        }
      }
    ];

    // Mock alerts
    const mockAlerts: MonitoringAlert[] = [
      {
        id: 'alert-1',
        alert_type: 'sensor_offline',
        sensor_id: 'PH-001',
        severity: 'critical',
        title: 'pH Sensor Offline',
        description: 'Brine Solution pH Monitor has been offline for 30 minutes. Last reading: 4.2 pH',
        triggered_at: new Date(Date.now() - 1800000).toISOString(),
        acknowledged: false,
        resolved: false,
        auto_resolved: false,
        escalated: true
      },
      {
        id: 'alert-2',
        alert_type: 'battery_low',
        sensor_id: 'PH-001',
        severity: 'warning',
        title: 'Low Battery Warning',
        description: 'Battery level at 23% for pH sensor PH-001. Replace battery within 48 hours.',
        triggered_at: new Date(Date.now() - 3600000).toISOString(),
        acknowledged: true,
        acknowledged_by: '<EMAIL>',
        acknowledged_at: new Date(Date.now() - 3000000).toISOString(),
        resolved: false,
        auto_resolved: false,
        escalated: false
      }
    ];

    // Mock monitoring zones
    const mockZones: MonitoringZone[] = [
      {
        id: 'zone-1',
        name: 'Cold Storage',
        description: 'Primary cold storage facility for fresh seafood',
        zone_type: 'cold_storage',
        target_conditions: {
          temperature_range: { min: -2.0, max: 2.0 },
          humidity_range: { min: 85.0, max: 95.0 }
        },
        sensors: [mockSensors[0]],
        compliance_requirements: ['FDA HACCP', 'USDC Cold Storage'],
        regulatory_classification: 'Critical Control Point'
      },
      {
        id: 'zone-2',
        name: 'Processing Floor',
        description: 'Main seafood processing and packaging area',
        zone_type: 'processing_floor',
        target_conditions: {
          temperature_range: { min: 8.0, max: 15.0 },
          humidity_range: { min: 45.0, max: 75.0 }
        },
        sensors: [mockSensors[1], mockSensors[2]],
        compliance_requirements: ['FDA HACCP', 'GMP Standards'],
        regulatory_classification: 'Monitored Area'
      }
    ];

    // Mock system status
    const mockSystemStatus: SystemStatus = {
      overall_status: 'healthy',
      total_sensors: 3,
      active_alerts: 2,
      offline_sensors: 1,
      online_sensors: 2,
      sensors_in_error: 0,
      data_collection_rate: 98.5,
      data_points_collected_today: 8640,
      system_uptime_percentage: 99.2,
      last_system_check: new Date().toISOString(),
      last_system_backup: new Date(Date.now() - 21600000).toISOString(),
      database_health: 'healthy',
      api_response_time_ms: 145
    };

    // Generate mock trend data
    const mockTrendData = generateMockTrendData();

    // Set state
    setSensors(mockSensors);
    setAlerts(mockAlerts);
    setMonitoringZones(mockZones);
    setSystemStatus(mockSystemStatus);
    setTrendData(mockTrendData);
    setSelectedZone(mockZones[0]);
  };

  // Generate mock trend data for charts
  const generateMockTrendData = () => {
    const now = new Date();
    const data = [];
    
    for (let i = 23; i >= 0; i--) {
      const time = new Date(now.getTime() - (i * 60 * 60 * 1000));
      data.push({
        time: time.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' }),
        temperature: -1.8 + (Math.random() * 0.6 - 0.3), // -2.1 to -1.5
        humidity: 65.2 + (Math.random() * 10 - 5), // 60.2 to 70.2
        ph: 4.2 + (Math.random() * 0.4 - 0.2) // 4.0 to 4.4
      });
    }
    
    return data;
  };

  // Calculate system status metrics
  const calculateSystemStatus = (sensors: IoTSensor[], alerts: MonitoringAlert[]) => {
    const onlineSensors = sensors.filter(s => s.status === 'online').length;
    const offlineSensors = sensors.filter(s => s.status === 'offline').length;
    const errorSensors = sensors.filter(s => s.status === 'error').length;
    
    const status: SystemStatus = {
      overall_status: offlineSensors > sensors.length / 2 ? 'critical' : (errorSensors > 0 ? 'warning' : 'healthy'),
      total_sensors: sensors.length,
      active_alerts: alerts.length,
      offline_sensors: offlineSensors,
      online_sensors: onlineSensors,
      sensors_in_error: errorSensors,
      data_collection_rate: sensors.length > 0 ? (onlineSensors / sensors.length) * 100 : 100,
      data_points_collected_today: sensors.length * 17280,
      system_uptime_percentage: sensors.length > 0 ? (onlineSensors / sensors.length) * 100 : 100,
      last_system_check: new Date().toISOString(),
      last_system_backup: new Date(Date.now() - 6 * 60 * 60 * 1000).toISOString(),
      database_health: 'healthy',
      api_response_time_ms: 120 + Math.random() * 50
    };

    setSystemStatus(status);
  };

  // Real-time data simulation
  useEffect(() => {
    if (!autoRefresh) return;

    const interval = setInterval(() => {
      // Simulate real-time sensor readings
      setSensors(prevSensors => 
        prevSensors.map(sensor => {
          if (sensor.status === 'offline') return sensor;
          
          let newReading = sensor.last_reading;
          
          // Add some realistic variation
          switch (sensor.sensor_type) {
            case 'temperature':
              newReading += (Math.random() - 0.5) * 0.2; // ±0.1°C variation
              break;
            case 'humidity':
              newReading += (Math.random() - 0.5) * 2; // ±1% RH variation
              break;
            case 'ph':
              newReading += (Math.random() - 0.5) * 0.1; // ±0.05 pH variation
              break;
          }

          return {
            ...sensor,
            last_reading: Number(newReading.toFixed(2)),
            last_reading_timestamp: new Date().toISOString()
          };
        })
      );

      // Update trend data
      setTrendData((prevData: TrendDataPoint[]) => {
        const newData = [...prevData.slice(1)]; // Remove first point
        const now = new Date();
        newData.push({
          time: now.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' }),
          temperature: -1.8 + (Math.random() * 0.6 - 0.3),
          humidity: 65.2 + (Math.random() * 10 - 5),
          ph: 4.2 + (Math.random() * 0.4 - 0.2)
        });
        return newData;
      });
    }, refreshInterval);

    return () => clearInterval(interval);
  }, [autoRefresh, refreshInterval]);

  // Load initial data
  useEffect(() => {
    loadMonitoringData();
  }, [loadMonitoringData]);

  // Get sensor status icon and color
  const getSensorStatusIcon = (status: string) => {
    switch (status) {
      case 'online':
        return <Wifi className="w-4 h-4 text-green-500" />;
      case 'offline':
        return <WifiOff className="w-4 h-4 text-red-500" />;
      case 'error':
        return <AlertTriangle className="w-4 h-4 text-orange-500" />;
      case 'maintenance':
        return <Settings className="w-4 h-4 text-yellow-500" />;
      default:
        return <Zap className="w-4 h-4 text-gray-500" />;
    }
  };

  // Get sensor type icon
  const getSensorTypeIcon = (type: string) => {
    switch (type) {
      case 'temperature':
        return <Thermometer className="w-4 h-4" />;
      case 'humidity':
        return <Droplets className="w-4 h-4" />;
      case 'ph':
        return <Activity className="w-4 h-4" />;
      case 'pressure':
        return <Gauge className="w-4 h-4" />;
      default:
        return <Activity className="w-4 h-4" />;
    }
  };

  // Get alert severity color

  // Render loading state
  if (loading) {
    return (
      <Card className="p-6">
        <div className="animate-pulse space-y-4">
          <div className="h-6 bg-gray-200 rounded w-1/3"></div>
          <div className="h-4 bg-gray-200 rounded w-3/4"></div>
          <div className="h-4 bg-gray-200 rounded w-1/2"></div>
        </div>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header with System Status */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">Real-time Monitoring Dashboard</h2>
          <p className="text-sm text-gray-600 mt-1">
            IoT sensor monitoring with automated compliance alerts and trend analysis
          </p>
        </div>
        <div className="flex items-center gap-3">
          <Button
            onClick={() => setAutoRefresh(!autoRefresh)}
            variant={autoRefresh ? 'default' : 'outline'}
            className="flex items-center gap-2"
          >
            <Activity className="w-4 h-4" />
            {autoRefresh ? 'Live' : 'Paused'}
          </Button>
          <Badge variant={systemStatus && systemStatus.system_uptime_percentage > 95 ? 'default' : 'destructive'} className="flex items-center gap-1">
            <div className={`w-2 h-2 rounded-full ${systemStatus && systemStatus.system_uptime_percentage > 95 ? 'bg-green-500 animate-pulse' : 'bg-red-500'}`}></div>
            {systemStatus?.system_uptime_percentage.toFixed(1)}% Uptime
          </Badge>
        </div>
      </div>

      {/* System Overview Cards */}
      {systemStatus && (
        <div className="grid grid-cols-1 md:grid-cols-4 lg:grid-cols-6 gap-4">
          <Card className="p-4">
            <div className="flex items-center gap-3">
              <Monitor className="w-8 h-8 text-blue-600" />
              <div>
                <div className="text-2xl font-bold text-gray-900">{systemStatus.total_sensors}</div>
                <div className="text-sm text-gray-600">Total Sensors</div>
              </div>
            </div>
          </Card>
          
          <Card className="p-4">
            <div className="flex items-center gap-3">
              <CheckCircle className="w-8 h-8 text-green-600" />
              <div>
                <div className="text-2xl font-bold text-gray-900">{systemStatus.online_sensors}</div>
                <div className="text-sm text-gray-600">Online</div>
              </div>
            </div>
          </Card>
          
          <Card className="p-4">
            <div className="flex items-center gap-3">
              <WifiOff className="w-8 h-8 text-red-600" />
              <div>
                <div className="text-2xl font-bold text-gray-900">{systemStatus.offline_sensors}</div>
                <div className="text-sm text-gray-600">Offline</div>
              </div>
            </div>
          </Card>
          
          <Card className="p-4">
            <div className="flex items-center gap-3">
              <Bell className="w-8 h-8 text-orange-600" />
              <div>
                <div className="text-2xl font-bold text-gray-900">{systemStatus.active_alerts}</div>
                <div className="text-sm text-gray-600">Active Alerts</div>
              </div>
            </div>
          </Card>
          
          <Card className="p-4">
            <div className="flex items-center gap-3">
              <Database className="w-8 h-8 text-purple-600" />
              <div>
                <div className="text-2xl font-bold text-gray-900">{(systemStatus.data_points_collected_today / 1000).toFixed(1)}k</div>
                <div className="text-sm text-gray-600">Data Points Today</div>
              </div>
            </div>
          </Card>
          
          <Card className="p-4">
            <div className="flex items-center gap-3">
              <Cpu className="w-8 h-8 text-indigo-600" />
              <div>
                <div className="text-2xl font-bold text-gray-900">{systemStatus.api_response_time_ms}ms</div>
                <div className="text-sm text-gray-600">API Response</div>
              </div>
            </div>
          </Card>
        </div>
      )}

      {/* Active Alerts Bar */}
      {alerts.length > 0 && (
        <Alert className="border-red-200 bg-red-50">
          <AlertTriangle className="h-4 w-4" />
          <div className="flex items-center justify-between w-full">
            <div className="text-red-800">
              <strong>{alerts.length} Active Alert{alerts.length !== 1 ? 's' : ''}</strong>
              <div className="text-sm mt-1">
                {alerts.slice(0, 2).map(alert => alert.title).join(', ')}
                {alerts.length > 2 && ` and ${alerts.length - 2} more...`}
              </div>
            </div>
            <Button variant="outline" size="sm" className="ml-4">
              View All Alerts
            </Button>
          </div>
        </Alert>
      )}

      {/* Main Content Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="overview">Live Overview</TabsTrigger>
          <TabsTrigger value="sensors">Sensors</TabsTrigger>
          <TabsTrigger value="zones">Monitoring Zones</TabsTrigger>
          <TabsTrigger value="analytics">Analytics</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          {/* Live Sensor Readings Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {sensors.map((sensor) => (
              <Card key={sensor.id} className="p-6">
                <div className="flex items-center justify-between mb-4">
                  <div className="flex items-center gap-2">
                    {getSensorTypeIcon(sensor.sensor_type)}
                    <span className="font-medium text-gray-900">{sensor.name}</span>
                  </div>
                  {getSensorStatusIcon(sensor.status)}
                </div>
                
                <div className="text-3xl font-bold mb-2">
                  <span className={`${
                    sensor.last_reading >= sensor.critical_limits.min && 
                    sensor.last_reading <= sensor.critical_limits.max 
                      ? 'text-green-600' 
                      : 'text-red-600'
                  }`}>
                    {sensor.last_reading.toFixed(1)}
                  </span>
                  <span className="text-lg text-gray-500 ml-1">{sensor.measurement_unit}</span>
                </div>
                
                <div className="text-sm text-gray-600 mb-3">
                  {sensor.location.zone} • {sensor.location.area}
                </div>
                
                <div className="space-y-2 text-xs">
                  <div className="flex justify-between">
                    <span className="text-gray-500">Critical Range:</span>
                    <span>{sensor.critical_limits.min} - {sensor.critical_limits.max} {sensor.measurement_unit}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-500">Last Updated:</span>
                    <span>{new Date(sensor.last_reading_timestamp).toLocaleTimeString()}</span>
                  </div>
                  {sensor.battery_level && (
                    <div className="flex justify-between">
                      <span className="text-gray-500">Battery:</span>
                      <span className={`${sensor.battery_level < 30 ? 'text-red-600' : 'text-gray-700'}`}>
                        {sensor.battery_level}%
                      </span>
                    </div>
                  )}
                </div>
              </Card>
            ))}
          </div>

          {/* Real-time Trend Chart */}
          <Card className="p-6">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold">24-Hour Trend Analysis</h3>
              <div className="flex items-center gap-2">
                <Button variant="outline" size="sm">1H</Button>
                <Button variant="outline" size="sm">6H</Button>
                <Button size="sm">24H</Button>
                <Button variant="outline" size="sm">7D</Button>
              </div>
            </div>
            
            <div className="h-80">
              <ResponsiveContainer width="100%" height="100%">
                <RechartsLineChart data={trendData}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="time" />
                  <YAxis yAxisId="temp" orientation="left" />
                  <YAxis yAxisId="humidity" orientation="right" />
                  <Tooltip />
                  <Legend />
                  <Line
                    yAxisId="temp"
                    type="monotone"
                    dataKey="temperature"
                    stroke="#3B82F6"
                    strokeWidth={2}
                    dot={false}
                    name="Temperature (°C)"
                  />
                  <Line
                    yAxisId="humidity"
                    type="monotone"
                    dataKey="humidity"
                    stroke="#10B981"
                    strokeWidth={2}
                    dot={false}
                    name="Humidity (%RH)"
                  />
                  <Line
                    yAxisId="temp"
                    type="monotone"
                    dataKey="ph"
                    stroke="#F59E0B"
                    strokeWidth={2}
                    dot={false}
                    name="pH"
                  />
                </RechartsLineChart>
              </ResponsiveContainer>
            </div>
          </Card>
        </TabsContent>

        <TabsContent value="sensors" className="space-y-6">
          {/* Sensors Management */}
          <Card className="p-6">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold">IoT Sensors Management</h3>
              <Button className="flex items-center gap-2">
                <Settings className="w-4 h-4" />
                Add Sensor
              </Button>
            </div>
            
            <div className="overflow-x-auto">
              <table className="w-full text-sm">
                <thead>
                  <tr className="border-b">
                    <th className="text-left p-2">Sensor</th>
                    <th className="text-left p-2">Type</th>
                    <th className="text-left p-2">Location</th>
                    <th className="text-left p-2">Status</th>
                    <th className="text-left p-2">Current Reading</th>
                    <th className="text-left p-2">Battery</th>
                    <th className="text-left p-2">Last Updated</th>
                    <th className="text-left p-2">Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {sensors.map((sensor) => (
                    <tr key={sensor.id} className="border-b hover:bg-gray-50">
                      <td className="p-2">
                        <div className="flex items-center gap-2">
                          {getSensorTypeIcon(sensor.sensor_type)}
                          <div>
                            <div className="font-medium">{sensor.name}</div>
                            <div className="text-xs text-gray-500">{sensor.sensor_id}</div>
                          </div>
                        </div>
                      </td>
                      <td className="p-2">{sensor.sensor_type}</td>
                      <td className="p-2">
                        <div className="text-sm">
                          <div>{sensor.location.zone}</div>
                          <div className="text-gray-500">{sensor.location.area}</div>
                        </div>
                      </td>
                      <td className="p-2">
                        <div className="flex items-center gap-1">
                          {getSensorStatusIcon(sensor.status)}
                          <span className={`text-sm capitalize ${
                            sensor.status === 'online' ? 'text-green-600' :
                            sensor.status === 'offline' ? 'text-red-600' :
                            sensor.status === 'error' ? 'text-orange-600' :
                            'text-yellow-600'
                          }`}>
                            {sensor.status}
                          </span>
                        </div>
                      </td>
                      <td className="p-2">
                        <span className={`font-medium ${
                          sensor.last_reading >= sensor.critical_limits.min && 
                          sensor.last_reading <= sensor.critical_limits.max 
                            ? 'text-green-600' 
                            : 'text-red-600'
                        }`}>
                          {sensor.last_reading.toFixed(1)} {sensor.measurement_unit}
                        </span>
                      </td>
                      <td className="p-2">
                        {sensor.battery_level ? (
                          <span className={`text-sm ${
                            sensor.battery_level < 30 ? 'text-red-600' : 
                            sensor.battery_level < 50 ? 'text-yellow-600' : 'text-green-600'
                          }`}>
                            {sensor.battery_level}%
                          </span>
                        ) : (
                          <span className="text-gray-400">Wired</span>
                        )}
                      </td>
                      <td className="p-2 text-sm text-gray-600">
                        {new Date(sensor.last_reading_timestamp).toLocaleString()}
                      </td>
                      <td className="p-2">
                        <div className="flex items-center gap-1">
                          <Button variant="ghost" size="sm" onClick={() => setSelectedSensor(sensor)}>
                            <Eye className="w-4 h-4" />
                          </Button>
                          <Button variant="ghost" size="sm">
                            <Settings className="w-4 h-4" />
                          </Button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </Card>
        </TabsContent>

        <TabsContent value="zones">
          <Card className="p-6">
            <h3 className="text-lg font-semibold mb-4">Monitoring Zones (Coming Soon)</h3>
            <p className="text-gray-600">
              Zone-based monitoring with compliance requirements and automated reporting.
            </p>
          </Card>
        </TabsContent>

        <TabsContent value="analytics">
          <Card className="p-6">
            <h3 className="text-lg font-semibold mb-4">Advanced Analytics (Coming Soon)</h3>
            <p className="text-gray-600">
              Predictive analytics, anomaly detection, and compliance reporting dashboards.
            </p>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
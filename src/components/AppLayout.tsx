import React, { useEffect, useState } from 'react';
import { useNavigationContext } from '../contexts/NavigationContext';
import { useThemeAwareStyles } from '../contexts/ThemeContext';
import { supabase } from '../lib/supabase';
import Sidebar from './Sidebar';
import Dashboard from './Dashboard';
import Inventory from './Inventory';
import Analytics from './Analytics';
import Messages from './Messages';
import Settings from './Settings';
import VendorsView from './vendors/VendorsView';
import CustomersView from './customers/CustomersView';
import ImportInventory from './import/ImportInventory';
import HACCPEventsView from './HACCPEventsView';
import BatchTracking from './BatchTracking';
import HACCPCalendar from './HACCPCalendar';
import EventsView from './EventsView';
import FloatingVoiceButton from './voice/FloatingVoiceButton';
import VoiceTestPage from './voice/VoiceTestPage';
import VoiceEventManagement from '../pages/VoiceEventManagement';
import TemperatureMonitoring from '../pages/TemperatureMonitoring';
import SensorManagementPage from '../pages/SensorManagementPage';
import TempStickTest from './TempStickTest';
import { SimpleTempStickDashboard } from './sensors/SimpleTempStickDashboard';
import OverlayDiagnostic from './ui/OverlayDiagnostic';

interface AppLayoutProps {
  children?: React.ReactNode;
}

function AppLayout({ children }: AppLayoutProps) {
  const { activeView, setActiveView } = useNavigationContext();
  const [userEmail, setUserEmail] = useState<string | null>(null);
  const styles = useThemeAwareStyles();

  useEffect(() => {
    async function getSession() {
      const { data: { session } } = await supabase.auth.getSession();
      setUserEmail(session?.user?.email ?? null);
    }
    getSession();

    const { data: { subscription } } = supabase.auth.onAuthStateChange((_event, session) => {
      setUserEmail(session?.user?.email ?? null);
    });

    return () => subscription.unsubscribe();
  }, []);

  const handleSignOut = async () => {
    try {
      const { error } = await supabase.auth.signOut();
      if (error) throw error;
    } catch (error) {
      console.error('Error signing out:', error);
    }
  };

  const handleEventCreated = (eventType: string) => {
    console.log('Voice event created:', eventType);
    // Optional: Navigate to events view or refresh data
  };

  let content;
  switch (activeView) {
    case 'Dashboard':
      content = <Dashboard />;
      break;
    case 'Inventory':
      content = <Inventory />;
      break;
    case 'Vendors':
      content = <VendorsView />;
      break;
    case 'Customers':
      content = <CustomersView />;
      break;
    case 'Analytics':
      content = <Analytics />;
      break;
    case 'Messages':
      content = <Messages />;
      break;
    case 'Settings':
      content = <Settings />;
      break;
    case 'Import':
      content = <ImportInventory />;
      break;
    case 'Events':
      content = <EventsView />;
      break;
    case 'HACCP Events':
      content = <HACCPEventsView />;
      break;
    case 'HACCP: Events':
      content = <HACCPEventsView />;
      break;
    case 'HACCP: Batches':
      content = <BatchTracking />;
      break;
    case 'HACCP: Calendar':
      content = <HACCPCalendar />;
      break;
    case 'Voice Test':
      content = <VoiceTestPage onBack={() => setActiveView('Dashboard')} />;
      break;
    case 'Voice Management':
      content = <VoiceEventManagement />;
      break;
    case 'Temperature Monitoring':
      content = <TemperatureMonitoring />;
      break;
    case 'Sensor Management':
      content = <SensorManagementPage />;
      break;
    case 'Temperature: Dashboard':
      content = <TemperatureMonitoring />;
      break;
    case 'Temperature: Sensors':
      content = <SensorManagementPage />;
      break;
    case 'Temperature: Calibration':
      content = <SensorManagementPage />;
      break;
    case 'TempStick Test':
      content = <TempStickTest />;
      break;
    case 'TempStick Dashboard':
      content = <SimpleTempStickDashboard />;
      break;
    case 'Temperature: Live':
      content = <SimpleTempStickDashboard />;
      break;
    default:
      content = <Dashboard />;
  }

  return (
    <div 
      className={`relative flex h-screen ${styles.bg.secondary}`}
      style={{
        '--sidebar-width': '256px' // Set CSS custom property for sidebar width
      } as React.CSSProperties}
    >
      <Sidebar 
        activeView={activeView}
        setActiveView={setActiveView}
        onSignOut={handleSignOut}
        userEmail={userEmail ?? undefined}
        className="sidebar-protected"
      />
      <div className="relative flex-1 overflow-auto">
        <div className="relative z-0">
          {content}
          {children}
        </div>
      </div>

      {/* Floating Voice Assistant */}
      <FloatingVoiceButton onEventCreated={handleEventCreated} />
      
      {/* Development overlay diagnostic */}
      <OverlayDiagnostic />
    </div>
  );
}

export default AppLayout;
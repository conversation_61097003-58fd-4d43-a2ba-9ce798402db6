/**
 * Real-time Temperature Dashboard Component
 * 
 * Provides comprehensive temperature monitoring with:
 * - Real-time sensor status display
 * - Interactive temperature trend charts
 * - Sensor health indicators
 * - Responsive design for mobile and tablet
 * - Auto-refresh functionality
 * - Dashboard filters and controls
 */

import React, { useCallback, useMemo } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { Separator } from '@/components/ui/separator';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  Thermometer, 
  Wifi, 
  WifiOff, 
  Battery, 
  BatteryLow, 
  AlertTriangle, 
  RefreshCw,
  <PERSON>tings,
  Filter,
  TrendingUp,
  TrendingDown
} from 'lucide-react';
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, ReferenceLine } from 'recharts';
import { useTemperatureDashboard, type DashboardFilters } from '@/hooks/useTemperatureDashboard';
import { TempStickDataSourceSelector } from './TempStickDataSourceSelector';
import type { 
  SensorStatus, 
  DashboardSummary, 
  TemperatureTrendData, 
  TimeRange,
  SystemHealth 
} from '@/types/tempstick';

// Dashboard configuration types are imported from the hook

interface DashboardProps {
  className?: string;
  autoRefreshInterval?: number;
  showFilters?: boolean;
  compactMode?: boolean;
}

export const TemperatureDashboard: React.FC<DashboardProps> = ({
  className = '',
  autoRefreshInterval = 120000, // 2 minutes default (reduced from 30s to prevent rate limiting)
  showFilters = true,
  compactMode = false
}) => {
  // Use the custom hook for all dashboard data management
  const {
    sensorStatuses,
    dashboardSummary,
    temperatureTrends,
    systemHealth,
    loading,
    error,
    lastUpdate,
    autoRefresh,
    setAutoRefresh,
    filters,
    setFilters,
    refreshData,
    clearError,
    availableStorageAreas,
    availableSensors
  } = useTemperatureDashboard({
    autoRefreshInterval,
    enableRealTimeUpdates: true,
    maxTrendDataPoints: 1000
  });

  /**
   * Manual refresh handler
   */
  const handleManualRefresh = useCallback(async () => {
    await refreshData();
  }, [refreshData]);

  /**
   * Filter change handler
   */
  const handleFilterChange = useCallback((key: keyof DashboardFilters, value: any) => {
    setFilters({ [key]: value });
  }, [setFilters]);

  // Auto-refresh effect
  // Auto-refresh and initial data loading is handled by the useTemperatureDashboard hook

  // Filter options loading is handled by the useTemperatureDashboard hook

  // Memoized chart data
  const chartData = useMemo(() => {
    if (temperatureTrends.length === 0) return [];
    
    // Group data by timestamp and aggregate
    const groupedData = temperatureTrends.reduce((acc, reading) => {
      const timestamp = new Date(reading.timestamp).toISOString();
      if (!acc[timestamp]) {
        acc[timestamp] = {
          timestamp,
          readings: []
        };
      }
      acc[timestamp].readings.push(reading);
      return acc;
    }, {} as Record<string, { timestamp: string; readings: TemperatureTrendData[] }>);

    return Object.values(groupedData).map(group => ({
      timestamp: group.timestamp,
      averageTemp: group.readings.reduce((sum, r) => sum + r.temperature, 0) / group.readings.length,
      minTemp: Math.min(...group.readings.map(r => r.temperature)),
      maxTemp: Math.max(...group.readings.map(r => r.temperature)),
      alertCount: group.readings.filter(r => r.alertLevel === 'critical').length
    }));
  }, [temperatureTrends]);

  if (loading && !sensorStatuses.length) {
    return (
      <div className={`flex items-center justify-center p-8 ${className}`}>
        <div className="flex items-center space-x-2">
          <RefreshCw className="h-4 w-4 animate-spin" />
          <span>Loading temperature dashboard...</span>
        </div>
      </div>
    );
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Dashboard Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div>
          <h1 className="text-2xl font-bold">Temperature Monitoring</h1>
          <p className="text-muted-foreground">
            Real-time temperature monitoring and alerts
            {lastUpdate && (
              <span className="ml-2 text-sm">
                Last updated: {lastUpdate.toLocaleTimeString()}
              </span>
            )}
          </p>
        </div>
        
        <div className="flex items-center space-x-2">
          <div className="flex items-center space-x-2">
            <Switch
              id="auto-refresh"
              checked={autoRefresh}
              onCheckedChange={setAutoRefresh}
            />
            <Label htmlFor="auto-refresh" className="text-sm">
              Auto-refresh
            </Label>
          </div>
          
          <Button
            variant="outline"
            size="sm"
            onClick={handleManualRefresh}
            disabled={loading}
          >
            <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
        </div>
      </div>

      {/* Error Alert */}
      {error && (
        <Alert variant="destructive">
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {/* Dashboard Filters */}
      {showFilters && (
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="flex items-center text-sm font-medium">
              <Filter className="h-4 w-4 mr-2" />
              Filters
            </CardTitle>
          </CardHeader>
          <CardContent className="pt-0">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              <div className="space-y-2">
                <Label htmlFor="time-range">Time Range</Label>
                <Select
                  value={filters.timeRange}
                  onValueChange={(value: TimeRange) => handleFilterChange('timeRange', value)}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="1h">Last Hour</SelectItem>
                    <SelectItem value="6h">Last 6 Hours</SelectItem>
                    <SelectItem value="24h">Last 24 Hours</SelectItem>
                    <SelectItem value="7d">Last 7 Days</SelectItem>
                    <SelectItem value="30d">Last 30 Days</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="sensors">Sensors</Label>
                <Select
                  value={filters.selectedSensors[0] || 'all'}
                  onValueChange={(value) =>
                    handleFilterChange('selectedSensors', value === 'all' ? [] : [value])
                  }
                >
                  <SelectTrigger>
                    <SelectValue placeholder="All sensors" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Sensors</SelectItem>
                    {sensorStatuses.map((sensorStatus) => (
                      <SelectItem key={sensorStatus.sensor.id} value={sensorStatus.sensor.id}>
                        {sensorStatus.sensor.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="temperature-range">Temperature Range</Label>
                <Select
                  value="all"
                  onValueChange={(value) => {
                    // Future: Implement temperature range filtering
                    console.log('Temperature range filter:', value);
                  }}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="All temperatures" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Temperatures</SelectItem>
                    <SelectItem value="freezer">Freezer (-30°F to 0°F)</SelectItem>
                    <SelectItem value="refrigerator">Refrigerator (32°F to 40°F)</SelectItem>
                    <SelectItem value="room">Room Temperature (60°F to 80°F)</SelectItem>
                    <SelectItem value="critical">Critical Alerts</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="flex items-center space-x-2">
                <Switch
                  id="show-offline"
                  checked={filters.showOfflineSensors}
                  onCheckedChange={(checked) => handleFilterChange('showOfflineSensors', checked)}
                />
                <Label htmlFor="show-offline" className="text-sm">
                  Show offline sensors
                </Label>
              </div>

              <div className="flex items-center space-x-2">
                <Switch
                  id="alerts-only"
                  checked={filters.alertsOnly}
                  onCheckedChange={(checked) => handleFilterChange('alertsOnly', checked)}
                />
                <Label htmlFor="alerts-only" className="text-sm">
                  Alerts only
                </Label>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* TempStick Data Source Selector */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <div className="lg:col-span-1">
          <TempStickDataSourceSelector 
            onDataSourceChange={(mode) => {
              console.log(`🔄 Data source changed to: ${mode}`);
              // Refresh data after mode change
              refreshData();
            }}
          />
        </div>
        
        {/* Enhanced Controls and Status */}
        <div className="lg:col-span-2">
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-sm font-medium">System Status</CardTitle>
            </CardHeader>
            <CardContent className="pt-0">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                {/* API Status */}
                <div className="flex items-center space-x-2">
                  <div className={`w-2 h-2 rounded-full ${
                    error ? 'bg-red-500' : 'bg-green-500'
                  }`} />
                  <span className="text-sm">
                    API: {error ? 'Disconnected' : 'Connected'}
                  </span>
                </div>

                {/* Auto Refresh Status */}
                <div className="flex items-center space-x-2">
                  <div className={`w-2 h-2 rounded-full ${
                    autoRefresh ? 'bg-blue-500' : 'bg-gray-400'
                  }`} />
                  <span className="text-sm">
                    Auto-refresh: {autoRefresh ? 'On' : 'Off'}
                  </span>
                </div>

                {/* Data Source */}
                <div className="flex items-center space-x-2">
                  <div className="w-2 h-2 rounded-full bg-purple-500" />
                  <span className="text-sm">
                    Source: TempStick API
                  </span>
                </div>
              </div>

              {/* Quick Actions */}
              <div className="flex items-center space-x-2 mt-4">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setAutoRefresh(!autoRefresh)}
                >
                  {autoRefresh ? 'Disable' : 'Enable'} Auto-refresh
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleManualRefresh}
                  disabled={loading}
                >
                  <RefreshCw className={`h-4 w-4 mr-1 ${loading ? 'animate-spin' : ''}`} />
                  Refresh Now
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Dashboard Summary Cards */}
      {dashboardSummary && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Total Sensors</p>
                  <p className="text-2xl font-bold">{dashboardSummary.totalSensors}</p>
                </div>
                <Thermometer className="h-8 w-8 text-blue-500" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Online Sensors</p>
                  <p className="text-2xl font-bold text-green-600">{dashboardSummary.onlineSensors}</p>
                </div>
                <Wifi className="h-8 w-8 text-green-500" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Active Alerts</p>
                  <p className="text-2xl font-bold text-orange-600">{dashboardSummary.activeAlerts}</p>
                </div>
                <AlertTriangle className="h-8 w-8 text-orange-500" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Avg Temperature</p>
                  <p className="text-2xl font-bold">
                    {dashboardSummary.averageTemperature ? `${dashboardSummary.averageTemperature}°F` : 'N/A'}
                  </p>
                </div>
                <TrendingUp className="h-8 w-8 text-blue-500" />
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Temperature Trend Chart */}
      {chartData.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle>Temperature Trends</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="h-80">
              <ResponsiveContainer width="100%" height="100%">
                <LineChart data={chartData}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis 
                    dataKey="timestamp" 
                    tickFormatter={(value) => new Date(value).toLocaleTimeString()}
                  />
                  <YAxis 
                    label={{ value: 'Temperature (°F)', angle: -90, position: 'insideLeft' }}
                  />
                  <Tooltip 
                    labelFormatter={(value) => new Date(value).toLocaleString()}
                    formatter={(value: number) => [`${value.toFixed(1)}°F`, 'Temperature']}
                  />
                  <Line 
                    type="monotone" 
                    dataKey="averageTemp" 
                    stroke="#2563eb" 
                    strokeWidth={2}
                    dot={false}
                  />
                  <Line 
                    type="monotone" 
                    dataKey="minTemp" 
                    stroke="#10b981" 
                    strokeWidth={1}
                    strokeDasharray="5 5"
                    dot={false}
                  />
                  <Line 
                    type="monotone" 
                    dataKey="maxTemp" 
                    stroke="#ef4444" 
                    strokeWidth={1}
                    strokeDasharray="5 5"
                    dot={false}
                  />
                </LineChart>
              </ResponsiveContainer>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Sensor Status Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {sensorStatuses.map((sensorStatus) => (
          <SensorStatusCard
            key={sensorStatus.sensor.id}
            sensorStatus={sensorStatus}
            compactMode={compactMode}
          />
        ))}
      </div>

      {/* Empty State */}
      {sensorStatuses.length === 0 && !loading && (
        <Card>
          <CardContent className="p-8 text-center">
            <Thermometer className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
            <h3 className="text-lg font-medium mb-2">No sensors found</h3>
            <p className="text-muted-foreground mb-4">
              No temperature sensors match your current filters.
            </p>
            <Button variant="outline" onClick={() => setFilters({
              timeRange: '24h',
              selectedSensors: [],
              selectedStorageAreas: [],
              showOfflineSensors: true,
              alertsOnly: false
            })}>
              Clear Filters
            </Button>
          </CardContent>
        </Card>
      )}
    </div>
  );
};

/**
 * Individual sensor status card component
 */
interface SensorStatusCardProps {
  sensorStatus: SensorStatus;
  compactMode?: boolean;
}

const SensorStatusCard: React.FC<SensorStatusCardProps> = ({ 
  sensorStatus, 
  compactMode = false 
}) => {
  const { sensor, latestReading, status, activeAlerts, batteryLevel, signalStrength } = sensorStatus;

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'online': return 'bg-green-100 text-green-800 border-green-200';
      case 'warning': return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'critical': return 'bg-red-100 text-red-800 border-red-200';
      case 'offline': return 'bg-gray-100 text-gray-800 border-gray-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'online': return <Wifi className="h-4 w-4" />;
      case 'warning': return <AlertTriangle className="h-4 w-4" />;
      case 'critical': return <AlertTriangle className="h-4 w-4" />;
      case 'offline': return <WifiOff className="h-4 w-4" />;
      default: return <WifiOff className="h-4 w-4" />;
    }
  };

  return (
    <Card className="hover:shadow-md transition-shadow">
      <CardHeader className={compactMode ? "pb-2" : "pb-3"}>
        <div className="flex items-center justify-between">
          <CardTitle className="text-sm font-medium truncate">
            {sensor.name}
          </CardTitle>
          <Badge className={`${getStatusColor(status)} flex items-center space-x-1`}>
            {getStatusIcon(status)}
            <span className="capitalize">{status}</span>
          </Badge>
        </div>
        {!compactMode && (
          <p className="text-xs text-muted-foreground truncate">
            {sensor.location}
          </p>
        )}
      </CardHeader>
      
      <CardContent className={compactMode ? "pt-0 pb-4" : "pt-0"}>
        {latestReading ? (
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <span className="text-2xl font-bold">
                {latestReading.temperature.toFixed(1)}°F
              </span>
              {latestReading.humidity && (
                <span className="text-sm text-muted-foreground">
                  {latestReading.humidity.toFixed(0)}% RH
                </span>
              )}
            </div>
            
            {!compactMode && (
              <>
                <div className="text-xs text-muted-foreground">
                  Last reading: {new Date(latestReading.reading_timestamp).toLocaleString()}
                </div>
                
                {/* Battery and Signal Strength Indicators */}
                <div className="flex items-center justify-between text-xs text-muted-foreground">
                  {batteryLevel !== undefined && (
                    <div className="flex items-center space-x-1">
                      {batteryLevel < 25 ? (
                        <BatteryLow className="h-3 w-3 text-red-500" />
                      ) : (
                        <Battery className="h-3 w-3 text-green-500" />
                      )}
                      <span>{batteryLevel}%</span>
                    </div>
                  )}
                  {signalStrength !== undefined && (
                    <div className="flex items-center space-x-1">
                      <Wifi className="h-3 w-3" />
                      <span>{signalStrength}%</span>
                    </div>
                  )}
                </div>
                
                {activeAlerts.length > 0 && (
                  <div className="flex items-center space-x-1 text-xs text-orange-600">
                    <AlertTriangle className="h-3 w-3" />
                    <span>{activeAlerts.length} active alert{activeAlerts.length > 1 ? 's' : ''}</span>
                  </div>
                )}
              </>
            )}
          </div>
        ) : (
          <div className="text-center py-4">
            <p className="text-sm text-muted-foreground">No recent readings</p>
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default TemperatureDashboard;
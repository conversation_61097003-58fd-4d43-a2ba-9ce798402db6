/**
 * Real-Time Data Subscription Integration Tests
 * 
 * Comprehensive integration tests for real-time temperature data updates including:
 * - Supabase real-time subscription lifecycle
 * - Temperature reading updates and state synchronization
 * - Alert triggering and real-time notifications
 * - Sensor status changes and connectivity updates
 * - Error handling and reconnection scenarios
 * - Performance under high-frequency updates
 * - Memory leak prevention and cleanup
 */

import React, { useEffect, useState } from 'react';
import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { screen, render, waitFor, act } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { renderWithTheme, createMockSensorDataset, waitForNextTick } from '../../../../__tests__/utils/test-utils';
import { createMockSupabaseClient, mockScenarios } from '../../../../__tests__/mocks/supabase-mocks';
import { ThemeProvider } from '../../../../contexts/ThemeContext';

// Mock hooks that handle real-time subscriptions
const mockUseSensorStatuses = vi.fn();
const mockUseTemperatureAlerts = vi.fn();
const mockUseTemperatureSync = vi.fn();

vi.mock('../../../../hooks/useTempStick', () => ({
  useSensorStatuses: mockUseSensorStatuses,
  useTemperatureAlerts: mockUseTemperatureAlerts,
  useTemperatureSync: mockUseTemperatureSync,
}));

// Test component that demonstrates real-time updates
const RealTimeTestComponent = ({ onUpdate }: { onUpdate?: (data: any) => void }) => {
  const { sensorStatuses, summary } = mockUseSensorStatuses();
  const { alerts } = mockUseTemperatureAlerts();
  const [updateCount, setUpdateCount] = useState(0);

  useEffect(() => {
    if (onUpdate) {
      onUpdate({ sensorStatuses, alerts, summary });
    }
    setUpdateCount(prev => prev + 1);
  }, [sensorStatuses, alerts, summary, onUpdate]);

  return (
    <div data-testid="realtime-component">
      <div data-testid="sensor-count">{summary.totalSensors}</div>
      <div data-testid="alert-count">{alerts.length}</div>
      <div data-testid="update-count">{updateCount}</div>
      <div data-testid="sensors">
        {sensorStatuses.map((status: any) => (
          <div key={status.sensor.id} data-testid={`sensor-${status.sensor.id}`}>
            <span data-testid="sensor-name">{status.sensor.name}</span>
            <span data-testid="sensor-temp">
              {status.latestReading?.temperature?.toFixed(1) || 'N/A'}°F
            </span>
            <span data-testid="sensor-status">{status.status}</span>
          </div>
        ))}
      </div>
      <div data-testid="alerts">
        {alerts.map((alert: any) => (
          <div key={alert.id} data-testid={`alert-${alert.id}`}>
            <span data-testid="alert-type">{alert.alert_type}</span>
            <span data-testid="alert-severity">{alert.severity_level}</span>
          </div>
        ))}
      </div>
    </div>
  );
};

describe('Real-Time Data Subscription Integration', () => {
  let mockSupabaseClient: ReturnType<typeof createMockSupabaseClient>;
  let subscriptionCallbacks: Function[] = [];

  beforeEach(() => {
    mockSupabaseClient = createMockSupabaseClient();
    subscriptionCallbacks = [];
    
    // Reset mocks with default empty state
    mockUseSensorStatuses.mockReturnValue({
      sensorStatuses: [],
      summary: {
        totalSensors: 0,
        onlineSensors: 0,
        activeAlerts: 0,
        criticalAlerts: 0,
        averageTemperature: null,
        temperatureRange: null,
      },
      loading: false,
      error: null,
      refetch: vi.fn(),
    });

    mockUseTemperatureAlerts.mockReturnValue({
      alerts: [],
      unreadCount: 0,
      loading: false,
      error: null,
      dismissAlert: vi.fn(),
      resolveAlert: vi.fn(),
      refetch: vi.fn(),
    });

    mockUseTemperatureSync.mockReturnValue({
      sync: vi.fn(),
      syncing: false,
      lastSyncTime: new Date(),
      error: null,
    });

    // Mock subscription mechanism
    const originalChannel = mockSupabaseClient.channel;
    mockSupabaseClient.channel = vi.fn((channelName) => {
      const channel = originalChannel.call(mockSupabaseClient, channelName);
      
      // Override the on method to capture callbacks
      const originalOn = channel.on;
      channel.on = vi.fn((event, callback) => {
        subscriptionCallbacks.push(callback);
        return originalOn.call(channel, event, callback);
      });
      
      return channel;
    });
  });

  afterEach(() => {
    vi.clearAllMocks();
    subscriptionCallbacks = [];
    mockSupabaseClient.__clearMocks();
  });

  describe('Subscription Lifecycle', () => {
    it('initializes with empty data and sets up subscriptions', async () => {
      renderWithTheme(<RealTimeTestComponent />);

      expect(screen.getByTestId('sensor-count')).toHaveTextContent('0');
      expect(screen.getByTestId('alert-count')).toHaveTextContent('0');
      expect(screen.getByTestId('update-count')).toHaveTextContent('1');
    });

    it('receives initial data load', async () => {
      const initialData = createMockSensorDataset(2);
      
      mockUseSensorStatuses.mockReturnValue({
        sensorStatuses: initialData,
        summary: {
          totalSensors: 2,
          onlineSensors: 2,
          activeAlerts: 0,
          criticalAlerts: 0,
          averageTemperature: -18.5,
          temperatureRange: { min: -20.0, max: -17.0 },
        },
        loading: false,
        error: null,
        refetch: vi.fn(),
      });

      renderWithTheme(<RealTimeTestComponent />);

      expect(screen.getByTestId('sensor-count')).toHaveTextContent('2');
      expect(screen.getByTestId('sensors')).toBeInTheDocument();
      
      const sensors = screen.getAllByTestId(/^sensor-/);
      expect(sensors).toHaveLength(2);
    });

    it('handles subscription errors gracefully', async () => {
      mockUseSensorStatuses.mockReturnValue({
        sensorStatuses: [],
        summary: {
          totalSensors: 0,
          onlineSensors: 0,
          activeAlerts: 0,
          criticalAlerts: 0,
          averageTemperature: null,
          temperatureRange: null,
        },
        loading: false,
        error: 'Subscription failed',
        refetch: vi.fn(),
      });

      renderWithTheme(<RealTimeTestComponent />);

      // Component should still render even with subscription errors
      expect(screen.getByTestId('realtime-component')).toBeInTheDocument();
      expect(screen.getByTestId('sensor-count')).toHaveTextContent('0');
    });
  });

  describe('Real-Time Temperature Updates', () => {
    it('updates temperature readings in real-time', async () => {
      const initialData = createMockSensorDataset(1);
      const sensor = initialData[0];
      
      // Initial render with first temperature reading
      mockUseSensorStatuses.mockReturnValue({
        sensorStatuses: initialData,
        summary: {
          totalSensors: 1,
          onlineSensors: 1,
          activeAlerts: 0,
          criticalAlerts: 0,
          averageTemperature: -18.5,
          temperatureRange: null,
        },
        loading: false,
        error: null,
        refetch: vi.fn(),
      });

      const { rerender } = renderWithTheme(<RealTimeTestComponent />);

      expect(screen.getByTestId('sensor-temp')).toHaveTextContent(
        `${sensor.latest_reading!.temperature.toFixed(1)}°F`
      );

      // Simulate real-time update with new temperature
      const updatedData = [{
        ...sensor,
        latest_reading: {
          ...sensor.latest_reading!,
          temperature: -19.2,
          recorded_at: new Date().toISOString(),
        }
      }];

      mockUseSensorStatuses.mockReturnValue({
        sensorStatuses: updatedData,
        summary: {
          totalSensors: 1,
          onlineSensors: 1,
          activeAlerts: 0,
          criticalAlerts: 0,
          averageTemperature: -19.2,
          temperatureRange: null,
        },
        loading: false,
        error: null,
        refetch: vi.fn(),
      });

      rerender(<RealTimeTestComponent />);

      await waitFor(() => {
        expect(screen.getByTestId('sensor-temp')).toHaveTextContent('-19.2°F');
      });

      // Update count should increment
      expect(screen.getByTestId('update-count')).toHaveTextContent('2');
    });

    it('handles multiple sensor updates simultaneously', async () => {
      const initialData = createMockSensorDataset(3);
      
      mockUseSensorStatuses.mockReturnValue({
        sensorStatuses: initialData,
        summary: {
          totalSensors: 3,
          onlineSensors: 3,
          activeAlerts: 0,
          criticalAlerts: 0,
          averageTemperature: -18.0,
          temperatureRange: { min: -20.0, max: -16.0 },
        },
        loading: false,
        error: null,
        refetch: vi.fn(),
      });

      const { rerender } = renderWithTheme(<RealTimeTestComponent />);

      expect(screen.getAllByTestId(/^sensor-/)).toHaveLength(3);

      // Update all sensors with new readings
      const updatedData = initialData.map((sensor, index) => ({
        ...sensor,
        latest_reading: {
          ...sensor.latest_reading!,
          temperature: -18.0 + index,
          recorded_at: new Date().toISOString(),
        }
      }));

      mockUseSensorStatuses.mockReturnValue({
        sensorStatuses: updatedData,
        summary: {
          totalSensors: 3,
          onlineSensors: 3,
          activeAlerts: 0,
          criticalAlerts: 0,
          averageTemperature: -17.0,
          temperatureRange: { min: -18.0, max: -16.0 },
        },
        loading: false,
        error: null,
        refetch: vi.fn(),
      });

      rerender(<RealTimeTestComponent />);

      await waitFor(() => {
        const tempElements = screen.getAllByTestId('sensor-temp');
        expect(tempElements[0]).toHaveTextContent('-18.0°F');
        expect(tempElements[1]).toHaveTextContent('-17.0°F');
        expect(tempElements[2]).toHaveTextContent('-16.0°F');
      });
    });

    it('handles rapid temperature updates without performance issues', async () => {
      const initialData = createMockSensorDataset(1);
      const sensor = initialData[0];
      let updateCount = 0;
      
      const onUpdate = vi.fn(() => {
        updateCount++;
      });

      mockUseSensorStatuses.mockReturnValue({
        sensorStatuses: initialData,
        summary: {
          totalSensors: 1,
          onlineSensors: 1,
          activeAlerts: 0,
          criticalAlerts: 0,
          averageTemperature: -18.5,
          temperatureRange: null,
        },
        loading: false,
        error: null,
        refetch: vi.fn(),
      });

      const { rerender } = renderWithTheme(
        <RealTimeTestComponent onUpdate={onUpdate} />
      );

      const startTime = performance.now();

      // Simulate 50 rapid updates
      for (let i = 1; i <= 50; i++) {
        const updatedData = [{
          ...sensor,
          latest_reading: {
            ...sensor.latest_reading!,
            temperature: -18.0 + (i * 0.1),
            recorded_at: new Date().toISOString(),
          }
        }];

        mockUseSensorStatuses.mockReturnValue({
          sensorStatuses: updatedData,
          summary: {
            totalSensors: 1,
            onlineSensors: 1,
            activeAlerts: 0,
            criticalAlerts: 0,
            averageTemperature: -18.0 + (i * 0.1),
            temperatureRange: null,
          },
          loading: false,
          error: null,
          refetch: vi.fn(),
        });

        rerender(<RealTimeTestComponent onUpdate={onUpdate} />);
        await waitForNextTick();
      }

      const endTime = performance.now();
      const totalTime = endTime - startTime;

      // Should handle 50 updates in reasonable time (under 1 second)
      expect(totalTime).toBeLessThan(1000);
      expect(updateCount).toBe(50);

      // Final temperature should reflect last update
      await waitFor(() => {
        expect(screen.getByTestId('sensor-temp')).toHaveTextContent('-13.0°F');
      });
    });
  });

  describe('Real-Time Alert Updates', () => {
    it('shows new alerts immediately', async () => {
      const initialData = createMockSensorDataset(1);
      
      // Start with no alerts
      mockUseSensorStatuses.mockReturnValue({
        sensorStatuses: initialData,
        summary: {
          totalSensors: 1,
          onlineSensors: 1,
          activeAlerts: 0,
          criticalAlerts: 0,
          averageTemperature: -18.5,
          temperatureRange: null,
        },
        loading: false,
        error: null,
        refetch: vi.fn(),
      });

      mockUseTemperatureAlerts.mockReturnValue({
        alerts: [],
        unreadCount: 0,
        loading: false,
        error: null,
        dismissAlert: vi.fn(),
        resolveAlert: vi.fn(),
        refetch: vi.fn(),
      });

      const { rerender } = renderWithTheme(<RealTimeTestComponent />);

      expect(screen.getByTestId('alert-count')).toHaveTextContent('0');

      // Add new alert
      const newAlert = {
        id: 'alert-001',
        sensor_id: initialData[0].sensor.id,
        alert_type: 'high_temp',
        severity_level: 'critical',
        message: 'Temperature exceeded critical threshold',
        created_at: new Date().toISOString(),
        is_resolved: false,
      };

      mockUseTemperatureAlerts.mockReturnValue({
        alerts: [newAlert],
        unreadCount: 1,
        loading: false,
        error: null,
        dismissAlert: vi.fn(),
        resolveAlert: vi.fn(),
        refetch: vi.fn(),
      });

      rerender(<RealTimeTestComponent />);

      await waitFor(() => {
        expect(screen.getByTestId('alert-count')).toHaveTextContent('1');
        expect(screen.getByTestId('alert-alert-001')).toBeInTheDocument();
        expect(screen.getByTestId('alert-type')).toHaveTextContent('high_temp');
        expect(screen.getByTestId('alert-severity')).toHaveTextContent('critical');
      });
    });

    it('removes resolved alerts in real-time', async () => {
      const alert = {
        id: 'alert-001',
        sensor_id: 'sensor-001',
        alert_type: 'high_temp',
        severity_level: 'critical',
        message: 'Temperature exceeded critical threshold',
        created_at: new Date().toISOString(),
        is_resolved: false,
      };

      // Start with active alert
      mockUseTemperatureAlerts.mockReturnValue({
        alerts: [alert],
        unreadCount: 1,
        loading: false,
        error: null,
        dismissAlert: vi.fn(),
        resolveAlert: vi.fn(),
        refetch: vi.fn(),
      });

      const { rerender } = renderWithTheme(<RealTimeTestComponent />);

      expect(screen.getByTestId('alert-count')).toHaveTextContent('1');
      expect(screen.getByTestId('alert-alert-001')).toBeInTheDocument();

      // Resolve alert
      mockUseTemperatureAlerts.mockReturnValue({
        alerts: [],
        unreadCount: 0,
        loading: false,
        error: null,
        dismissAlert: vi.fn(),
        resolveAlert: vi.fn(),
        refetch: vi.fn(),
      });

      rerender(<RealTimeTestComponent />);

      await waitFor(() => {
        expect(screen.getByTestId('alert-count')).toHaveTextContent('0');
        expect(screen.queryByTestId('alert-alert-001')).not.toBeInTheDocument();
      });
    });

    it('handles multiple simultaneous alerts', async () => {
      const alerts = [
        {
          id: 'alert-001',
          sensor_id: 'sensor-001',
          alert_type: 'high_temp',
          severity_level: 'critical',
          message: 'High temperature',
          created_at: new Date().toISOString(),
          is_resolved: false,
        },
        {
          id: 'alert-002',
          sensor_id: 'sensor-002',
          alert_type: 'low_temp',
          severity_level: 'high',
          message: 'Low temperature',
          created_at: new Date().toISOString(),
          is_resolved: false,
        },
        {
          id: 'alert-003',
          sensor_id: 'sensor-001',
          alert_type: 'sensor_offline',
          severity_level: 'medium',
          message: 'Sensor offline',
          created_at: new Date().toISOString(),
          is_resolved: false,
        },
      ];

      mockUseTemperatureAlerts.mockReturnValue({
        alerts,
        unreadCount: 3,
        loading: false,
        error: null,
        dismissAlert: vi.fn(),
        resolveAlert: vi.fn(),
        refetch: vi.fn(),
      });

      renderWithTheme(<RealTimeTestComponent />);

      await waitFor(() => {
        expect(screen.getByTestId('alert-count')).toHaveTextContent('3');
        expect(screen.getByTestId('alert-alert-001')).toBeInTheDocument();
        expect(screen.getByTestId('alert-alert-002')).toBeInTheDocument();
        expect(screen.getByTestId('alert-alert-003')).toBeInTheDocument();
      });
    });
  });

  describe('Sensor Status Changes', () => {
    it('updates sensor online/offline status in real-time', async () => {
      const initialData = createMockSensorDataset(1);
      const sensor = { ...initialData[0], sensor: { ...initialData[0].sensor, isOnline: true } };
      
      mockUseSensorStatuses.mockReturnValue({
        sensorStatuses: [{ ...sensor, status: 'online' }],
        summary: {
          totalSensors: 1,
          onlineSensors: 1,
          activeAlerts: 0,
          criticalAlerts: 0,
          averageTemperature: -18.5,
          temperatureRange: null,
        },
        loading: false,
        error: null,
        refetch: vi.fn(),
      });

      const { rerender } = renderWithTheme(<RealTimeTestComponent />);

      expect(screen.getByTestId('sensor-status')).toHaveTextContent('online');

      // Sensor goes offline
      mockUseSensorStatuses.mockReturnValue({
        sensorStatuses: [{ ...sensor, status: 'offline', sensor: { ...sensor.sensor, isOnline: false } }],
        summary: {
          totalSensors: 1,
          onlineSensors: 0,
          activeAlerts: 1,
          criticalAlerts: 0,
          averageTemperature: null,
          temperatureRange: null,
        },
        loading: false,
        error: null,
        refetch: vi.fn(),
      });

      rerender(<RealTimeTestComponent />);

      await waitFor(() => {
        expect(screen.getByTestId('sensor-status')).toHaveTextContent('offline');
      });
    });

    it('handles sensor addition and removal', async () => {
      // Start with 2 sensors
      const initialData = createMockSensorDataset(2);
      
      mockUseSensorStatuses.mockReturnValue({
        sensorStatuses: initialData,
        summary: {
          totalSensors: 2,
          onlineSensors: 2,
          activeAlerts: 0,
          criticalAlerts: 0,
          averageTemperature: -18.5,
          temperatureRange: null,
        },
        loading: false,
        error: null,
        refetch: vi.fn(),
      });

      const { rerender } = renderWithTheme(<RealTimeTestComponent />);

      expect(screen.getByTestId('sensor-count')).toHaveTextContent('2');
      expect(screen.getAllByTestId(/^sensor-/)).toHaveLength(2);

      // Add a third sensor
      const updatedData = [...initialData, ...createMockSensorDataset(1)];
      
      mockUseSensorStatuses.mockReturnValue({
        sensorStatuses: updatedData,
        summary: {
          totalSensors: 3,
          onlineSensors: 3,
          activeAlerts: 0,
          criticalAlerts: 0,
          averageTemperature: -18.5,
          temperatureRange: null,
        },
        loading: false,
        error: null,
        refetch: vi.fn(),
      });

      rerender(<RealTimeTestComponent />);

      await waitFor(() => {
        expect(screen.getByTestId('sensor-count')).toHaveTextContent('3');
        expect(screen.getAllByTestId(/^sensor-/)).toHaveLength(3);
      });

      // Remove first sensor
      const reducedData = updatedData.slice(1);
      
      mockUseSensorStatuses.mockReturnValue({
        sensorStatuses: reducedData,
        summary: {
          totalSensors: 2,
          onlineSensors: 2,
          activeAlerts: 0,
          criticalAlerts: 0,
          averageTemperature: -18.5,
          temperatureRange: null,
        },
        loading: false,
        error: null,
        refetch: vi.fn(),
      });

      rerender(<RealTimeTestComponent />);

      await waitFor(() => {
        expect(screen.getByTestId('sensor-count')).toHaveTextContent('2');
        expect(screen.getAllByTestId(/^sensor-/)).toHaveLength(2);
      });
    });
  });

  describe('Error Handling and Reconnection', () => {
    it('handles subscription connection errors', async () => {
      // Start with successful connection
      const initialData = createMockSensorDataset(1);
      
      mockUseSensorStatuses.mockReturnValue({
        sensorStatuses: initialData,
        summary: {
          totalSensors: 1,
          onlineSensors: 1,
          activeAlerts: 0,
          criticalAlerts: 0,
          averageTemperature: -18.5,
          temperatureRange: null,
        },
        loading: false,
        error: null,
        refetch: vi.fn(),
      });

      const { rerender } = renderWithTheme(<RealTimeTestComponent />);

      expect(screen.getByTestId('sensor-count')).toHaveTextContent('1');

      // Simulate connection error
      mockUseSensorStatuses.mockReturnValue({
        sensorStatuses: [],
        summary: {
          totalSensors: 0,
          onlineSensors: 0,
          activeAlerts: 0,
          criticalAlerts: 0,
          averageTemperature: null,
          temperageRange: null,
        },
        loading: false,
        error: 'Connection lost',
        refetch: vi.fn(),
      });

      rerender(<RealTimeTestComponent />);

      // Component should handle error gracefully
      expect(screen.getByTestId('realtime-component')).toBeInTheDocument();
      expect(screen.getByTestId('sensor-count')).toHaveTextContent('0');

      // Simulate reconnection
      mockUseSensorStatuses.mockReturnValue({
        sensorStatuses: initialData,
        summary: {
          totalSensors: 1,
          onlineSensors: 1,
          activeAlerts: 0,
          criticalAlerts: 0,
          averageTemperature: -18.5,
          temperatureRange: null,
        },
        loading: false,
        error: null,
        refetch: vi.fn(),
      });

      rerender(<RealTimeTestComponent />);

      await waitFor(() => {
        expect(screen.getByTestId('sensor-count')).toHaveTextContent('1');
      });
    });

    it('handles partial data updates during network issues', async () => {
      const initialData = createMockSensorDataset(2);
      
      mockUseSensorStatuses.mockReturnValue({
        sensorStatuses: initialData,
        summary: {
          totalSensors: 2,
          onlineSensors: 2,
          activeAlerts: 0,
          criticalAlerts: 0,
          averageTemperature: -18.5,
          temperatureRange: null,
        },
        loading: false,
        error: null,
        refetch: vi.fn(),
      });

      const { rerender } = renderWithTheme(<RealTimeTestComponent />);

      expect(screen.getAllByTestId(/^sensor-/)).toHaveLength(2);

      // Simulate partial update (only one sensor updates)
      const partialData = [
        {
          ...initialData[0],
          latest_reading: {
            ...initialData[0].latest_reading!,
            temperature: -20.0,
          }
        },
        // Second sensor missing/stale data
        initialData[1]
      ];

      mockUseSensorStatuses.mockReturnValue({
        sensorStatuses: partialData,
        summary: {
          totalSensors: 2,
          onlineSensors: 1,
          activeAlerts: 0,
          criticalAlerts: 0,
          averageTemperature: -19.0,
          temperatureRange: null,
        },
        loading: false,
        error: null,
        refetch: vi.fn(),
      });

      rerender(<RealTimeTestComponent />);

      await waitFor(() => {
        const sensors = screen.getAllByTestId(/^sensor-/);
        expect(sensors).toHaveLength(2);
      });
    });
  });

  describe('Memory Management', () => {
    it('prevents memory leaks during rapid updates', async () => {
      const initialData = createMockSensorDataset(1);
      let renderCount = 0;
      
      const MemoryTestComponent = () => {
        const { sensorStatuses } = mockUseSensorStatuses();
        renderCount++;
        
        useEffect(() => {
          return () => {
            // Cleanup function should be called
          };
        }, [sensorStatuses]);
        
        return (
          <div data-testid="memory-test-component">
            Render: {renderCount}
          </div>
        );
      };

      mockUseSensorStatuses.mockReturnValue({
        sensorStatuses: initialData,
        summary: {
          totalSensors: 1,
          onlineSensors: 1,
          activeAlerts: 0,
          criticalAlerts: 0,
          averageTemperature: -18.5,
          temperatureRange: null,
        },
        loading: false,
        error: null,
        refetch: vi.fn(),
      });

      const { unmount, rerender } = renderWithTheme(<MemoryTestComponent />);

      // Simulate many rapid updates
      for (let i = 0; i < 100; i++) {
        const updatedData = [{
          ...initialData[0],
          latest_reading: {
            ...initialData[0].latest_reading!,
            temperature: -18.0 + (i * 0.01),
          }
        }];

        mockUseSensorStatuses.mockReturnValue({
          sensorStatuses: updatedData,
          summary: {
            totalSensors: 1,
            onlineSensors: 1,
            activeAlerts: 0,
            criticalAlerts: 0,
            averageTemperature: -18.0 + (i * 0.01),
            temperatureRange: null,
          },
          loading: false,
          error: null,
          refetch: vi.fn(),
        });

        rerender(<MemoryTestComponent />);
      }

      // Clean unmount should not cause issues
      expect(() => unmount()).not.toThrow();
      expect(renderCount).toBe(100);
    });

    it('cleans up subscriptions on unmount', async () => {
      const mockUnsubscribe = vi.fn();
      
      // Mock subscription cleanup
      const originalChannel = mockSupabaseClient.channel;
      mockSupabaseClient.channel = vi.fn((channelName) => {
        const channel = originalChannel.call(mockSupabaseClient, channelName);
        channel.unsubscribe = mockUnsubscribe;
        return channel;
      });

      const { unmount } = renderWithTheme(<RealTimeTestComponent />);

      // Unmount should trigger cleanup
      unmount();
      
      // Verify cleanup was called (this would be done internally by the hooks)
      expect(mockSupabaseClient.channel).toHaveBeenCalled();
    });
  });

  describe('Performance Under Load', () => {
    it('maintains performance with high-frequency updates', async () => {
      const largeDataset = createMockSensorDataset(20);
      let updateTime = 0;
      
      const PerformanceTestComponent = () => {
        const { sensorStatuses } = mockUseSensorStatuses();
        const startTime = performance.now();
        
        useEffect(() => {
          updateTime = performance.now() - startTime;
        }, [sensorStatuses]);
        
        return (
          <div data-testid="performance-test">
            {sensorStatuses.map((status) => (
              <div key={status.sensor.id}>
                {status.sensor.name}: {status.latest_reading?.temperature}°F
              </div>
            ))}
          </div>
        );
      };

      mockUseSensorStatuses.mockReturnValue({
        sensorStatuses: largeDataset,
        summary: {
          totalSensors: 20,
          onlineSensors: 20,
          activeAlerts: 0,
          criticalAlerts: 0,
          averageTemperature: -18.5,
          temperatureRange: null,
        },
        loading: false,
        error: null,
        refetch: vi.fn(),
      });

      const { rerender } = renderWithTheme(<PerformanceTestComponent />);

      // Update all sensors multiple times
      for (let i = 0; i < 10; i++) {
        const updatedDataset = largeDataset.map(sensor => ({
          ...sensor,
          latest_reading: {
            ...sensor.latest_reading!,
            temperature: sensor.latest_reading!.temperature + (i * 0.1),
          }
        }));

        mockUseSensorStatuses.mockReturnValue({
          sensorStatuses: updatedDataset,
          summary: {
            totalSensors: 20,
            onlineSensors: 20,
            activeAlerts: 0,
            criticalAlerts: 0,
            averageTemperature: -18.5 + (i * 0.1),
            temperatureRange: null,
          },
          loading: false,
          error: null,
          refetch: vi.fn(),
        });

        const updateStartTime = performance.now();
        rerender(<PerformanceTestComponent />);
        await waitForNextTick();
        const updateEndTime = performance.now();
        
        // Each update should complete quickly (under 50ms)
        expect(updateEndTime - updateStartTime).toBeLessThan(50);
      }

      expect(screen.getByTestId('performance-test')).toBeInTheDocument();
    });
  });
});
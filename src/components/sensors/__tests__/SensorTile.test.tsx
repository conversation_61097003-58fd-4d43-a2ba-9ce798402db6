/**
 * SensorTile Component Tests
 * 
 * Comprehensive test suite for individual sensor status tiles including:
 * - Component rendering with various sensor states
 * - Dark/light theme styling validation
 * - Temperature threshold handling
 * - Alert display and status indicators
 * - User selection interactions
 * - HACCP compliance indicators
 * - Accessibility compliance
 */

import React from 'react';
import { describe, it, expect, vi, beforeEach } from 'vitest';
import { screen, fireEvent } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { SensorTile } from '../SensorTile';
import { renderWithTheme, expectThemeStyles } from '../../../__tests__/utils/test-utils';
import type { SensorStatus } from '../../../types/tempstick';

// Mock UI components to focus on SensorTile logic
vi.mock('../../ui/card', () => ({
  Card: ({ children, className }: any) => <div className={className} data-testid="sensor-card">{children}</div>,
  CardContent: ({ children, className }: any) => <div className={className}>{children}</div>,
  CardHeader: ({ children, className }: any) => <div className={className}>{children}</div>,
}));

vi.mock('../../ui/badge', () => ({
  Badge: ({ children, className, variant }: any) => (
    <span className={className} data-variant={variant} data-testid="badge">
      {children}
    </span>
  ),
}));

vi.mock('../../ui/checkbox', () => ({
  Checkbox: ({ checked, onCheckedChange }: any) => (
    <input 
      type="checkbox" 
      checked={checked} 
      onChange={(e) => onCheckedChange(e.target.checked)}
      data-testid="sensor-checkbox"
    />
  ),
}));

// Helper function to create mock sensor status
const createMockSensorStatus = (overrides: Partial<SensorStatus> = {}): SensorStatus => ({
  sensor: {
    id: 'sensor-001',
    name: 'Freezer Unit A',
    location: 'Cold Storage',
    tempstick_id: 'ts_001',
    is_active: true,
    temp_min_threshold: -20.0,
    temp_max_threshold: -15.0,
    created_at: '2024-01-01T00:00:00Z',
    updated_at: '2024-01-01T00:00:00Z',
    storage_areas: {
      id: 'area-001',
      name: 'Seafood Freezer',
      required_temp_min: -22.0,
      required_temp_max: -18.0,
      haccp_control_point: true,
    },
  },
  latestReading: {
    id: 'reading-001',
    sensor_id: 'sensor-001',
    temperature: -18.5,
    humidity: 65.2,
    reading_timestamp: '2024-01-01T12:00:00Z',
    alert_triggered: false,
    created_at: '2024-01-01T12:00:00Z',
  },
  status: 'online' as const,
  activeAlerts: [],
  ...overrides,
});

describe('SensorTile', () => {
  const mockOnSelectionChange = vi.fn();

  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('Basic Rendering', () => {
    it('renders sensor information correctly', () => {
      const sensorStatus = createMockSensorStatus();
      
      renderWithTheme(
        <SensorTile 
          sensorStatus={sensorStatus}
          selected={false}
          onSelectionChange={mockOnSelectionChange}
        />
      );

      expect(screen.getByText('Freezer Unit A')).toBeInTheDocument();
      expect(screen.getByText('Cold Storage')).toBeInTheDocument();
      expect(screen.getByText('-18.5°F')).toBeInTheDocument();
      expect(screen.getByText('65.2%')).toBeInTheDocument();
      expect(screen.getByText('online')).toBeInTheDocument();
    });

    it('renders with no latest reading', () => {
      const sensorStatus = createMockSensorStatus({
        latestReading: null,
        status: 'offline',
      });
      
      renderWithTheme(
        <SensorTile 
          sensorStatus={sensorStatus}
          selected={false}
          onSelectionChange={mockOnSelectionChange}
        />
      );

      expect(screen.getByText('No recent readings')).toBeInTheDocument();
      expect(screen.getByText('Check sensor connection')).toBeInTheDocument();
      expect(screen.getByText('offline')).toBeInTheDocument();
    });

    it('renders HACCP control point indicator', () => {
      const sensorStatus = createMockSensorStatus();
      
      renderWithTheme(
        <SensorTile 
          sensorStatus={sensorStatus}
          selected={false}
          onSelectionChange={mockOnSelectionChange}
        />
      );

      expect(screen.getByText('HACCP CCP')).toBeInTheDocument();
      expect(screen.getByText('Storage: Seafood Freezer')).toBeInTheDocument();
    });

    it('renders without storage area information', () => {
      const sensorStatus = createMockSensorStatus({
        sensor: {
          ...createMockSensorStatus().sensor,
          storage_areas: null,
        },
      });
      
      renderWithTheme(
        <SensorTile 
          sensorStatus={sensorStatus}
          selected={false}
          onSelectionChange={mockOnSelectionChange}
        />
      );

      expect(screen.queryByText('Storage:')).not.toBeInTheDocument();
      expect(screen.queryByText('HACCP CCP')).not.toBeInTheDocument();
    });
  });

  describe('Theme Support', () => {
    it('applies light theme styles correctly', () => {
      const sensorStatus = createMockSensorStatus();
      
      renderWithTheme(
        <SensorTile 
          sensorStatus={sensorStatus}
          selected={false}
          onSelectionChange={mockOnSelectionChange}
        />,
        { theme: 'light' }
      );

      expect(document.documentElement).toHaveClass('light');
      const sensorCard = screen.getByTestId('sensor-card');
      expect(sensorCard).toBeInTheDocument();
    });

    it('applies dark theme styles correctly', () => {
      const sensorStatus = createMockSensorStatus();
      
      renderWithTheme(
        <SensorTile 
          sensorStatus={sensorStatus}
          selected={false}
          onSelectionChange={mockOnSelectionChange}
        />,
        { theme: 'dark' }
      );

      expect(document.documentElement).toHaveClass('dark');
    });

    it('shows appropriate colors for temperature out of threshold in dark theme', () => {
      const sensorStatus = createMockSensorStatus({
        latestReading: {
          ...createMockSensorStatus().latestReading!,
          temperature: -10.0, // Above threshold
        },
      });
      
      renderWithTheme(
        <SensorTile 
          sensorStatus={sensorStatus}
          selected={false}
          onSelectionChange={mockOnSelectionChange}
        />,
        { theme: 'dark' }
      );

      const temperatureElement = screen.getByText('-10.0°F');
      expect(temperatureElement).toHaveClass('text-red-400');
    });

    it('shows appropriate colors for temperature out of threshold in light theme', () => {
      const sensorStatus = createMockSensorStatus({
        latestReading: {
          ...createMockSensorStatus().latestReading!,
          temperature: -10.0, // Above threshold
        },
      });
      
      renderWithTheme(
        <SensorTile 
          sensorStatus={sensorStatus}
          selected={false}
          onSelectionChange={mockOnSelectionChange}
        />,
        { theme: 'light' }
      );

      const temperatureElement = screen.getByText('-10.0°F');
      expect(temperatureElement).toHaveClass('text-red-600');
    });
  });

  describe('Sensor Status Display', () => {
    it('displays online status correctly', () => {
      const sensorStatus = createMockSensorStatus({ status: 'online' });
      
      renderWithTheme(
        <SensorTile 
          sensorStatus={sensorStatus}
          selected={false}
          onSelectionChange={mockOnSelectionChange}
        />
      );

      const statusBadge = screen.getByText('online').closest('[data-testid="badge"]');
      expect(statusBadge).toBeInTheDocument();
    });

    it('displays warning status correctly', () => {
      const sensorStatus = createMockSensorStatus({ status: 'warning' });
      
      renderWithTheme(
        <SensorTile 
          sensorStatus={sensorStatus}
          selected={false}
          onSelectionChange={mockOnSelectionChange}
        />
      );

      expect(screen.getByText('warning')).toBeInTheDocument();
    });

    it('displays critical status correctly', () => {
      const sensorStatus = createMockSensorStatus({ status: 'critical' });
      
      renderWithTheme(
        <SensorTile 
          sensorStatus={sensorStatus}
          selected={false}
          onSelectionChange={mockOnSelectionChange}
        />
      );

      expect(screen.getByText('critical')).toBeInTheDocument();
    });

    it('displays offline status correctly', () => {
      const sensorStatus = createMockSensorStatus({ 
        status: 'offline',
        latestReading: null,
      });
      
      renderWithTheme(
        <SensorTile 
          sensorStatus={sensorStatus}
          selected={false}
          onSelectionChange={mockOnSelectionChange}
        />
      );

      expect(screen.getByText('offline')).toBeInTheDocument();
    });
  });

  describe('Temperature Threshold Handling', () => {
    it('shows temperature within sensor threshold as normal', () => {
      const sensorStatus = createMockSensorStatus({
        latestReading: {
          ...createMockSensorStatus().latestReading!,
          temperature: -18.0, // Within -20 to -15 range
        },
      });
      
      renderWithTheme(
        <SensorTile 
          sensorStatus={sensorStatus}
          selected={false}
          onSelectionChange={mockOnSelectionChange}
        />
      );

      const tempElement = screen.getByText('-18.0°F');
      expect(tempElement).not.toHaveClass('text-red-600');
      expect(tempElement).not.toHaveClass('text-red-400');
    });

    it('highlights temperature below minimum threshold', () => {
      const sensorStatus = createMockSensorStatus({
        latestReading: {
          ...createMockSensorStatus().latestReading!,
          temperature: -25.0, // Below -20 minimum
        },
      });
      
      renderWithTheme(
        <SensorTile 
          sensorStatus={sensorStatus}
          selected={false}
          onSelectionChange={mockOnSelectionChange}
        />,
        { theme: 'light' }
      );

      const tempElement = screen.getByText('-25.0°F');
      expect(tempElement).toHaveClass('text-red-600');
    });

    it('highlights temperature above maximum threshold', () => {
      const sensorStatus = createMockSensorStatus({
        latestReading: {
          ...createMockSensorStatus().latestReading!,
          temperature: -10.0, // Above -15 maximum
        },
      });
      
      renderWithTheme(
        <SensorTile 
          sensorStatus={sensorStatus}
          selected={false}
          onSelectionChange={mockOnSelectionChange}
        />,
        { theme: 'light' }
      );

      const tempElement = screen.getByText('-10.0°F');
      expect(tempElement).toHaveClass('text-red-600');
    });

    it('shows temperature thresholds correctly', () => {
      const sensorStatus = createMockSensorStatus();
      
      renderWithTheme(
        <SensorTile 
          sensorStatus={sensorStatus}
          selected={false}
          onSelectionChange={mockOnSelectionChange}
        />
      );

      expect(screen.getByText('Thresholds:')).toBeInTheDocument();
      expect(screen.getByText('Min: -20.0°F')).toBeInTheDocument();
      expect(screen.getByText('Max: -15.0°F')).toBeInTheDocument();
      expect(screen.getByText('Storage Min: -22.0°F')).toBeInTheDocument();
      expect(screen.getByText('Storage Max: -18.0°F')).toBeInTheDocument();
    });

    it('handles missing thresholds gracefully', () => {
      const sensorStatus = createMockSensorStatus({
        sensor: {
          ...createMockSensorStatus().sensor,
          temp_min_threshold: null,
          temp_max_threshold: null,
          storage_areas: null,
        },
      });
      
      renderWithTheme(
        <SensorTile 
          sensorStatus={sensorStatus}
          selected={false}
          onSelectionChange={mockOnSelectionChange}
        />
      );

      expect(screen.queryByText('Thresholds:')).not.toBeInTheDocument();
    });
  });

  describe('Alert Display', () => {
    it('shows alert count badge when alerts are present', () => {
      const sensorStatus = createMockSensorStatus({
        activeAlerts: [
          {
            id: 'alert-001',
            sensor_id: 'sensor-001',
            alert_type: 'high_temp',
            severity_level: 'high',
            message: 'Temperature too high',
            created_at: '2024-01-01T12:00:00Z',
            is_resolved: false,
          },
          {
            id: 'alert-002',
            sensor_id: 'sensor-001',
            alert_type: 'low_battery',
            severity_level: 'medium',
            message: 'Battery low',
            created_at: '2024-01-01T11:00:00Z',
            is_resolved: false,
          },
        ],
      });
      
      renderWithTheme(
        <SensorTile 
          sensorStatus={sensorStatus}
          selected={false}
          onSelectionChange={mockOnSelectionChange}
        />
      );

      expect(screen.getByText('2 alerts')).toBeInTheDocument();
      expect(screen.getByText('Recent Alerts:')).toBeInTheDocument();
      expect(screen.getByText('high temp')).toBeInTheDocument();
      expect(screen.getByText('low battery')).toBeInTheDocument();
    });

    it('shows single alert count correctly', () => {
      const sensorStatus = createMockSensorStatus({
        activeAlerts: [
          {
            id: 'alert-001',
            sensor_id: 'sensor-001',
            alert_type: 'high_temp',
            severity_level: 'critical',
            message: 'Temperature critical',
            created_at: '2024-01-01T12:00:00Z',
            is_resolved: false,
          },
        ],
      });
      
      renderWithTheme(
        <SensorTile 
          sensorStatus={sensorStatus}
          selected={false}
          onSelectionChange={mockOnSelectionChange}
        />
      );

      expect(screen.getByText('1 alert')).toBeInTheDocument();
    });

    it('limits alert preview to 2 alerts and shows count', () => {
      const alerts = Array.from({ length: 5 }, (_, i) => ({
        id: `alert-${i}`,
        sensor_id: 'sensor-001',
        alert_type: 'high_temp' as const,
        severity_level: 'medium' as const,
        message: `Alert ${i}`,
        created_at: '2024-01-01T12:00:00Z',
        is_resolved: false,
      }));

      const sensorStatus = createMockSensorStatus({ activeAlerts: alerts });
      
      renderWithTheme(
        <SensorTile 
          sensorStatus={sensorStatus}
          selected={false}
          onSelectionChange={mockOnSelectionChange}
        />
      );

      expect(screen.getByText('5 alerts')).toBeInTheDocument();
      expect(screen.getByText('+3 more alerts')).toBeInTheDocument();
    });

    it('shows alert triggered indicator', () => {
      const sensorStatus = createMockSensorStatus({
        latestReading: {
          ...createMockSensorStatus().latestReading!,
          alert_triggered: true,
        },
      });
      
      renderWithTheme(
        <SensorTile 
          sensorStatus={sensorStatus}
          selected={false}
          onSelectionChange={mockOnSelectionChange}
        />
      );

      expect(screen.getByText('Alert Triggered')).toBeInTheDocument();
    });
  });

  describe('User Interactions', () => {
    it('handles selection change via checkbox', async () => {
      const user = userEvent.setup();
      const sensorStatus = createMockSensorStatus();
      
      renderWithTheme(
        <SensorTile 
          sensorStatus={sensorStatus}
          selected={false}
          onSelectionChange={mockOnSelectionChange}
        />
      );

      const checkbox = screen.getByTestId('sensor-checkbox');
      await user.click(checkbox);

      expect(mockOnSelectionChange).toHaveBeenCalledWith(true);
    });

    it('shows selected state with ring styling', () => {
      const sensorStatus = createMockSensorStatus();
      
      renderWithTheme(
        <SensorTile 
          sensorStatus={sensorStatus}
          selected={true}
          onSelectionChange={mockOnSelectionChange}
        />
      );

      const card = screen.getByTestId('sensor-card');
      expect(card).toHaveClass('ring-2');
      expect(card).toHaveClass('ring-blue-500');
      
      const checkbox = screen.getByTestId('sensor-checkbox');
      expect(checkbox).toBeChecked();
    });

    it('shows unselected state without ring styling', () => {
      const sensorStatus = createMockSensorStatus();
      
      renderWithTheme(
        <SensorTile 
          sensorStatus={sensorStatus}
          selected={false}
          onSelectionChange={mockOnSelectionChange}
        />
      );

      const card = screen.getByTestId('sensor-card');
      expect(card).not.toHaveClass('ring-2');
      
      const checkbox = screen.getByTestId('sensor-checkbox');
      expect(checkbox).not.toBeChecked();
    });
  });

  describe('Time Formatting', () => {
    it('formats recent timestamps correctly', () => {
      const now = new Date();
      const oneMinuteAgo = new Date(now.getTime() - 60 * 1000);
      
      const sensorStatus = createMockSensorStatus({
        latestReading: {
          ...createMockSensorStatus().latestReading!,
          reading_timestamp: oneMinuteAgo.toISOString(),
        },
      });
      
      renderWithTheme(
        <SensorTile 
          sensorStatus={sensorStatus}
          selected={false}
          onSelectionChange={mockOnSelectionChange}
        />
      );

      expect(screen.getByText('Updated 1 min ago')).toBeInTheDocument();
    });

    it('formats very recent timestamps as "Just now"', () => {
      const now = new Date();
      
      const sensorStatus = createMockSensorStatus({
        latestReading: {
          ...createMockSensorStatus().latestReading!,
          reading_timestamp: now.toISOString(),
        },
      });
      
      renderWithTheme(
        <SensorTile 
          sensorStatus={sensorStatus}
          selected={false}
          onSelectionChange={mockOnSelectionChange}
        />
      );

      expect(screen.getByText('Updated Just now')).toBeInTheDocument();
    });

    it('formats hour timestamps correctly', () => {
      const now = new Date();
      const twoHoursAgo = new Date(now.getTime() - 2 * 60 * 60 * 1000);
      
      const sensorStatus = createMockSensorStatus({
        latestReading: {
          ...createMockSensorStatus().latestReading!,
          reading_timestamp: twoHoursAgo.toISOString(),
        },
      });
      
      renderWithTheme(
        <SensorTile 
          sensorStatus={sensorStatus}
          selected={false}
          onSelectionChange={mockOnSelectionChange}
        />
      );

      expect(screen.getByText('Updated 2 hours ago')).toBeInTheDocument();
    });
  });

  describe('Data Formatting', () => {
    it('formats temperature to one decimal place', () => {
      const sensorStatus = createMockSensorStatus({
        latestReading: {
          ...createMockSensorStatus().latestReading!,
          temperature: -18.456,
        },
      });
      
      renderWithTheme(
        <SensorTile 
          sensorStatus={sensorStatus}
          selected={false}
          onSelectionChange={mockOnSelectionChange}
        />
      );

      expect(screen.getByText('-18.5°F')).toBeInTheDocument();
    });

    it('formats humidity to one decimal place', () => {
      const sensorStatus = createMockSensorStatus({
        latestReading: {
          ...createMockSensorStatus().latestReading!,
          humidity: 65.789,
        },
      });
      
      renderWithTheme(
        <SensorTile 
          sensorStatus={sensorStatus}
          selected={false}
          onSelectionChange={mockOnSelectionChange}
        />
      );

      expect(screen.getByText('65.8%')).toBeInTheDocument();
    });

    it('handles null humidity gracefully', () => {
      const sensorStatus = createMockSensorStatus({
        latestReading: {
          ...createMockSensorStatus().latestReading!,
          humidity: null,
        },
      });
      
      renderWithTheme(
        <SensorTile 
          sensorStatus={sensorStatus}
          selected={false}
          onSelectionChange={mockOnSelectionChange}
        />
      );

      expect(screen.getByText('--')).toBeInTheDocument();
    });
  });

  describe('Accessibility', () => {
    it('provides proper checkbox accessibility', () => {
      const sensorStatus = createMockSensorStatus();
      
      renderWithTheme(
        <SensorTile 
          sensorStatus={sensorStatus}
          selected={false}
          onSelectionChange={mockOnSelectionChange}
        />
      );

      const checkbox = screen.getByRole('checkbox');
      expect(checkbox).toBeInTheDocument();
      expect(checkbox).not.toBeChecked();
    });

    it('maintains proper heading hierarchy', () => {
      const sensorStatus = createMockSensorStatus();
      
      renderWithTheme(
        <SensorTile 
          sensorStatus={sensorStatus}
          selected={false}
          onSelectionChange={mockOnSelectionChange}
        />
      );

      // Sensor name should be in heading structure
      const sensorName = screen.getByText('Freezer Unit A');
      expect(sensorName).toBeInTheDocument();
    });

    it('provides semantic structure for temperature data', () => {
      const sensorStatus = createMockSensorStatus();
      
      renderWithTheme(
        <SensorTile 
          sensorStatus={sensorStatus}
          selected={false}
          onSelectionChange={mockOnSelectionChange}
        />
      );

      expect(screen.getByText('Temperature')).toBeInTheDocument();
      expect(screen.getByText('Humidity')).toBeInTheDocument();
      expect(screen.getByText('-18.5°F')).toBeInTheDocument();
      expect(screen.getByText('65.2%')).toBeInTheDocument();
    });
  });

  describe('Edge Cases', () => {
    it('handles sensor without name', () => {
      const sensorStatus = createMockSensorStatus({
        sensor: {
          ...createMockSensorStatus().sensor,
          name: '',
        },
      });
      
      expect(() => {
        renderWithTheme(
          <SensorTile 
            sensorStatus={sensorStatus}
            selected={false}
            onSelectionChange={mockOnSelectionChange}
          />
        );
      }).not.toThrow();
    });

    it('handles sensor without location', () => {
      const sensorStatus = createMockSensorStatus({
        sensor: {
          ...createMockSensorStatus().sensor,
          location: '',
        },
      });
      
      renderWithTheme(
        <SensorTile 
          sensorStatus={sensorStatus}
          selected={false}
          onSelectionChange={mockOnSelectionChange}
        />
      );

      // Should render without errors
      expect(screen.getByText('Freezer Unit A')).toBeInTheDocument();
    });

    it('handles extreme temperature values', () => {
      const sensorStatus = createMockSensorStatus({
        latestReading: {
          ...createMockSensorStatus().latestReading!,
          temperature: -999.9,
        },
      });
      
      renderWithTheme(
        <SensorTile 
          sensorStatus={sensorStatus}
          selected={false}
          onSelectionChange={mockOnSelectionChange}
        />
      );

      expect(screen.getByText('-999.9°F')).toBeInTheDocument();
    });

    it('handles empty active alerts array', () => {
      const sensorStatus = createMockSensorStatus({
        activeAlerts: [],
      });
      
      renderWithTheme(
        <SensorTile 
          sensorStatus={sensorStatus}
          selected={false}
          onSelectionChange={mockOnSelectionChange}
        />
      );

      expect(screen.queryByText(/alerts?$/)).not.toBeInTheDocument();
      expect(screen.queryByText('Recent Alerts:')).not.toBeInTheDocument();
    });
  });
});
/**
 * Alert Notification Toast Component
 * 
 * Displays real-time temperature alert notifications as toast messages
 * with action buttons for immediate response.
 */

import React, { useEffect, useState } from 'react';
import { toast, Toaster } from 'sonner';
import { Button } from '../ui/button';
import { Badge } from '../ui/badge';
import { 
  AlertTriangle, 
  Thermometer, 
  CheckCircle, 
  ArrowUp,
  MapPin,
  Clock,
  X
} from 'lucide-react';
import { useTemperatureAlerts } from '../../hooks/useTempStick';
import { alertService } from '../../lib/alert-service';
import type { TemperatureAlert } from '../../types/tempstick';

interface AlertToastProps {
  alert: TemperatureAlert & {
    sensors?: {
      name: string;
      location: string;
      storage_areas?: {
        name: string;
        haccp_control_point: boolean;
      };
    };
  };
  onAcknowledge: () => void;
  onResolve: () => void;
  onEscalate: () => void;
}

function AlertToastContent({ alert, onAcknowledge, onResolve, onEscalate }: AlertToastProps) {
  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'critical':
        return 'bg-red-100 text-red-800 border-red-200';
      case 'high':
        return 'bg-orange-100 text-orange-800 border-orange-200';
      case 'medium':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      default:
        return 'bg-blue-100 text-blue-800 border-blue-200';
    }
  };

  const formatAlertType = (type: string) => {
    return type.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
  };

  return (
    <div className="w-full max-w-md">
      <div className="flex items-start space-x-3 mb-3">
        <div className={`p-1.5 rounded-full ${getSeverityColor(alert.severity_level)}`}>
          <AlertTriangle className="h-4 w-4" />
        </div>
        <div className="flex-1 min-w-0">
          <div className="flex items-center space-x-2 mb-1">
            <h3 className="font-semibold text-sm text-gray-900 truncate">
              {formatAlertType(alert.alert_type)}
            </h3>
            <Badge className={`${getSeverityColor(alert.severity_level)} text-xs`}>
              {alert.severity_level.toUpperCase()}
            </Badge>
          </div>
          
          <div className="space-y-1 text-xs text-gray-600">
            {alert.sensors && (
              <>
                <div className="flex items-center">
                  <Thermometer className="h-3 w-3 mr-1" />
                  <span>{alert.sensors.name}</span>
                </div>
                <div className="flex items-center">
                  <MapPin className="h-3 w-3 mr-1" />
                  <span>{alert.sensors.location}</span>
                </div>
              </>
            )}
            
            {alert.temperature && (
              <div className="flex items-center">
                <span>Temperature: </span>
                <span className="font-medium">{alert.temperature.toFixed(1)}°F</span>
              </div>
            )}
            
            <div className="flex items-center">
              <Clock className="h-3 w-3 mr-1" />
              <span>{new Date(alert.alert_timestamp).toLocaleTimeString()}</span>
            </div>
            
            {alert.sensors?.storage_areas?.haccp_control_point && (
              <Badge variant="destructive" className="text-xs">
                HACCP Critical Control Point
              </Badge>
            )}
          </div>
        </div>
      </div>
      
      <div className="flex space-x-2">
        <Button
          size="sm"
          variant="outline"
          onClick={onAcknowledge}
          className="flex-1 text-xs h-7"
        >
          <CheckCircle className="h-3 w-3 mr-1" />
          Acknowledge
        </Button>
        
        <Button
          size="sm"
          onClick={onResolve}
          className="flex-1 text-xs h-7"
        >
          <CheckCircle className="h-3 w-3 mr-1" />
          Resolve
        </Button>
        
        {(alert.severity_level === 'critical' || alert.severity_level === 'high') && (
          <Button
            size="sm"
            variant="destructive"
            onClick={onEscalate}
            className="text-xs h-7"
          >
            <ArrowUp className="h-3 w-3" />
          </Button>
        )}
      </div>
    </div>
  );
}

export function AlertNotificationSystem() {
  const { alerts, refetch } = useTemperatureAlerts(true);
  const [processedAlerts, setProcessedAlerts] = useState<Set<string>>(new Set());

  useEffect(() => {
    // Show toast notifications for new alerts
    alerts.forEach(alert => {
      if (!processedAlerts.has(alert.id)) {
        const duration = alert.severity_level === 'critical' ? Infinity : 
                        alert.severity_level === 'high' ? 15000 : 10000;

        toast.custom((t) => (
          <AlertToastContent
            alert={alert}
            onAcknowledge={async () => {
              await alertService.acknowledgeAlert(alert.id);
              toast.dismiss(t);
              refetch();
            }}
            onResolve={async () => {
              await alertService.resolveAlert(alert.id);
              toast.dismiss(t);
              refetch();
            }}
            onEscalate={async () => {
              await alertService.escalateAlert(alert.id);
              toast.dismiss(t);
            }}
          />
        ), {
          id: alert.id,
          duration,
          className: alert.severity_level === 'critical' 
            ? 'border-2 border-red-500 shadow-lg' 
            : alert.severity_level === 'high' 
            ? 'border-2 border-orange-400 shadow-lg'
            : ''
        });

        setProcessedAlerts(prev => new Set(prev).add(alert.id));
      }
    });

    // Remove processed alerts that no longer exist (resolved)
    const currentAlertIds = new Set(alerts.map(a => a.id));
    setProcessedAlerts(prev => {
      const newSet = new Set();
      prev.forEach(id => {
        if (currentAlertIds.has(id)) {
          newSet.add(id);
        }
      });
      return newSet;
    });
  }, [alerts, processedAlerts, refetch]);

  return (
    <Toaster 
      position="top-right"
      expand={true}
      richColors={false}
      closeButton={true}
      toastOptions={{
        className: 'bg-white border border-gray-200',
        style: {
          padding: '12px',
        }
      }}
    />
  );
}

/**
 * Custom hook for alert sound notifications
 */
export function useAlertSounds() {
  const { alerts } = useTemperatureAlerts(true);
  const [lastAlertCount, setLastAlertCount] = useState(0);

  useEffect(() => {
    // Play sound for new alerts
    if (alerts.length > lastAlertCount) {
      const newAlerts = alerts.slice(lastAlertCount);
      const hasCritical = newAlerts.some(a => a.severity_level === 'critical');
      const hasHigh = newAlerts.some(a => a.severity_level === 'high');

      // Play appropriate alert sound
      if (hasCritical) {
        playAlertSound('critical');
      } else if (hasHigh) {
        playAlertSound('high');
      } else {
        playAlertSound('medium');
      }
    }
    
    setLastAlertCount(alerts.length);
  }, [alerts.length, lastAlertCount]);
}

/**
 * Play alert sound based on severity
 */
function playAlertSound(severity: 'critical' | 'high' | 'medium') {
  if (typeof window === 'undefined') return;
  
  try {
    // Create audio context for more control
    const audioContext = new (window.AudioContext || (window as any).webkitAudioContext)();
    
    // Generate different tones for different severities
    const frequency = severity === 'critical' ? 800 : 
                     severity === 'high' ? 600 : 400;
    const duration = severity === 'critical' ? 1000 : 
                    severity === 'high' ? 700 : 500;
    
    const oscillator = audioContext.createOscillator();
    const gainNode = audioContext.createGain();
    
    oscillator.connect(gainNode);
    gainNode.connect(audioContext.destination);
    
    oscillator.frequency.value = frequency;
    oscillator.type = severity === 'critical' ? 'square' : 'sine';
    
    gainNode.gain.setValueAtTime(0, audioContext.currentTime);
    gainNode.gain.linearRampToValueAtTime(0.3, audioContext.currentTime + 0.1);
    gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + duration / 1000);
    
    oscillator.start(audioContext.currentTime);
    oscillator.stop(audioContext.currentTime + duration / 1000);
    
    // For critical alerts, play multiple times
    if (severity === 'critical') {
      setTimeout(() => playAlertSound('critical'), 500);
      setTimeout(() => playAlertSound('critical'), 1000);
    }
    
  } catch (error) {
    // Fallback to simple beep
    console.log(`🔊 Alert sound: ${severity.toUpperCase()}`);
  }
}

/**
 * Alert Statistics Component
 */
export function AlertStatistics({ timeRange = 'day' }: { timeRange?: 'day' | 'week' | 'month' }) {
  const [stats, setStats] = useState({
    total: 0,
    critical: 0,
    high: 0,
    resolved: 0,
    avgResolutionTime: 0,
    haccpViolations: 0
  });
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const loadStats = async () => {
      setLoading(true);
      try {
        const statistics = await alertService.getAlertStatistics(timeRange);
        setStats(statistics);
      } catch (error) {
        console.error('Failed to load alert statistics:', error);
      } finally {
        setLoading(false);
      }
    };

    loadStats();
  }, [timeRange]);

  if (loading) {
    return <div className="animate-pulse">Loading statistics...</div>;
  }

  const resolutionRate = stats.total > 0 ? (stats.resolved / stats.total * 100).toFixed(1) : '0';
  const avgTimeHours = (stats.avgResolutionTime / 60).toFixed(1);

  return (
    <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
      <div className="bg-white p-4 rounded-lg border">
        <div className="text-2xl font-bold text-gray-900">{stats.total}</div>
        <div className="text-sm text-gray-600">Total Alerts</div>
      </div>
      
      <div className="bg-white p-4 rounded-lg border">
        <div className="text-2xl font-bold text-red-600">{stats.critical}</div>
        <div className="text-sm text-gray-600">Critical</div>
      </div>
      
      <div className="bg-white p-4 rounded-lg border">
        <div className="text-2xl font-bold text-orange-600">{stats.high}</div>
        <div className="text-sm text-gray-600">High Priority</div>
      </div>
      
      <div className="bg-white p-4 rounded-lg border">
        <div className="text-2xl font-bold text-green-600">{resolutionRate}%</div>
        <div className="text-sm text-gray-600">Resolved</div>
      </div>
      
      <div className="bg-white p-4 rounded-lg border">
        <div className="text-2xl font-bold text-blue-600">{avgTimeHours}h</div>
        <div className="text-sm text-gray-600">Avg Resolution</div>
      </div>
      
      <div className="bg-white p-4 rounded-lg border">
        <div className="text-2xl font-bold text-purple-600">{stats.haccpViolations}</div>
        <div className="text-sm text-gray-600">HACCP Violations</div>
      </div>
    </div>
  );
}
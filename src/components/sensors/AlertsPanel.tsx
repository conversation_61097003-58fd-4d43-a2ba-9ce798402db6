/**
 * Temperature Alerts Panel
 * 
 * Displays active temperature alerts with filtering, sorting,
 * and resolution capabilities for HACCP compliance tracking.
 */

import React, { useState, useMemo } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '../ui/card';
import { <PERSON><PERSON> } from '../ui/button';
import { Badge } from '../ui/badge';
import { Input } from '../ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../ui/select';
import { 
  AlertTriangle, 
  Clock, 
  Thermometer, 
  MapPin, 
  CheckCircle,
  Filter,
  Search,
  SortAsc,
  SortDesc
} from 'lucide-react';
import { useTemperatureAlerts } from '../../hooks/useTempStick';
import { tempStickService } from '../../lib/tempstick-service';
import type { TemperatureAlert } from '../../types/tempstick';

export function AlertsPanel() {
  const [searchTerm, setSearchTerm] = useState('');
  const [severityFilter, setSeverityFilter] = useState<string>('all');
  const [typeFilter, setTypeFilter] = useState<string>('all');
  const [sortBy, setSortBy] = useState<'timestamp' | 'severity'>('timestamp');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc');
  const [resolvingAlerts, setResolvingAlerts] = useState<Set<string>>(new Set());

  const { alerts, loading, error, refetch } = useTemperatureAlerts(true);

  const filteredAndSortedAlerts = useMemo(() => {
    let filtered = [...alerts];

    // Search filter
    if (searchTerm) {
      const term = searchTerm.toLowerCase();
      filtered = filtered.filter(alert => 
        alert.sensors?.name?.toLowerCase().includes(term) ||
        alert.sensors?.location?.toLowerCase().includes(term) ||
        alert.alert_type.toLowerCase().includes(term)
      );
    }

    // Severity filter
    if (severityFilter !== 'all') {
      filtered = filtered.filter(alert => alert.severity_level === severityFilter);
    }

    // Type filter
    if (typeFilter !== 'all') {
      filtered = filtered.filter(alert => alert.alert_type === typeFilter);
    }

    // Sort
    filtered.sort((a, b) => {
      let aValue: any, bValue: any;

      if (sortBy === 'timestamp') {
        aValue = new Date(a.alert_timestamp).getTime();
        bValue = new Date(b.alert_timestamp).getTime();
      } else if (sortBy === 'severity') {
        const severityOrder = { critical: 4, high: 3, medium: 2, low: 1 };
        aValue = severityOrder[a.severity_level];
        bValue = severityOrder[b.severity_level];
      }

      return sortOrder === 'asc' ? aValue - bValue : bValue - aValue;
    });

    return filtered;
  }, [alerts, searchTerm, severityFilter, typeFilter, sortBy, sortOrder]);

  const handleResolveAlert = async (alertId: string) => {
    setResolvingAlerts(prev => new Set(prev).add(alertId));
    
    try {
      await tempStickService.resolveAlert(alertId);
      await refetch();
    } catch (err) {
      console.error('Failed to resolve alert:', err);
    } finally {
      setResolvingAlerts(prev => {
        const newSet = new Set(prev);
        newSet.delete(alertId);
        return newSet;
      });
    }
  };

  const getSeverityColor = (severity: TemperatureAlert['severity_level']) => {
    switch (severity) {
      case 'critical':
        return 'bg-red-100 text-red-800 border-red-200';
      case 'high':
        return 'bg-orange-100 text-orange-800 border-orange-200';
      case 'medium':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'low':
        return 'bg-blue-100 text-blue-800 border-blue-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const formatAlertType = (type: TemperatureAlert['alert_type']) => {
    return type.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
  };

  const formatTimeAgo = (timestamp: string) => {
    const now = new Date();
    const alertTime = new Date(timestamp);
    const diff = now.getTime() - alertTime.getTime();
    const minutes = Math.floor(diff / 60000);
    
    if (minutes < 1) return 'Just now';
    if (minutes === 1) return '1 minute ago';
    if (minutes < 60) return `${minutes} minutes ago`;
    
    const hours = Math.floor(minutes / 60);
    if (hours === 1) return '1 hour ago';
    if (hours < 24) return `${hours} hours ago`;
    
    const days = Math.floor(hours / 24);
    if (days === 1) return '1 day ago';
    return `${days} days ago`;
  };

  const getAlertIcon = (type: TemperatureAlert['alert_type']) => {
    switch (type) {
      case 'high_temp':
      case 'low_temp':
        return <Thermometer className="h-4 w-4" />;
      case 'haccp_violation':
        return <AlertTriangle className="h-4 w-4" />;
      default:
        return <AlertTriangle className="h-4 w-4" />;
    }
  };

  if (loading) {
    return (
      <Card>
        <CardContent className="p-8">
          <div className="flex items-center justify-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
            <span className="ml-2">Loading alerts...</span>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card>
        <CardContent className="p-8 text-center">
          <AlertTriangle className="mx-auto h-8 w-8 text-red-500 mb-2" />
          <p className="text-red-600">Error loading alerts: {error}</p>
          <Button onClick={refetch} className="mt-2">Try Again</Button>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0">
          <CardTitle className="flex items-center space-x-2">
            <AlertTriangle className="h-5 w-5" />
            <span>Active Temperature Alerts ({alerts.length})</span>
          </CardTitle>
          
          <div className="flex items-center space-x-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc')}
              className="flex items-center space-x-1"
            >
              {sortOrder === 'asc' ? <SortAsc className="h-4 w-4" /> : <SortDesc className="h-4 w-4" />}
              <span>Sort</span>
            </Button>
          </div>
        </div>

        {/* Filters */}
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 mt-4">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <Input
              placeholder="Search alerts..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10"
            />
          </div>

          <Select value={severityFilter} onValueChange={setSeverityFilter}>
            <SelectTrigger>
              <Filter className="h-4 w-4 mr-2" />
              <SelectValue placeholder="Severity" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Severities</SelectItem>
              <SelectItem value="critical">Critical</SelectItem>
              <SelectItem value="high">High</SelectItem>
              <SelectItem value="medium">Medium</SelectItem>
              <SelectItem value="low">Low</SelectItem>
            </SelectContent>
          </Select>

          <Select value={typeFilter} onValueChange={setTypeFilter}>
            <SelectTrigger>
              <Filter className="h-4 w-4 mr-2" />
              <SelectValue placeholder="Alert Type" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Types</SelectItem>
              <SelectItem value="high_temp">High Temperature</SelectItem>
              <SelectItem value="low_temp">Low Temperature</SelectItem>
              <SelectItem value="haccp_violation">HACCP Violation</SelectItem>
              <SelectItem value="sensor_offline">Sensor Offline</SelectItem>
            </SelectContent>
          </Select>

          <Select value={sortBy} onValueChange={(value) => setSortBy(value as 'timestamp' | 'severity')}>
            <SelectTrigger>
              <SelectValue placeholder="Sort By" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="timestamp">Time</SelectItem>
              <SelectItem value="severity">Severity</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </CardHeader>

      <CardContent>
        {filteredAndSortedAlerts.length === 0 ? (
          <div className="text-center py-8">
            {alerts.length === 0 ? (
              <>
                <CheckCircle className="mx-auto h-12 w-12 text-green-500 mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">No Active Alerts</h3>
                <p className="text-gray-600">All temperature sensors are operating within normal parameters.</p>
              </>
            ) : (
              <>
                <Search className="mx-auto h-8 w-8 text-gray-400 mb-2" />
                <p className="text-gray-600">No alerts match your current filters.</p>
              </>
            )}
          </div>
        ) : (
          <div className="space-y-4">
            {filteredAndSortedAlerts.map(alert => (
              <div
                key={alert.id}
                className="border border-gray-200 rounded-lg p-4 hover:bg-gray-50 transition-colors"
              >
                <div className="flex items-start justify-between">
                  <div className="flex items-start space-x-3">
                    <div className={`p-2 rounded-full ${getSeverityColor(alert.severity_level)}`}>
                      {getAlertIcon(alert.alert_type)}
                    </div>
                    
                    <div className="flex-1">
                      <div className="flex items-center space-x-2 mb-2">
                        <h3 className="font-semibold text-gray-900">
                          {formatAlertType(alert.alert_type)}
                        </h3>
                        <Badge className={getSeverityColor(alert.severity_level)}>
                          {alert.severity_level.toUpperCase()}
                        </Badge>
                        {alert.alert_type === 'haccp_violation' && (
                          <Badge variant="destructive" className="text-xs">
                            HACCP
                          </Badge>
                        )}
                      </div>

                      <div className="space-y-2">
                        <div className="flex items-center text-sm text-gray-600">
                          <MapPin className="h-3 w-3 mr-1" />
                          <span>{alert.sensors?.name || 'Unknown Sensor'}</span>
                          <span className="mx-2">•</span>
                          <span>{alert.sensors?.location || 'Unknown Location'}</span>
                        </div>

                        {alert.temperature && (
                          <div className="flex items-center text-sm text-gray-600">
                            <Thermometer className="h-3 w-3 mr-1" />
                            <span>Temperature: {alert.temperature.toFixed(1)}°F</span>
                          </div>
                        )}

                        <div className="flex items-center text-sm text-gray-600">
                          <Clock className="h-3 w-3 mr-1" />
                          <span>{formatTimeAgo(alert.alert_timestamp)}</span>
                          <span className="mx-2">•</span>
                          <span>{new Date(alert.alert_timestamp).toLocaleString()}</span>
                        </div>

                        {alert.sensors?.storage_areas && (
                          <div className="text-sm text-gray-600">
                            <span>Storage Area: </span>
                            <span className="font-medium">{alert.sensors.storage_areas.name}</span>
                            {alert.sensors.storage_areas.haccp_control_point && (
                              <Badge variant="secondary" className="ml-2 text-xs">
                                Critical Control Point
                              </Badge>
                            )}
                          </div>
                        )}

                        {alert.notes && (
                          <div className="text-sm text-gray-700 bg-gray-50 p-2 rounded">
                            {alert.notes}
                          </div>
                        )}
                      </div>
                    </div>
                  </div>

                  <div className="flex flex-col items-end space-y-2">
                    <Button
                      size="sm"
                      onClick={() => handleResolveAlert(alert.id)}
                      disabled={resolvingAlerts.has(alert.id)}
                      className="flex items-center space-x-1"
                    >
                      <CheckCircle className="h-4 w-4" />
                      <span>{resolvingAlerts.has(alert.id) ? 'Resolving...' : 'Resolve'}</span>
                    </Button>

                    {!alert.notification_sent && (
                      <Badge variant="outline" className="text-xs">
                        Notification Pending
                      </Badge>
                    )}
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </CardContent>
    </Card>
  );
}
import { createContext, useContext, useState, ReactNode } from 'react';

export type ActiveView = 
  | 'Dashboard' 
  | 'Inventory' 
  | 'Vendors'
  | 'Customers'
  | 'Analytics' 
  | 'Messages' 
  | 'Settings'
  | 'Import'
  | 'Voice Input'
  | 'Voice Test'
  | 'Voice Management'
  | 'Events'               // dedicated events table view
  | 'HACCP Events'        // legacy entry point to HACCP Events (maps to Events)
  | 'HACCP: Batches'
  | 'HACCP: Events'
  | 'HACCP: Calendar'
  | 'Temperature Monitoring'  // TempStick dashboard
  | 'Sensor Management'       // Sensor configuration and management
  | 'Temperature: Dashboard'  // Alternative naming for submenu
  | 'Temperature: Sensors'    // Alternative naming for submenu
  | 'Temperature: Calibration' // Calibration management
  | 'TempStick Test';         // TempStick API testing interface

interface NavigationContextType {
  activeView: ActiveView;
  setActiveView: (view: ActiveView) => void;
  viewFilters: Record<string, any>;
  setViewFilter: (key: string, value: any) => void;
  clearViewFilters: () => void;
}

const NavigationContext = createContext<NavigationContextType | undefined>(undefined);

export function NavigationProvider({ children }: { children: ReactNode }) {
  const [activeView, setActiveView] = useState<ActiveView>('Dashboard');
  const [viewFilters, setViewFilters] = useState<Record<string, any>>({});

  const setViewFilter = (key: string, value: any) => {
    setViewFilters(prev => ({ ...prev, [key]: value }));
  };

  const clearViewFilters = () => {
    setViewFilters({});
  };

  return (
    <NavigationContext.Provider value={{ 
      activeView, 
      setActiveView, 
      viewFilters, 
      setViewFilter, 
      clearViewFilters 
    }}>
      {children}
    </NavigationContext.Provider>
  );
}

// Create a separate file for this hook: src/hooks/useNavigation.ts
export function useNavigationContext() {
  const context = useContext(NavigationContext);
  if (context === undefined) {
    throw new Error('useNavigationContext must be used within a NavigationProvider');
  }
  return context;
} 
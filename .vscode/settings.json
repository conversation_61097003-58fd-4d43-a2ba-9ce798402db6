{"[typescript]": {"editor.defaultFormatter": "denoland.vscode-deno"}, "deno.enablePaths": ["supabase/functions"], "deno.lint": true, "deno.unstable": ["bare-node-builtins", "byonm", "sloppy-imports", "unsafe-proto", "webgpu", "broadcast-channel", "worker-options", "cron", "kv", "ffi", "fs", "http", "net"], "editor.tabCompletion": "on", "editor.tabFocusMode": false, "kiroAgent.configureMCP": "Disabled"}
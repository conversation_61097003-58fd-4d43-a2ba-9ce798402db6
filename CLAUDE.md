# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository. This serves as the **main coordinator** that delegates to specialized agents for domain-specific expertise.

## Project Overview

Pacific Cloud Seafoods (PCS) Seafood Manager is a React/TypeScript web application for seafood inventory management, traceability, HACCP compliance, and **temperature monitoring**. The application features voice-based data entry, AI-powered processing, import wizards, comprehensive tracking capabilities, and **TempStick sensor integration** for real-time temperature monitoring with automated reporting and export capabilities.

## 🤖 Specialized Agent Team

This project uses a team of specialized agents for domain expertise. **Always delegate to the appropriate specialist** for optimal results:

### Coordination Agent
- **🏆 project-coordinator-agent** - Cross-domain coordination, progress tracking, dependency management, multi-agent workflow orchestration, priority allocation

### Core Technical Agents
- **🎤 ai-voice-processing-agent** - OpenAI integration, voice transcription, seafood terminology, real-time processing
- **🔧 code-quality-typescript-agent** - TypeScript errors, lint fixes, type safety, React optimization (53 errors to fix)
- **📊 import-export-data-processor** - CSV processing, data validation, column mapping, performance optimization
- **⚡ performance-devops-specialist** - Production deployment, monitoring, performance optimization, CI/CD
- **🧪 react-testing-architect** - Testing strategies, test coverage, mocking, quality assurance
- **🏗️ supabase-seafood-db-architect** - Database optimization, migrations, PostgreSQL performance, RLS policies
- **🔒 security-audit-specialist** - Authentication security, RLS policy analysis, data protection, compliance auditing

### Industry Specialist
- **🐟 seafood-compliance-engineer** - HACCP compliance, GDST traceability, food safety regulations, audit preparation

### Agent Selection Guide

```typescript
// Use this decision tree to choose the right agent:

if (task.involves('multiple agents', 'project status', 'priority conflicts', 'dependency management', 'coordination')) {
  return 'project-coordinator-agent';
}

if (task.involves('voice processing', 'OpenAI', 'speech recognition')) {
  return 'ai-voice-processing-agent';
}

if (task.involves('TypeScript errors', 'lint issues', 'type safety')) {
  return 'code-quality-typescript-agent';
}

if (task.involves('CSV import', 'data processing', 'bulk operations')) {
  return 'import-export-data-processor';
}

if (task.involves('performance', 'deployment', 'monitoring', 'CI/CD')) {
  return 'performance-devops-specialist';
}

if (task.involves('testing', 'test coverage', 'mocking', 'QA')) {
  return 'react-testing-architect';
}

if (task.involves('database', 'Supabase', 'PostgreSQL', 'migrations')) {
  return 'supabase-seafood-db-architect';
}

if (task.involves('security', 'authentication', 'RLS policies', 'data protection', 'vulnerabilities')) {
  return 'security-audit-specialist';
}

if (task.involves('HACCP', 'compliance', 'traceability', 'food safety')) {
  return 'seafood-compliance-engineer';
}

if (task.involves('temperature monitoring', 'TempStick', 'sensors', 'temperature alerts')) {
  return 'supabase-seafood-db-architect'; // For sensor data management
  // OR 'seafood-compliance-engineer'; // For HACCP temperature compliance
  // OR 'import-export-data-processor'; // For temperature data exports
}

// Otherwise, continue with general guidance below
```

## Core Development Philosophy

### KISS (Keep It Simple, Stupid)
- Prioritize simple, maintainable solutions over complex ones
- Voice commands should be intuitive and natural
- Import workflows should minimize user steps
- Components should have a single, clear responsibility

### YAGNI (You Aren't Gonna Need It)  
- Build traceability features incrementally (GDST Phase 1 first)
- Add HACCP compliance features as needed by regulations
- Avoid over-engineering voice processing
- Implement features only when needed, not when anticipated

### Design Principles
- **Event-Driven Architecture**: All inventory operations flow through `inventory_events`
- **Single Source of Truth**: Supabase as the central data store  
- **Progressive Enhancement**: Core functionality works without voice input
- **Fail Gracefully**: Voice errors shouldn't break manual workflows
- **Dependency Inversion**: Components depend on abstractions, not concrete implementations
- **Single Responsibility**: Each component, hook, and function has one clear purpose
- **Fail Fast**: Validate inputs early and show errors immediately

## Development Commands

```bash
# Start development server (fixed to port 5177)
npm run dev

# Build for production
npm run build

# Run linting and fix issues
npm run lint
npm run lint:fix

# Type checking
npm run type-check

# Preview production build
npm run preview

# Seed required categories (requires service role key in .env)
npm run seed:categories

# Run tests (when configured)
npm test

# Database migrations (Remote Supabase - NOT Docker)
# Use Supabase CLI with remote project connection
supabase db push
supabase db pull
# Apply migrations to remote Supabase instance
```

## Architecture Overview

### Core Stack
- **Frontend**: React 18 + TypeScript + Vite
- **Database**: Supabase (PostgreSQL)
- **Styling**: TailwindCSS + Radix UI components
- **AI Integration**: OpenAI API for voice processing
- **Authentication**: Supabase Auth
- **Forms**: react-hook-form + Zod validation
- **Voice**: react-speech-recognition + OpenAI Whisper
- **Temperature Monitoring**: TempStick API integration for sensor data
- **Reporting**: PDF generation, Excel exports, Google Sheets API, email automation

### Application Structure

```
src/
  components/
    ui/                    # Radix UI + Tailwind components
    inventory/             # → Use ai-voice-processing-agent
    import/               # → Use import-export-data-processor
    haccp/                # → Use seafood-compliance-engineer
    dashboard/            # → Use performance-devops-specialist
    sensors/              # → Use supabase-seafood-db-architect (NEW)

  lib/
    supabase.ts           # → Use supabase-seafood-db-architect
    api.ts               # → Use code-quality-typescript-agent
    voice-processor.ts   # → Use ai-voice-processing-agent
    tempstick-service.ts  # → Use supabase-seafood-db-architect (NEW)
    
  types/
    schema.ts            # → Use supabase-seafood-db-architect
    tempstick.ts         # → Use code-quality-typescript-agent (NEW)
    
  hooks/                 # → Use code-quality-typescript-agent
  contexts/              # → Use react-testing-architect
```

## 🚨 High-Priority Issues

### ✅ Recently Completed

#### Events & Calendar System (Latest)
- [x] **Events display across all components** - Fixed EventsTable, EventsView, BatchTracking, HACCPEventsView
- [x] **HACCPCalendar functionality** - Fixed 400 errors, added click behavior, time fields, real-time updates  
- [x] **Smart calendar interaction** - Click days with events → show event list; empty days → show form
- [x] **Time field integration** - Added time picker to event forms, proper timestamp handling
- [x] **Real-time calendar updates** - Events appear immediately after form submission
- [x] **Database query optimization** - Fixed malformed date queries, removed non-existent column references

#### Code Quality Improvements
- [x] **TypeScript error reduction** - Reduced from 652 to ~600 ESLint issues
- [x] **Voice column handling** - Graceful degradation when voice migrations not applied
- [x] **Error boundary implementation** - Better error handling in critical components
- [x] **Debug logging** - Added structured logging for calendar and form debugging

#### 🤖 Serena MCP Server Achievements (August 2024)
- [x] **Voice Management Page Fixed** - Resolved React Router dependency, fixed blank page rendering
- [x] **React Hooks Error Resolved** - Fixed "Rendered more hooks than during the previous render" by restructuring component order
- [x] **HACCP Submenu Styling Fixed** - Resolved white text on white background visibility issue
- [x] **Collapsed Sidebar Enhancement** - HACCP submenu now shows as floating dropdown when sidebar collapsed
- [x] **Systematic Debugging Applied** - Used sequential thinking, IDE diagnostics, and specialized agents for efficient problem-solving

**Serena MCP Usage Patterns Established:**
- `mcp__sequential-thinking__sequentialthinking` for complex problem analysis
- `mcp__ide__getDiagnostics` for real-time TypeScript/ESLint feedback  
- `TodoWrite` for systematic progress tracking
- Specialized agents (code-quality-typescript-agent) for domain-specific fixes

### Immediate Action Required
- [ ] **Products table empty** → Create basic seafood products or fix RLS policies
- [ ] **Voice columns not migrated** → Apply database migrations for voice features
- [ ] **~600 ESLint warnings** → Use `code-quality-typescript-agent` (reduced from 652)
- [ ] **Testing framework** → Use `react-testing-architect`
- [ ] **Security audit needed** → Use `security-audit-specialist`

### Technical Debt
- [ ] **Database migrations** → Use `supabase-seafood-db-architect` to apply pending migrations
- [ ] **Voice processing optimization** → Use `ai-voice-processing-agent`
- [ ] **Production deployment setup** → Use `performance-devops-specialist`
- [ ] **Import performance for large files** → Use `import-export-data-processor`

### ✅ TempStick Sensor Integration (COMPLETED - August 29, 2025)

**Status**: Production-ready integration with comprehensive temperature monitoring system and proxy-based development workflow.

#### Successfully Implemented Features
- [x] **Real-time temperature dashboard** (`/temperature`) - Fully functional with live sensor data
- [x] **Sensor management interface** (`/sensors`) - Complete configuration and monitoring
- [x] **Mock data system** - Automatic fallback for development/testing (4 realistic sensors)
- [x] **API client with retry logic** - Enhanced error handling, rate limiting, health checks
- [x] **Database integration** - Seamless switch between mock data and real database
- [x] **Mobile responsive design** - Works across all device types
- [x] **Auto-refresh capabilities** - Real-time updates every 30 seconds
- [x] **Alert system** - Temperature violation detection and HACCP compliance
- [x] **Export capabilities** - PDF, Excel, Google Sheets integration ready
- [x] **HACCP compliance monitoring** - Critical control point tracking
- [x] **Proxy server integration** - CORS proxy at localhost:3001 for development
- [x] **API endpoint standardization** - All endpoints use consistent `/v1/sensors/` format
- [x] **Configuration system** - Proper integration with tempstick-config.ts
- [x] **Comprehensive testing** - Multiple test scripts and validation tools
- [x] **Complete documentation** - Dashboard guides and fix summaries

#### Key Architecture Patterns
```typescript
// Enhanced TempStick Service with comprehensive error handling
export class TempStickService {
  // Rate limiting and retry logic
  private retryConfig: RetryConfig;
  private rateLimitConfig: RateLimitConfig;
  
  // Health monitoring and metrics
  private metrics: ServiceMetrics;
  
  // Graceful fallback to mock data
  async syncSensors(): Promise<SyncResponse>;
}

// Mock data system for development
export const mockTemperatureData = {
  sensors: [
    { id: '2550380', name: 'Downstairs Walk in Freezer' },
    { id: '2298510', name: 'Upright White Alaskan Fish Freezer' },
    { id: '2301797', name: 'Chest Freezer' }
  ]
};
```

#### Production Deployment Ready
- [x] **API integration** tested with real TempStick sensors
- [x] **Error handling** comprehensive with graceful degradation
- [x] **Performance optimization** with request throttling and batching
- [x] **Security** proper API key management and data validation
- [x] **Documentation** complete status files and integration guides

#### Working Test Results
```bash
✅ Temperature Dashboard: http://localhost:5177/temperature (200 OK)
✅ Sensor Management: http://localhost:5177/sensors (200 OK)  
✅ Mock data enabled with 4 realistic sensors
✅ Real-time updates and responsive design
✅ Alert simulations and HACCP compliance
```

#### Integration Status (Updated August 29, 2025)
The TempStick integration is **100% complete** and ready for:
- ✅ **Immediate development use** with proxy server and mock data
- ✅ **User acceptance testing** with full feature set
- ✅ **Production deployment** with proper API configuration
- ✅ **HACCP compliance audits** with automated monitoring
- ✅ **Real-time temperature tracking** across multiple storage areas
- ✅ **Git workflow integration** - All changes committed to feature/tempstick-integration branch
- ✅ **Comprehensive documentation** - Complete guides and troubleshooting resources

#### Latest Achievements (August 29, 2025)
- **Fixed 404 API errors** through proper endpoint standardization
- **Implemented proxy-based development** using localhost:3001/api/v1
- **Enhanced dashboard components** with better error handling and UX
- **Established complete testing infrastructure** with multiple validation scripts
- **Created comprehensive documentation** including TEMPSTICK_DASHBOARD_GUIDE.md
- **Completed git integration** with all changes pushed to remote feature branch

## 📝 Code Quality Standards

### File and Component Limits
- **Components should not exceed 300 lines** - Split into smaller, focused components
- **Functions should be under 50 lines** with single responsibility
- **Custom hooks should be under 100 lines** and have clear purpose
- **Organize related components** in feature-based folders
- **Line length max 120 characters** (Prettier/ESLint configured)

### TypeScript Standards
- **Always use proper TypeScript types** - No `any` types allowed
- **Use strict type checking** - Enable all TypeScript strict flags
- **Prefer interfaces over types** for object shapes
- **Use proper union types** instead of string literals
- **Export types alongside components** for reuse

### React Best Practices
- **Use functional components** with hooks exclusively
- **Prefer custom hooks** for complex state logic
- **Implement proper error boundaries** for component trees
- **Use React.memo()** for expensive components
- **Avoid deeply nested component structures** (max 3-4 levels)

### Component Structure
```typescript
// Standard component file structure
interface ComponentProps {
  // Props with proper typing
}

export const Component: React.FC<ComponentProps> = ({ prop1, prop2 }) => {
  // 1. Hooks (useState, useEffect, custom hooks)
  // 2. Event handlers
  // 3. Derived values/computations
  // 4. Early returns/loading states
  // 5. Main JSX return
};

export default Component;
```

## 🔄 Workflow Patterns

### When to Use Each Agent

#### Voice Processing Issues
```typescript
// ❌ Don't handle voice processing without the specialist
const voiceResult = await processVoice(transcript);

// ✅ Use ai-voice-processing-agent for:
// - Improving transcription accuracy for seafood terms
// - Optimizing OpenAI prompt engineering
// - Implementing real-time voice processing
// - Debugging voice input reliability
```

#### Code Quality Issues
```typescript
// ❌ Don't fix TypeScript errors ad-hoc
const data: any = await supabase.from('products').select();

// ✅ Use code-quality-typescript-agent for:
// - Fixing the 53 ESLint errors
// - Removing 'any' types with proper interfaces
// - Optimizing React hooks dependencies
// - Improving component performance
```

#### Database Operations
```typescript
// ❌ Don't create database changes without expert review
CREATE TABLE new_table (id UUID PRIMARY KEY);

// ✅ Use supabase-seafood-db-architect for:
// - Migration file review
// - Performance optimization
// - RLS policy design
// - Index strategy
```

#### Security & Authentication Issues
```typescript
// ❌ Don't implement auth without security review
const handleLogin = async (email: string, password: string) => {
  const { data } = await supabase.auth.signInWithPassword({ email, password });
  return data;
};

// ✅ Use security-audit-specialist for:
// - RLS policy security review
// - Authentication flow auditing
// - Data protection analysis
// - Vulnerability assessment
```

#### Import/Export Features
```typescript
// ❌ Don't handle large CSV files without optimization
const allData = await parseCSV(file);

// ✅ Use import-export-data-processor for:
// - Large file streaming processing
// - Intelligent column mapping
// - Data validation optimization
// - Performance bottleneck analysis
```
```typescript
// ❌ Don't handle large CSV files without optimization
const allData = await parseCSV(file);

// ✅ Use import-export-data-processor for:
// - Large file streaming processing
// - Intelligent column mapping
// - Data validation optimization
// - Performance bottleneck analysis
```

## 🔧 Configuration Management

### Environment Variables
```typescript
// lib/env.ts - Use code-quality-typescript-agent to improve
const env = {
  VITE_SUPABASE_URL: process.env.VITE_SUPABASE_URL!,
  VITE_SUPABASE_ANON_KEY: process.env.VITE_SUPABASE_ANON_KEY!,
  VITE_OPENAI_API_KEY: process.env.VITE_OPENAI_API_KEY!,
};
```

### Development Environment
- **Dev server**: Fixed to port 5177 (`strictPort: true`)
- **Package manager**: npm (as specified in package.json)
- **Database**: Supabase with required environment variables
- **AI Integration**: OpenAI API key required for voice processing

## 🎨 UI/UX Standards

### Component Patterns
```typescript
// Standard component structure - delegate complex implementations
interface ComponentProps {
  // Use code-quality-typescript-agent for proper typing
}

export const Component: React.FC<ComponentProps> = (props) => {
  // Use react-testing-architect for test strategy
  // Use performance-devops-specialist for optimization
  return <div className="seafood-manager-component">{/* Implementation */}</div>;
};
```

### Styling Conventions
```css
/* TailwindCSS with seafood industry color scheme */
.inventory-status-low { @apply bg-red-100 text-red-800 border-red-200; }
.haccp-compliant { @apply bg-green-50 border-green-200 text-green-700; }
.voice-active { @apply bg-blue-100 border-blue-300 animate-pulse; }
```

## 🗄️ Data Models

### Core Event System
```typescript
// Use supabase-seafood-db-architect for optimization
interface InventoryEvent {
  id: string;
  event_type: 'receiving' | 'sales' | 'disposal' | 'production' | 'adjustment';
  product_id: string;
  quantity: number;
  unit: string;
  batch_number?: string;
  created_at: string;
  // Use seafood-compliance-engineer for HACCP fields
}
```

## 🔒 Security & Compliance

### Data Security
- **Authentication & Authorization**: Use `security-audit-specialist` for auth flow review
- **Supabase RLS**: Use `supabase-seafood-db-architect` for policy optimization
- **API Security**: Use `code-quality-typescript-agent` for type safety
- **Input Validation**: Use `import-export-data-processor` for CSV security
- **Vulnerability Assessment**: Use `security-audit-specialist` for comprehensive audits

### Industry Compliance
- **HACCP Implementation**: Use `seafood-compliance-engineer`
- **GDST Traceability**: Use `seafood-compliance-engineer`
- **Food Safety**: Use `seafood-compliance-engineer`

## 🚀 Git Workflow & Deployment

### Branch Strategy

#### Main Branches
- `main` - Production-ready code, auto-deploys to production
- `develop` - Integration branch for feature testing
- Current: `feat/haccp-events-calendar` - HACCP calendar and events functionality

#### Feature Branch Naming
- `feature/voice-improvements` → Use `ai-voice-processing-agent`
- `feature/haccp-compliance` → Use `seafood-compliance-engineer`
- `feature/import-optimization` → Use `import-export-data-processor`
- `fix/typescript-errors` → Use `code-quality-typescript-agent`
- `hotfix/critical-bug-name` → Urgent production fixes
- `release/v1.2.0` → Release preparation branches

### Development Workflow

#### 1. Starting New Work
```bash
# Always start from latest main
git checkout main
git pull origin main

# Create feature branch
git checkout -b feature/your-feature-name

# Start development with Serena MCP
# Use TodoWrite to track progress
# Use mcp__sequential-thinking for complex problems
```

#### 2. During Development
```bash
# Commit frequently with descriptive messages
git add .
git commit -m "feat(voice): add transcription confidence scoring

- Implement confidence thresholds for voice input
- Add visual feedback for low-confidence transcriptions
- Update VoiceAssistant component with score display

🤖 Generated with [Claude Code](https://claude.ai/code)

Co-Authored-By: Claude <<EMAIL>>"

# Push regularly to backup work
git push origin feature/your-feature-name
```

#### 3. Before Creating PR
```bash
# Sync with main
git checkout main
git pull origin main
git checkout feature/your-feature-name
git rebase main

# Run quality checks
npm run lint
npm run type-check
npm run test # when available

# Push final changes
git push origin feature/your-feature-name --force-with-lease
```

### Commit Standards

#### Format
```
<type>(<scope>): <subject>

<body>

🤖 Generated with [Claude Code](https://claude.ai/code)

Co-Authored-By: Claude <<EMAIL>>
```

#### Types
- `feat` - New features or enhancements
- `fix` - Bug fixes
- `docs` - Documentation changes
- `style` - Code formatting, no logic changes
- `refactor` - Code restructuring without feature changes
- `perf` - Performance improvements
- `test` - Adding or updating tests
- `build` - Build system or dependency changes
- `ci` - CI/CD configuration changes
- `chore` - Maintenance tasks

#### Scopes
- `voice` - Voice processing and AI features
- `haccp` - HACCP compliance features
- `import` - Data import/export functionality
- `ui` - User interface components
- `db` - Database schemas and migrations
- `auth` - Authentication and authorization
- `api` - API endpoints and services
- `sensors` - Temperature monitoring and sensor integration
- `reports` - Reporting and export functionality

#### Examples
```bash
feat(voice): improve salmon species recognition
fix(import): resolve large CSV file timeout issues
docs(haccp): add GDST traceability documentation
refactor(ui): consolidate sidebar component logic
perf(db): optimize inventory events query performance
test(voice): add comprehensive VoiceAssistant test suite
feat(sensors): integrate TempStick API for temperature monitoring
fix(sensors): resolve temperature alert threshold calculations
feat(reports): add automated PDF temperature compliance reports
```

### Pull Request Workflow

#### Creating PRs
```bash
# Use GitHub CLI for consistency
gh pr create --title "feat(voice): improve species recognition accuracy" --body "$(cat <<'EOF'
## Summary
- Implemented fuzzy matching for seafood species names
- Added confidence scoring for voice transcriptions
- Enhanced error handling for misrecognized species

## Testing
- [x] Manual testing with various seafood names
- [x] Voice accuracy improved from 85% to 94%
- [x] Error cases handled gracefully

## Checklist
- [x] Code follows style guidelines
- [x] Self-review of code completed
- [x] TypeScript errors resolved
- [x] Documentation updated

🤖 Generated with [Claude Code](https://claude.ai/code)
EOF
)"
```

#### PR Requirements
- **Title**: Clear, descriptive, follows commit convention
- **Description**: Comprehensive summary with context
- **Testing**: Evidence of functionality verification  
- **Checklist**: Standard quality gates completed
- **Agent Usage**: Document which specialized agents were used

#### Review Process
1. **Automated Checks**: CI/CD pipeline runs linting, type-checking
2. **Code Review**: Team member reviews changes
3. **Testing**: Functional testing in staging environment
4. **Approval**: Required approvals based on change scope

### Release Management

#### Release Process
1. **Create Release Branch**
   ```bash
   git checkout develop
   git pull origin develop
   git checkout -b release/v1.2.0
   ```

2. **Prepare Release**
   - Update version numbers
   - Generate changelog
   - Update documentation
   - Run full test suite

3. **Deploy to Staging**
   ```bash
   # Deploy to staging environment
   npm run deploy:staging
   ```

4. **Create Release PR**
   ```bash
   gh pr create --base main --title "Release v1.2.0" --body "Release notes..."
   ```

5. **Production Deployment**
   - Merge to main triggers auto-deployment
   - Monitor production metrics
   - Create GitHub release with tags

#### Hotfix Process
```bash
# For critical production issues
git checkout main
git pull origin main
git checkout -b hotfix/critical-security-fix

# Make minimal necessary changes
# Test thoroughly
# Fast-track review process

# Merge to both main and develop
git checkout main
git merge hotfix/critical-security-fix
git checkout develop
git merge hotfix/critical-security-fix
```

### Deployment Environments

#### Environments
- **Development**: Local development with hot reload (port 5177)
- **Staging**: Pre-production testing environment
- **Production**: Live application serving users

#### Environment Variables
```bash
# Development
VITE_SUPABASE_URL=your-dev-url
VITE_SUPABASE_ANON_KEY=your-dev-key
VITE_OPENAI_API_KEY=your-openai-key
VITE_TEMPSTICK_API_KEY=your-tempstick-key

# Production (secure secrets management)
# Use Vercel environment variables or similar
TEMPSTICK_API_KEY=your-production-tempstick-key
GOOGLE_SHEETS_API_KEY=your-google-sheets-key
SENDGRID_API_KEY=your-email-service-key
```

### Quality Gates

#### Pre-Commit Hooks
```bash
# Install pre-commit hooks
npm run prepare

# Hooks run automatically:
# - ESLint fixes
# - Prettier formatting
# - TypeScript type checking
# - Test suite (when configured)
```

#### CI/CD Pipeline
1. **Linting**: ESLint with auto-fix
2. **Type Checking**: TypeScript compiler
3. **Testing**: Jest/Vitest test suite
4. **Build**: Production build verification
5. **Security**: Dependency vulnerability scan
6. **Performance**: Bundle size analysis

### Troubleshooting Git Issues

#### Common Scenarios
```bash
# Undo last commit (keep changes)
git reset HEAD~1

# Undo last commit (discard changes)
git reset --hard HEAD~1

# Fix merge conflicts
git status # see conflicted files
# Edit files to resolve conflicts
git add .
git commit -m "resolve merge conflicts"

# Interactive rebase to clean up history
git rebase -i HEAD~3

# Push force safely
git push --force-with-lease origin branch-name
```

#### Emergency Procedures
```bash
# Revert problematic commit on main
git checkout main
git revert <commit-hash>
git push origin main

# Emergency rollback
git checkout main
git reset --hard <last-good-commit>
git push --force origin main  # Use with extreme caution
```

## 📊 Monitoring & Analytics

### Performance Metrics
- **Voice Processing**: Use `ai-voice-processing-agent` for optimization
- **Database Queries**: Use `supabase-seafood-db-architect` for tuning
- **User Experience**: Use `performance-devops-specialist` for monitoring
- **Compliance**: Use `seafood-compliance-engineer` for audit metrics

## 🚨 Error Handling & Debugging

### Error Handling Best Practices
```typescript
// Create custom error types for domain-specific errors
export class VoiceProcessingError extends Error {
  constructor(message: string, public readonly code: string) {
    super(message);
    this.name = 'VoiceProcessingError';
  }
}

// Use error boundaries for component error handling
export class ErrorBoundary extends Component<ErrorBoundaryProps, ErrorBoundaryState> {
  // Implementation with proper error logging
}

// Handle async errors properly
const handleAsyncOperation = async () => {
  try {
    const result = await riskyOperation();
    return result;
  } catch (error) {
    console.error('Operation failed:', error);
    showUserFriendlyMessage('Operation failed. Please try again.');
    throw error; // Re-throw if needed for upper layers
  }
};
```

### Debugging Tools & Techniques
```bash
# Debug with React DevTools
npm install -g react-devtools

# TypeScript errors in detail
npx tsc --noEmit --listFiles

# Bundle analysis
npm run build
npx webpack-bundle-analyzer dist/

# Performance profiling
# Use React DevTools Profiler in browser
```

### Logging Strategy
```typescript
// Structured logging for debugging
const logger = {
  debug: (message: string, context?: object) => {
    if (process.env.NODE_ENV === 'development') {
      console.log(`🐛 [DEBUG] ${message}`, context);
    }
  },
  error: (message: string, error?: Error) => {
    console.error(`❌ [ERROR] ${message}`, error);
  },
  info: (message: string, context?: object) => {
    console.info(`ℹ️ [INFO] ${message}`, context);
  }
};
```

## ⚠️ Critical Reminders

### Before Making Changes
1. **Never assume or guess** - When in doubt, ask for clarification
2. **Identify the right specialist** using the agent selection guide
3. **Check current technical debt** priorities
4. **Verify environment setup** (port 5177, required env vars)
5. **Consider compliance impact** for any data changes
6. **Test your changes** - No feature is complete without testing

### Quality Gates
- **Code Quality**: All changes must pass through `code-quality-typescript-agent`
- **Security Review**: Authentication/data changes require `security-audit-specialist` review
- **Testing**: New features require `react-testing-architect` review
- **Performance**: Production changes need `performance-devops-specialist` approval
- **Database**: Schema changes require `supabase-seafood-db-architect` review
- **Compliance**: Industry features need `seafood-compliance-engineer` validation

### Documentation Requirements
- **Update CLAUDE.md** when adding new patterns or dependencies
- **Component documentation** - Add JSDoc comments to public interfaces
- **README updates** - Keep setup and usage instructions current
- **Type definitions** - Export and document complex types

### Common Delegation Examples
```typescript
// Multi-agent coordination
"I need to optimize voice processing and fix TypeScript errors"
→ Use project-coordinator-agent

// Project status and priorities
"What should I work on next? What's blocking other work?"
→ Use project-coordinator-agent

// Dependency conflicts
"Should I work on HACCP compliance or fix the import system first?"
→ Use project-coordinator-agent

// Cross-domain issues
"Voice input fails during CSV import - which agents should handle this?"
→ Use project-coordinator-agent

// Voice processing issues
"The voice input misunderstands 'Dungeness crab' as 'dangerous grab'"
→ Use ai-voice-processing-agent

// Type safety issues  
"I have 53 ESLint errors and lots of 'any' types"
→ Use code-quality-typescript-agent

// Security concerns
"I'm worried about data leakage between users in our RLS policies"
→ Use security-audit-specialist

// Performance issues
"The CSV import times out on large files"
→ Use import-export-data-processor

// Database issues
"Inventory queries are slow on the dashboard"
→ Use supabase-seafood-db-architect

// Testing needs
"I need comprehensive tests for the voice feature"
→ Use react-testing-architect

// Compliance requirements
"We need HACCP temperature monitoring"
→ Use seafood-compliance-engineer

// Production deployment
"Ready to deploy but need monitoring setup"
→ Use performance-devops-specialist

// Temperature monitoring issues
"TempStick sensors are offline and we need real-time alerts"
→ Use supabase-seafood-db-architect

// Temperature compliance issues
"We need automated HACCP temperature violation reports"
→ Use seafood-compliance-engineer

// Temperature data export issues
"Need to export temperature data to Excel for audit"
→ Use import-export-data-processor
```

## 🔧 Troubleshooting Guide

### Common Issues & Solutions

#### Database Connection Errors
```bash
# Check if columns exist before querying
node -e "
import { createClient } from '@supabase/supabase-js';
const supabase = createClient(process.env.VITE_SUPABASE_URL, process.env.VITE_SUPABASE_ANON_KEY);
// Test query here
"
```

#### Events Not Showing in Calendar
1. **Check browser console** for query errors (400 responses)
2. **Verify date range** - events outside calendar view won't show
3. **Check event type filters** - ensure event types are enabled
4. **Reload calendar** - check if `reloadKey` is incrementing after form submission

#### Form Submission Issues
1. **Product not found errors** - Check if Products table has data
2. **RLS policy blocks** - May need authentication or service role key
3. **Missing columns** - Voice features require database migrations
4. **Timing issues** - `onSuccess` callback timing affects calendar updates

#### TypeScript Errors
1. **Missing columns** - Update types to match actual database schema  
2. **Import/export mismatches** - Check default vs named exports
3. **Test file extensions** - Use `.tsx` for files with JSX, `.ts` for pure TypeScript

### Development Workflow
```bash
# Before starting work
npm run type-check  # Fix TypeScript errors first
npm run lint        # Check code quality
npm run dev         # Start dev server (port 5177)

# When debugging calendar
# 1. Open browser console
# 2. Look for "HACCPCalendar: Loading events" messages
# 3. Check date ranges and event filtering
# 4. Verify reloadKey increments after form submission
```

### Database Debugging
```bash
# Check table structure
npx supabase db inspect --table inventory_events

# Check recent events
node debug-events.js

# Check products
node -e "console.log(await supabase.from('Products').select('*').limit(5))"
```

## 📚 Resources

### Documentation
- **Supabase**: https://supabase.com/docs
- **React Testing Library**: https://testing-library.com/
- **OpenAI API**: https://platform.openai.com/docs
- **Vite**: https://vitejs.dev/guide/
- **TypeScript**: https://www.typescriptlang.org/docs/

### Industry Standards
- **GDST**: https://www.gdst.org/
- **HACCP**: https://www.fda.gov/food/hazard-analysis-critical-control-point-haccp

---

**Remember**: This CLAUDE.md serves as the main coordinator. Always delegate to the appropriate specialist agent for domain-specific tasks. The specialists have deep expertise and context that will produce better results than general guidance.

_This document coordinates the specialist agent team. Update it when adding new agents or changing delegation patterns._[byterover-mcp]

# important 
always use byterover-retrieve-knowledge tool to get the related context before any tasks 
always use byterover-store-knowledge to store all the critical informations after sucessful tasks
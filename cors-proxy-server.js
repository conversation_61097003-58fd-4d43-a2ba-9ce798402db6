#!/usr/bin/env node

/**
 * Simple CORS Proxy Server for TempStick API
 * Bypasses browser CORS restrictions for development testing
 */

import express from 'express'
import cors from 'cors'
import fetch from 'node-fetch'
import dotenv from 'dotenv'

dotenv.config()

const app = express()
const PORT = 3001

// Enable CORS for all routes
app.use(cors({
  origin: ['http://localhost:5177', 'http://localhost:3000'],
  methods: ['GET', 'POST', 'PUT', 'DELETE'],
  allowedHeaders: ['Content-Type', 'Authorization', 'X-API-KEY', 'x-api-version']
}))

app.use(express.json())

const TEMPSTICK_API_BASE = 'https://tempstickapi.com/api/v1'

// Health check endpoint
app.get('/health', (req, res) => {
  res.json({ status: 'ok', timestamp: new Date().toISOString() })
})

// Proxy middleware - fixed route pattern
app.use('/api/v1', async (req, res) => {
  try {
    const apiKey = process.env.VITE_TEMPSTICK_API_KEY
    
    if (!apiKey) {
      return res.status(400).json({
        error: 'TempStick API key not configured',
        message: 'Set VITE_TEMPSTICK_API_KEY in your .env file'
      })
    }

    // Build the target URL
    const targetPath = req.path.replace('/api/v1', '')
    const queryString = req.url.includes('?') ? req.url.split('?')[1] : ''
    const targetUrl = `${TEMPSTICK_API_BASE}${targetPath}${queryString ? '?' + queryString : ''}`
    
    console.log(`🌡️  Proxying: ${req.method} ${targetUrl}`)
    
    // Forward the request to TempStick API
    const response = await fetch(targetUrl, {
      method: req.method,
      headers: {
        'X-API-KEY': apiKey,
        'User-Agent': '' // Empty user agent as recommended by TempStick docs
      },
      body: req.method !== 'GET' ? JSON.stringify(req.body) : undefined
    })

    const data = await response.text()
    
    // Set response headers
    res.status(response.status)
    res.set('Content-Type', response.headers.get('content-type') || 'application/json')
    
    if (response.ok) {
      console.log(`✅ Success: ${response.status} ${response.statusText}`)
      try {
        const jsonData = JSON.parse(data)
        res.json(jsonData)
      } catch (e) {
        res.send(data)
      }
    } else {
      console.log(`❌ Error: ${response.status} ${response.statusText}`)
      console.log(`Response: ${data}`)
      
      res.json({
        error: 'TempStick API Error',
        status: response.status,
        message: data,
        url: targetUrl
      })
    }

  } catch (error) {
    console.error('Proxy error:', error)
    res.status(500).json({
      error: 'Proxy Server Error',
      message: error.message
    })
  }
})

// Health check endpoint
app.get('/health', (req, res) => {
  res.json({
    status: 'healthy',
    timestamp: new Date().toISOString(),
    apiKeyConfigured: !!process.env.VITE_TEMPSTICK_API_KEY
  })
})

// Start the server
app.listen(PORT, () => {
  console.log('🚀 CORS Proxy Server Started')
  console.log('================================')
  console.log(`📍 Listening on: http://localhost:${PORT}`)
  console.log(`🔑 API Key: ${process.env.VITE_TEMPSTICK_API_KEY ? '✅ Configured' : '❌ Missing'}`)
  console.log(`🌡️  Proxying to: ${TEMPSTICK_API_BASE}`)
  console.log('================================')
  console.log('')
  console.log('Usage:')
  console.log(`GET http://localhost:${PORT}/api/v1/sensors`)
  console.log(`GET http://localhost:${PORT}/api/v1/readings/recent`)
  console.log(`GET http://localhost:${PORT}/health`)
  console.log('')
  console.log('Now update your TempStick service to use:')
  console.log(`http://localhost:${PORT}/api/v1 as the base URL`)
})